package com.hellofresh.cif.sqr

import InfraPreparation.getMigratedDataSource
import KafkaInfraPreparation
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.lib.kafka.serde.serde
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.safetystock.Configuration
import com.hellofresh.cif.safetystock.SafetyStockConfiguration.Companion.DEFAULT_SKU_RISK_RATING
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository.Companion.objectMapper
import com.hellofresh.cif.safetystocklib.schema.tables.records.SafetyStocksRecord
import com.hellofresh.cif.sku_inputs_lib.schema.Tables
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.skuinput.service.SkuInputService
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationConfigRepository
import com.hellofresh.cif.sqr.repository.WeeklyCalculationsRepository
import com.hellofresh.cif.sqr.schema.Tables.CALCULATION
import com.hellofresh.cif.sqr.schema.Tables.DC_CONFIG
import com.hellofresh.cif.sqr.schema.Tables.DC_CONFIG_WEIGHT_VIEW
import com.hellofresh.cif.sqr.schema.Tables.SKU_SPECIFICATION
import com.hellofresh.cif.sqr.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sqr.schema.Tables.SQR_SHORT_SHELF_LIFE
import com.hellofresh.cif.sqr.schema.Tables.SUPPLY_QUANTITY_RECOMMENDATION
import com.hellofresh.cif.sqr.schema.enums.Uom
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.sqr.schema.tables.GoodsReceivedNote.GOODS_RECEIVED_NOTE
import com.hellofresh.cif.sqr.schema.tables.records.CalculationRecord
import com.hellofresh.cif.sqr.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.sqr.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.sqr.service.BNLBufferCalculationService
import com.hellofresh.cif.sqr.service.SQR_TOPIC_NAME
import com.hellofresh.cif.sqr.shortshelflife.SSL_SQR_TOPIC_NAME
import com.hellofresh.cif.sqr.shortshelflife.repository.DailyCalculationsRepository
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeRepository
import com.hellofresh.cif.sqrlib.schema.Tables.SQR_SHORT_SHELF_LIFE_CONF
import com.hellofresh.cif.sqrlib.schema.Tables.SUPPLY_QUANTITY_RECOMMENDATION_CONF
import com.hellofresh.cif.sqrlib.schema.tables.records.SupplyQuantityRecommendationConfRecord
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationVal
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import com.hellofresh.sku.models.SkuSpecification
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.mockk
import java.io.File
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.ZERO
import java.nio.file.Files
import java.time.DayOfWeek
import java.time.DayOfWeek.TUESDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import org.apache.kafka.clients.admin.NewTopic
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.jooq.JSONB
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.testcontainers.containers.KafkaContainer

open class FunctionalTest {

    @AfterEach
    fun afterEach() {
        dsl.deleteFrom(CALCULATION).execute()
        dsl.deleteFrom(SUPPLY_QUANTITY_RECOMMENDATION_CONF).execute()
        dsl.deleteFrom(SUPPLY_QUANTITY_RECOMMENDATION).execute()
        dsl.deleteFrom(SQR_SHORT_SHELF_LIFE).execute()
        dsl.deleteFrom(SQR_SHORT_SHELF_LIFE_CONF).execute()
        dsl.deleteFrom(DC_CONFIG).execute()
        dsl.deleteFrom(SKU_SPECIFICATION).execute()
        refreshDcConfigWeightView()
        refreshSkuView()
    }

    fun insertSKU(skuID: UUID = UUID.randomUUID(), skuCategory: String = "PRO"): SkuSpecificationRecord {
        val skuSpecRecord = SkuSpecificationRecord().apply {
            id = skuID
            parentId = null
            category = skuCategory
            code = UUID.randomUUID().toString()
            name = "Name"
            acceptableCodeLife = 0
            coolingType = ""
            packaging = ""
            market = "market"
            uom = Uom.UOM_LBS
        }
        dsl.batchInsert(skuSpecRecord).execute()
        refreshSkuView()
        return skuSpecRecord
    }

    fun refreshSkuView() =
        dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()

    @SuppressWarnings("LongParameterList")
    fun insertCalculation(
        distributionCenterConfiguration: DistributionCenterConfiguration,
        date: LocalDate,
        skuId: UUID,
        openingStock: BigDecimal = BigDecimal(100),
        demand: BigDecimal = BigDecimal(1000),
        unusable: BigDecimal = BigDecimal.ZERO,
        stockUpdate: BigDecimal? = null,
        uom: Uom = UOM_LITRE
    ): CalculationRecord = insertCalculation(
        distributionCenterConfiguration.dcCode,
        distributionCenterConfiguration.productionStart,
        date,
        skuId,
        openingStock,
        demand,
        unusable,
        stockUpdate,
        uom,
    )

    @SuppressWarnings("LongParameterList")
    fun insertCalculation(
        dcCode: String,
        productionStart: DayOfWeek,
        date: LocalDate,
        skuId: UUID,
        openingStock: BigDecimal = BigDecimal(100),
        demand: BigDecimal = BigDecimal(1000),
        unusable: BigDecimal = BigDecimal.ZERO,
        stockUpdate: BigDecimal? = null,
        uom: Uom = UOM_LITRE,
        incomingPos: BigDecimal = BigDecimal.ZERO,
        actualInbound: BigDecimal = BigDecimal.ZERO
    ): CalculationRecord =
        CalculationRecord().apply {
            this.dcCode = dcCode
            this.cskuId = skuId
            this.date = date
            this.productionWeek = DcWeek(date, productionStart).toString()
            this.openingStock = openingStock
            this.expired = unusable
            this.demanded = demand
            this.stockUpdate = stockUpdate
            this.uom = uom
            this.expectedInbound = incomingPos
            this.expectedInboundPo = "2508WM027948,2508WM027949"
            this.actualInbound = actualInbound
            this.actualInboundPo = "2508WM027948,2508WM027949"
        }.also {
            dsl.batchInsert(it).execute()
            refreshDcConfigWeightView()
        }

    @SuppressWarnings("LongParameterList")
    fun insertSQRConfiguration(
        dcCode: String,
        productionStart: DayOfWeek,
        date: LocalDate,
        skuId: UUID,
        recommended: Boolean,
        multiWeek: Boolean,
    ) =
        SupplyQuantityRecommendationConfRecord().apply {
            this.dcCode = dcCode
            this.skuId = skuId
            this.week = DcWeek(date, productionStart).toString()
            this.recommendationEnabled = recommended
            this.multiWeekEnabled = multiWeek
        }.also {
            dsl.batchInsert(it).execute()
        }

    fun insertSafetyStock(
        dcCode: String,
        productionStart: DayOfWeek,
        date: LocalDate,
        skuId: UUID,
        safetyStock: Long,
    ) =
        SafetyStocksRecord().apply {
            this.dcCode = dcCode
            this.skuId = skuId
            this.week = DcWeek(date, productionStart).toString()
            this.safetyStock = safetyStock
            this.configuration =
                JSONB.jsonb(objectMapper.writeValueAsString(Configuration(ONE, DEFAULT_SKU_RISK_RATING, ZERO)))
        }.also {
            dsl.batchInsert(it).execute()
        }

    fun insertDcRecord(dcCode: String, enabled: Boolean = true, market: String = "dach") =
        DcConfigRecord(
            dcCode, market, TUESDAY.name, TUESDAY.name, "UTC", enabled,
            LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null, LocalTime.now()
        ).also {
            dsl.batchInsert(it).execute()
            refreshDcConfigWeightView()
        }

    @Suppress("LongParameterList")
    fun insertGrn(
        dcCode: String = "VE",
        poNumber: String = "2305DH046273",
        poRef: String = "2305DH046273_01",
        skuId: UUID,
        deliveryId: String = UUID.randomUUID().toString(),
        deliveryDate: OffsetDateTime = OffsetDateTime.now(UTC),
        quantity: Long = 9,
        deliveryInfoStatus: DeliveryInfoStatus,
        expiryDate: LocalDate? = null
    ) = GoodsReceivedNoteSku(
        dcCode = dcCode,
        poNumber = poNumber,
        poRef = poRef,
        skuId = skuId,
        deliveryId = deliveryId,
        deliveryDate = deliveryDate,
        quantity = quantity,
        expiryDate = expiryDate,
        grnStatus = deliveryInfoStatus,
    )

    fun insertGoodsReceivedNotes(goodsReceivedNotes: Set<GoodsReceivedNoteSku>) {
        goodsReceivedNotes
            .forEach { grnRecord ->
                with(GOODS_RECEIVED_NOTE) {
                    dsl.insertInto(this).columns(
                        SKU_ID,
                        DC_CODE,
                        DELIVERY_ID,
                        DELIVERY_TIME,
                        QUANTITY,
                        DELIVERY_STATUS,
                        PO_REF,
                        PO_NUMBER,
                        EXPIRY_DATE,
                    ).values(
                        grnRecord.skuId,
                        grnRecord.dcCode,
                        grnRecord.deliveryId,
                        grnRecord.deliveryDate,
                        grnRecord.quantity.toBigDecimal(),
                        grnRecord.grnStatus.name,
                        grnRecord.poRef,
                        grnRecord.poNumber,
                        grnRecord.expiryDate,
                    ).execute()
                }
            }
        refreshView()
    }

    fun insertPurchaseOrderAndRefreshPoInfoView(
        purchaseOrderRecord: PurchaseOrderRecord
    ) = persistPurchaseOrderAndRefreshPoInfoView(kotlin.collections.listOf(purchaseOrderRecord))

    fun persistPurchaseOrderAndRefreshPoInfoView(
        purchaseOrderRecords: List<PurchaseOrderRecord>
    ) {
        purchaseOrderRecords.forEach {
            persistPurchaseOrder(it)
            persistPurchaseOrderSku(it)
            dsl.query("insert into supplier(id, name) values(?,?)", it.supplierId, it.supplierName).execute()
        }
        refreshView()
    }

    private fun persistPurchaseOrderSku(it: PurchaseOrderRecord) {
        it.skus.forEach { sku ->
            dsl.query(
                "insert into purchase_order_sku(po_number, sku_id, quantity) values(?,?,?)",
                it.poNumber,
                sku.skuId,
                sku.quantity,
                sku.quantity,
            ).execute()
        }
    }

    private fun persistPurchaseOrder(it: PurchaseOrderRecord) {
        dsl.query(
            "insert into purchase_order(po_ref, po_id, po_number, dc_code, " +
                "expected_arrival_start_time,expected_arrival_end_time, supplier_id, supplier_name) " +
                "values(?,?,?,?,(?::timestamptz),(?::timestamptz),?,?)",
            it.poRef,
            it.poId,
            it.poNumber,
            it.dcCode,
            it.expectedDateStartTime,
            it.expectedDateEndTime,
            it.supplierId,
            it.poSupplierName,
        ).execute()
    }

    private fun refreshView() = dsl.query("refresh materialized view PURCHASE_ORDERS_VIEW").execute()

    fun refreshDcConfigWeightView() = dsl.query(
        "refresh materialized view ${DC_CONFIG_WEIGHT_VIEW.name}",
    ).execute()

    fun readFileContent(fileName: String): ByteArray = with(this::class.java.classLoader) {
        File(getResource(fileName)!!.toURI())
    }.let {
        Files.readAllBytes(it.toPath())
    }

    fun insertSkuSpecification(skuCodes: List<SkuSpecification>) {
        skuCodes.forEach { skuItem ->
            dsl.insertInto(
                Tables.SKU_SPECIFICATION,
                Tables.SKU_SPECIFICATION.ID,
                Tables.SKU_SPECIFICATION.PARENT_ID,
                Tables.SKU_SPECIFICATION.CATEGORY,
                Tables.SKU_SPECIFICATION.CODE,
                Tables.SKU_SPECIFICATION.NAME,
                Tables.SKU_SPECIFICATION.COOLING_TYPE,
                Tables.SKU_SPECIFICATION.PACKAGING,
                Tables.SKU_SPECIFICATION.ACCEPTABLE_CODE_LIFE,
                Tables.SKU_SPECIFICATION.MARKET,
            )
                .values(
                    UUID.randomUUID(),
                    UUID.randomUUID(),
                    skuItem.category,
                    skuItem.skuCode,
                    skuItem.name,
                    skuItem.coolingType,
                    skuItem.packaging,
                    skuItem.acceptableCodeLife,
                    skuItem.market,
                )
                .execute()
        }.also {
            refreshSkuView()
        }
    }

    data class GoodsReceivedNoteSku(
        val dcCode: String,
        val poNumber: String,
        val poRef: String,
        val skuId: UUID,
        val deliveryId: String,
        val deliveryDate: OffsetDateTime,
        val quantity: Long,
        val expiryDate: LocalDate?,
        val grnStatus: DeliveryInfoStatus
    )

    data class PurchaseOrderRecord(
        val poRef: String,
        val poId: UUID = UUID.randomUUID(),
        val poNumber: String = UUID.randomUUID().toString(),
        val dcCode: String,
        val expectedDateStartTime: OffsetDateTime = OffsetDateTime.now(UTC),
        val expectedDateEndTime: OffsetDateTime = OffsetDateTime.now(UTC).plusHours(2),
        val supplierId: UUID = UUID.randomUUID(),
        val supplierName: String = UUID.randomUUID().toString(),
        val poSupplierName: String? = null,
        val skus: List<PurchaseOrderSkuRecord>
    ) {
        constructor(
            poRef: String,
            poId: UUID = UUID.randomUUID(),
            poNumber: String = UUID.randomUUID().toString(),
            dcCode: String,
            expectedDateStartTime: OffsetDateTime = OffsetDateTime.now(UTC),
            expectedDateEndTime: OffsetDateTime = OffsetDateTime.now(UTC).plusHours(2),
            supplierId: UUID = UUID.randomUUID(),
            supplierName: String = UUID.randomUUID().toString(),
            skuId: UUID,
            quantity: Long,
            poSupplierName: String? = null,
        ) :
            this(
                poRef,
                poId,
                poNumber,
                dcCode,
                expectedDateStartTime,
                expectedDateEndTime,
                supplierId,
                supplierName,
                poSupplierName,
                listOf(PurchaseOrderSkuRecord(skuId, quantity)),
            )
    }

    data class PurchaseOrderSkuRecord(
        val skuId: UUID,
        val quantity: Long,
    )

    companion object {
        lateinit var dsl: MetricsDSLContext
        lateinit var weeklyCalculationsRepository: WeeklyCalculationsRepository
        lateinit var supplyQuantityRecommendationConfigRepository: SupplyQuantityRecommendationConfigRepository
        lateinit var dailyCalculationsRepository: DailyCalculationsRepository
        lateinit var sqsShelfLifeRepository: SQRShortShelfLifeRepository
        lateinit var dcRepository: DcRepositoryImpl
        lateinit var dcConfigService: DcConfigService

        lateinit var producerConfig: Map<String, String>
        lateinit var expectedSqrTopicConsumer:
            KafkaConsumer<SupplyQuantityRecommendationKey, SupplyQuantityRecommendationVal>

        lateinit var expectedSqrSSLTopicConsumer:
            KafkaConsumer<SupplyQuantityRecommendationDailyKey, SupplyQuantityRecommendationDailyVal>

        private lateinit var kafka: KafkaContainer

        private val dataSource = getMigratedDataSource(nestedFolderCount = 1)
        val meterRegistry = SimpleMeterRegistry()
        val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
        lateinit var s3Importer: S3Importer
        lateinit var bufferCalculationService: BNLBufferCalculationService
        private lateinit var skuInputDataRepository: SkuInputDataRepository
        private lateinit var skuInputService: SkuInputService
        private lateinit var sqrShortShelfLifeConfRepository: SQRShortShelfLifeConfRepository

        @BeforeAll
        @JvmStatic
        fun init() {
            initKafka()
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newFixedThreadPool(2))
                }
            dsl = DSL.using(dbConfiguration).withMetrics(meterRegistry)

            val bnlDcConfig = DistributionCenterConfiguration(
                dcCode = "DH",
                productionStart = LocalDate.now().dayOfWeek,
                cleardown = LocalDate.now().dayOfWeek,
                market = "BENELUXFR",
                zoneId = UTC,
                enabled = true,
                wmsType = WmsSystem.WMS_SYSTEM_FCMS,
            )

            weeklyCalculationsRepository = WeeklyCalculationsRepository(dsl)
            supplyQuantityRecommendationConfigRepository = SupplyQuantityRecommendationConfigRepository(dsl)
            dailyCalculationsRepository = DailyCalculationsRepository(dsl)
            dcRepository = DcRepositoryImpl(dsl)
            dcConfigService = DcConfigService(meterRegistry, repo = {
                listOf(bnlDcConfig)
            })

            s3Importer = mockk<S3Importer>()
            skuInputDataRepository = SkuInputDataRepositoryImpl(
                dsl,
                dcConfigService
            )
            skuInputService = SkuInputService(skuInputDataRepository)
            sqrShortShelfLifeConfRepository = SQRShortShelfLifeConfRepository(dsl)
            bufferCalculationService =
                BNLBufferCalculationService(s3Importer, sqrShortShelfLifeConfRepository, skuInputService)
        }

        private fun initKafka() {
            kafka = KafkaInfraPreparation.startKafkaAndCreateTopics(
                listOf(
                    NewTopic(SQR_TOPIC_NAME, 6, 1),
                    NewTopic(SSL_SQR_TOPIC_NAME, 6, 1)
                ),
            )

            expectedSqrTopicConsumer = KafkaInfraPreparation.createKafkaConsumer(
                "randomSQRJobTest",
                SQR_TOPIC_NAME,
                serde<SupplyQuantityRecommendationKey>().deserializer(),
                serde<SupplyQuantityRecommendationVal>().deserializer(),
            )

            expectedSqrSSLTopicConsumer = KafkaInfraPreparation.createKafkaConsumer(
                "randomSQRSSLJobTest",
                SSL_SQR_TOPIC_NAME,
                serde<SupplyQuantityRecommendationDailyKey>().deserializer(),
                serde<SupplyQuantityRecommendationDailyVal>().deserializer(),
            )

            producerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                )
        }
    }
}
