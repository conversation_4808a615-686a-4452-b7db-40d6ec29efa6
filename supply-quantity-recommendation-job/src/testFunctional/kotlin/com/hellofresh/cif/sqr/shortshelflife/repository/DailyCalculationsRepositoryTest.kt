package com.hellofresh.cif.sqr.shortshelflife.repository

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.sqr.FunctionalTest
import com.hellofresh.cif.sqr.schema.enums.Uom
import com.hellofresh.cif.sqr.schema.tables.records.CalculationRecord
import java.math.BigDecimal
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class DailyCalculationsRepositoryTest : FunctionalTest() {

    @Test
    fun `daily calculations are fetched from latest production date`() {
        val distributionCenterConfiguration1 = DistributionCenterConfiguration.Companion.default()
            .copy(
                productionStart = FRIDAY,
                zoneId = UTC,
            )
        val distributionCenterConfiguration2 = DistributionCenterConfiguration.Companion.default().copy(
            dcCode = "DC",
            productionStart = WEDNESDAY,
            zoneId = UTC,
        )

        val today = LocalDate.now(UTC)
        val sku1 = insertSKU()
        val skuId1 = sku1.id
        val uom = Uom.UOM_GAL

        val calc1Day1 = insertCalculation(
            distributionCenterConfiguration1,
            distributionCenterConfiguration1.getProductionStartDate(today),
            skuId1,
            openingStock = BigDecimal(100),
            demand = BigDecimal(10),
            unusable = BigDecimal(11),
            stockUpdate = BigDecimal(1),
            uom,
        )
        val calc1Day2 = insertCalculation(
            distributionCenterConfiguration1,
            distributionCenterConfiguration1.getProductionStartDate(today).plusDays(1),
            skuId1,
            openingStock = BigDecimal(1000),
            demand = BigDecimal(50),
            unusable = BigDecimal.ZERO,
            stockUpdate = null,
            uom,
        )

        val sku2 = insertSKU()
        val skuId2 = sku2.id
        val calc2Day1 = insertCalculation(
            distributionCenterConfiguration2,
            distributionCenterConfiguration2.getLatestProductionStart(),
            skuId2,
            openingStock = BigDecimal(400),
            demand = BigDecimal(500),
            unusable = BigDecimal(40),
            stockUpdate = BigDecimal(50),
            uom,
        )

        // OTHER DC
        insertCalculation(
            DistributionCenterConfiguration.Companion.default("XX"),
            today,
            skuId1,
            openingStock = BigDecimal(400),
            demand = BigDecimal(500),
            uom = uom,
        )

        val dailyCalculations =
            runBlocking {
                dailyCalculationsRepository.fetchDailyCalculations(
                    setOf(distributionCenterConfiguration1, distributionCenterConfiguration2),
                ).toList().flatten()
            }

        assertEquals(3, dailyCalculations.size)

        assertDailyCalculation(
            calc1Day1,
            dailyCalculations.first { it.date == calc1Day1.date && it.dcCode == calc1Day1.dcCode },
        )
        assertDailyCalculation(
            calc1Day2,
            dailyCalculations.first { it.date == calc1Day2.date && it.dcCode == calc1Day2.dcCode },
        )
        assertDailyCalculation(
            calc2Day1,
            dailyCalculations.first { it.date == calc2Day1.date && it.dcCode == calc2Day1.dcCode },
        )
    }

    @Test
    fun `all calculations are fetched in seek query`() {
        val distributionCenterConfiguration = DistributionCenterConfiguration.Companion.default()
            .copy(productionStart = FRIDAY, zoneId = UTC)

        val today = LocalDate.now(UTC)
        val sku = insertSKU()

        val totalCalculations = SEEK_SIZE + 10L
        for (i in 0..<totalCalculations) {
            val date = today.plusDays(i)
            insertCalculation(distributionCenterConfiguration, date, sku.id)
        }

        val dates =
            runBlocking {
                dailyCalculationsRepository.fetchDailyCalculations(
                    setOf(distributionCenterConfiguration),
                ).map { it.map { it.date } }.toList().flatten()
            }

        assertEquals(totalCalculations.toInt(), dates.size)
        assertEquals(today, dates.min())
        assertEquals(today.plusDays(totalCalculations - 1), dates.max())
    }

    @Test
    fun `return empty daily calculations when dc list is empty`() {
        assertTrue(runBlocking { dailyCalculationsRepository.fetchDailyCalculations(emptySet()).toList() }.isEmpty())
    }

    private fun assertDailyCalculation(
        calculationRecord: CalculationRecord,
        dailyCalculation: DailyCalculation,
    ) {
        with(calculationRecord) {
            assertEquals(dcCode, dailyCalculation.dcCode)
            assertEquals(date, dailyCalculation.date)
            assertEquals(cskuId, dailyCalculation.skuId)
            assertEquals(openingStock, dailyCalculation.openingStock.getValue())
            assertEquals(expired, dailyCalculation.unusable.getValue())
            assertEquals(demanded, dailyCalculation.consumption.getValue())
            assertEquals(stockUpdate, dailyCalculation.stockUpdate?.getValue())
            assertEquals(uom.name, dailyCalculation.uom.name)
        }
    }
}
