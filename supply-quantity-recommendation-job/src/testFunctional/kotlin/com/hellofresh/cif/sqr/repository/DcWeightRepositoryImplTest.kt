package com.hellofresh.cif.sqr.repository

import com.hellofresh.cif.sqr.FunctionalTest
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneId
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class DcWeightRepositoryImplTest : FunctionalTest() {

    @Test
    fun `fetch dc return just enabled dcs`() {
        val enabledDcs = (0..5).map {
            insertDcRecord(UUID.randomUUID().toString(), true)
        }

        val disabledDcs = (0..2).map {
            insertDcRecord(UUID.randomUUID().toString(), false)
        }

        val fetchDcsWithWeight = runBlocking { DcWeightRepositoryImpl(dsl).fetchDcsWithWeight() }

        assertEquals(enabledDcs.map { it.dcCode }, fetchDcsWithWeight.map { it.dcCode })
        fetchDcsWithWeight.forEach { assertEquals(0, it.weight) }

        with(fetchDcsWithWeight.map { it.dcCode }.toSet()) {
            disabledDcs.forEach { assertTrue(!contains(it.dcCode)) }
        }
    }

    @Test
    fun `fetch dc return dcs with weight using calculations`() {
        val dcs = (0..5L).associate {
            insertDcRecord(UUID.randomUUID().toString(), true) to it + 1L
        }.onEach { (dcRecord, count) ->
            (0 until count).map {
                insertCalculation(
                    dcRecord.dcCode,
                    DayOfWeek.valueOf(dcRecord.productionStart),
                    LocalDate.now(ZoneId.of(dcRecord.zoneId)).plusWeeks(1),
                    UUID.randomUUID(),
                )
            }
        }

        val fetchDcsWithWeight = runBlocking { DcWeightRepositoryImpl(dsl).fetchDcsWithWeight() }

        dcs.forEach { (dcRecord, count) ->
            with(fetchDcsWithWeight.first { it.dcCode == dcRecord.dcCode }) {
                assertEquals(count, this.weight)
            }
        }
    }
}
