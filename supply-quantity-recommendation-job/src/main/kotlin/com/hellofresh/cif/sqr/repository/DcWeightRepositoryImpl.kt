package com.hellofresh.cif.sqr.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.lib.dcbalancer.DcWeight
import com.hellofresh.cif.lib.dcbalancer.DcWeightRepository
import com.hellofresh.cif.sqr.schema.Tables.DC_CONFIG_WEIGHT_VIEW
import kotlinx.coroutines.future.await

class DcWeightRepositoryImpl(private val metricsDSLContext: MetricsDSLContext) : DcWeightRepository {

    override suspend fun fetchDcsWithWeight(): Set<DcWeight> =
        metricsDSLContext.withTagName("dcs-with-weight")
            .selectFrom(DC_CONFIG_WEIGHT_VIEW)
            .fetchAsync()
            .thenApply { r ->
                r.map {
                    DcWeight(it.dcCode, it.weight)
                }.toSet()
            }.await()
}
