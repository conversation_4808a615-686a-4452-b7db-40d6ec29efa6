package com.hellofresh.cif.sqr.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationRepository.mapToSkuQuantity
import com.hellofresh.cif.sqr.schema.Tables.CALCULATION
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL

@Suppress("LongMethod")
class WeeklyCalculationsRepository(private val dslContext: MetricsDSLContext) {

    suspend fun fetchWeeklyCalculations(distributionCenters: Set<DistributionCenterConfiguration>): List<WeeklyCalculation> {
        if (distributionCenters.isEmpty()) {
            return emptyList()
        }

        val productionStartToDcs = distributionCenters.groupBy {
            it.getLatestProductionStart()
        }

        val aggregatedDemand = DSL.sum(CALCULATION.DEMANDED).`as`("agg_demand")
        val productionStartDate = DSL.min(CALCULATION.DATE).`as`("prod_start_date")
        val incomingPos = DSL.sum(CALCULATION.EXPECTED_INBOUND).`as`("agg_incoming_pos")
        val inbound = DSL.sum(CALCULATION.ACTUAL_INBOUND).`as`("agg_inbound")
        val unusableStock = DSL.sum(CALCULATION.EXPIRED).`as`("agg_unusable")

        return productionStartToDcs.flatMap { (productionStart, dcs) ->
            val groupSelect =
                dslContext
                    .select(
                        CALCULATION.DC_CODE,
                        CALCULATION.CSKU_ID,
                        CALCULATION.PRODUCTION_WEEK,
                        aggregatedDemand,
                        productionStartDate,
                        incomingPos,
                        inbound,
                        unusableStock,
                    ).from(CALCULATION)
                    .where(
                        CALCULATION.DC_CODE.`in`(dcs.map { it.dcCode })
                            .and(
                                CALCULATION.DATE.ge(productionStart),
                            ),
                    ).groupBy(CALCULATION.DC_CODE, CALCULATION.CSKU_ID, CALCULATION.PRODUCTION_WEEK)
                    .asTable("grouped_calculation")

            dslContext.withTagName("fetch-weekly-calculations-by-dcs")
                .select(
                    CALCULATION.DC_CODE,
                    CALCULATION.CSKU_ID,
                    CALCULATION.PRODUCTION_WEEK,
                    groupSelect.field(aggregatedDemand),
                    groupSelect.field(incomingPos),
                    groupSelect.field(inbound),
                    groupSelect.field(unusableStock),
                    CALCULATION.OPENING_STOCK,
                    CALCULATION.UOM,
                ).from(groupSelect)
                .join(CALCULATION).on(
                    CALCULATION.DC_CODE.eq(groupSelect.field(CALCULATION.DC_CODE))
                        .and(CALCULATION.CSKU_ID.eq(groupSelect.field(CALCULATION.CSKU_ID)))
                        .and(
                            CALCULATION.DATE.eq(groupSelect.field(productionStartDate)),
                        ),
                ).fetchAsync()
                .thenApply { results ->
                    results.map { record ->
                        WeeklyCalculation(
                            record[CALCULATION.DC_CODE],
                            record[CALCULATION.CSKU_ID],
                            record[CALCULATION.PRODUCTION_WEEK],
                            record[aggregatedDemand].mapToSkuQuantity(record[CALCULATION.UOM]),
                            record[incomingPos].mapToSkuQuantity(record[CALCULATION.UOM]),
                            record[inbound].mapToSkuQuantity(record[CALCULATION.UOM]),
                            record[unusableStock].mapToSkuQuantity(record[CALCULATION.UOM]),
                            record[CALCULATION.OPENING_STOCK].mapToSkuQuantity(record[CALCULATION.UOM]),
                        )
                    }
                }.await()
        }
    }
}

data class WeeklyCalculation(
    val dcCode: String,
    val skuId: UUID,
    val week: String,
    val aggregatedDemand: SkuQuantity,
    val incomingPos: SkuQuantity,
    val inbound: SkuQuantity,
    val unusableStock: SkuQuantity,
    val productionStartOpeningStock: SkuQuantity
) {
    val uom: SkuUOM
        get() = productionStartOpeningStock.unitOfMeasure

    companion object
}
