package com.hellofresh.cif.sqr.service

import com.hellofresh.cif.fileconsumer.ProcessFile
import com.hellofresh.cif.fileconsumer.model.FileTypeDescriptor
import com.hellofresh.cif.fileconsumer.model.ProcessFileResult
import com.hellofresh.cif.fileconsumer.model.ViolationHandlersConfig
import com.hellofresh.cif.fileconsumer.violations.ViolationHandlersPerFile
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.skuinput.service.SkuInputService
import com.hellofresh.cif.sqr.model.BNLBufferCalculationParsedFile
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeConf
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import com.hellofresh.cif.sqs.MessageHandlerServiceInterface
import java.math.BigDecimal
import java.time.LocalDate
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging

class BNLBufferCalculationService(
    private val importer: S3Importer,
    private val shortShelfLifeConfRepository: SQRShortShelfLifeConfRepository,
    private val skuInputService: SkuInputService
) :
    MessageHandlerServiceInterface {
    override suspend fun process(s3File: S3File) {
        logger.info("Request to download file from S3: bucket='${s3File.bucket}', key='${s3File.key}'")
        val allBytes = withContext(Dispatchers.IO) {
            importer.fetchObjectContent(s3File.bucket, s3File.key).readAllBytes()
        }

        logger.info("Starting CSV file parsing")
        val content = processContent(allBytes, s3File.key)

        if (content.isNotEmpty()) {
            logger.info("Saving objects to sqr_short_shelf_life_conf: total ${content.size} objects")
            shortShelfLifeConfRepository.insertShortShelfLifeConfigs(content)
        }
    }

    override suspend fun name(): String = "BNL buffer calculation SQS Handler"

    private suspend fun processContent(content: ByteArray, fileName: String): List<SQRShortShelfLifeConf> {
        val file = BNLBufferCalculationFile(
            ProcessFile.processFileContent(
                fileName,
                content,
                listOf(';'),
                FileTypeDescriptor(BNLBufferCalculationParsedFile.columns, ::BNLBufferCalculationParsedFile),
                violationHandlersConfig = ViolationHandlersConfig(
                    preViolationHandlers = ViolationHandlersPerFile.createInstances(),
                ),
            ),
        )

        if (file.processFileResult.hasSevereViolations()) {
            val message = file.processFileResult.violations.joinToString { it.message }
            logger.error(
                "Error while processing the bnl buffer calculation file $fileName, because: $message.",
            )
        } else if (file.processFileResult.violations.isNotEmpty()) {
            val message = file.processFileResult.violations.joinToString { it.message }
            logger.warn(
                "Warning while processing the bnl buffer calculation file $fileName, because: $message.",
            )
        }
        return if (file.processFileResult.run { !hasSevereViolations() && violations.isEmpty() }) {
            file.toSQRShortShelfLifeConf()
        } else {
            emptyList()
        }
    }

    private suspend fun BNLBufferCalculationFile.toSQRShortShelfLifeConf(): List<SQRShortShelfLifeConf> {
        val file = this.processFileResult.parsedFile
        return file?.data?.mapNotNull {
            val dcCode = it[BNLBufferCalculationParsedFile.DC_HEADER]
            val shortSkuSpec = skuInputService.getSkuId(dcCode, it[BNLBufferCalculationParsedFile.SKU_CODE_HEADER])

            shortSkuSpec?.let { skuSpec ->
                SQRShortShelfLifeConf(
                    skuId = skuSpec.id,
                    dcCode = dcCode,
                    date = LocalDate.parse(it[BNLBufferCalculationParsedFile.DATE_HEADER]),
                    bufferPercentage = BigDecimal(0),
                    bufferAdditional = BigDecimal(it[BNLBufferCalculationParsedFile.BUFFER_VALUE_HEADER]),
                    touchlessOrderingEnabled = false
                )
            }
        } ?: emptyList()
    }

    companion object : Logging

    data class BNLBufferCalculationFile(
        val processFileResult: ProcessFileResult
    )
}
