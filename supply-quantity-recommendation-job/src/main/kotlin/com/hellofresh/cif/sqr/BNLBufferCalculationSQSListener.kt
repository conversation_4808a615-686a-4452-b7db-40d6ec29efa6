package com.hellofresh.cif.sqr

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.lib.KrontabScheduler
import com.hellofresh.cif.lib.MeteredJob
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.s3.S3EventMessageParser
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.skuinput.service.SkuInputService
import com.hellofresh.cif.sqr.service.BNLBufferCalculationService
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import com.hellofresh.cif.sqs.SQSClientBuilder
import com.hellofresh.cif.sqs.SQSListener
import com.hellofresh.cif.sqs.SQSMessageProxy
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import software.amazon.awssdk.services.sqs.SqsClient

object BNLBufferCalculationSQSListener {
    private val sqsClient: SqsClient =
        SQSClientBuilder.getSqsClient(getAssumeRoleArn(), "bnl-buffer-calculation-job-session")

    private fun jobTimeMinutes(): Int = ConfigurationLoader.getStringOrFail("job.time_minutes").toInt()
    private fun jobTimeSeconds(): Int? = ConfigurationLoader.getStringIfPresent("job.time_seconds")?.toInt()
    private fun sqsJobTimeSeconds(): Int =
        ConfigurationLoader.getStringOrDefault("sqs.listener.time_seconds", "30").toInt()

    private fun getAssumeRoleArn() = ConfigurationLoader.getStringOrFail("assume.role.arn")
    private fun getBNLBufferCalculationSqsUrl() = ConfigurationLoader.getStringOrFail("aws.sqs.url")

    private fun jobPeriod() =
        jobTimeSeconds()?.let { it to TimeUnit.SECONDS }
            ?: (jobTimeMinutes() to TimeUnit.MINUTES)

    fun launch(
        meterRegistry: HelloFreshMeterRegistry,
        readMetricsDSLContext: MetricsDSLContext,
        readWriteMetricsDSLContext: MetricsDSLContext,
    ) {
        val s3Importer = S3Importer(getAssumeRoleArn(), "bnl-buffer-session")
        val s3EventMessageParser = S3EventMessageParser()
        val shortShelfLifeConfRepository = SQRShortShelfLifeConfRepository(readWriteMetricsDSLContext)
        val dcRepository = DcRepositoryImpl(readMetricsDSLContext)
        val dcConfigService = DcConfigService(meterRegistry, dcRepository)
        val skuInputDataRepository = SkuInputDataRepositoryImpl(readMetricsDSLContext, dcConfigService)
        val skuInputService = SkuInputService(skuInputDataRepository)

        val bnlBufferCalculationService =
            BNLBufferCalculationService(s3Importer, shortShelfLifeConfRepository, skuInputService)

        val bnlBufferCalculationSqsService = SQSMessageProxy(
            bnlBufferCalculationService,
            s3EventMessageParser,
        )

        val bnlBufferCalculationSqsListener = shutdownNeeded {
            SQSListener(
                bnlBufferCalculationSqsService,
                sqsClient,
                getBNLBufferCalculationSqsUrl(),
            )
        }

        val meteredBNLBufferCalculationSqsListenerJob = MeteredJob(
            meterRegistry,
            "bnl-buffer-calculation-notification-sqs-listener-job",
            bnlBufferCalculationSqsListener::run,
        )

        val (_, timeUnit) = jobPeriod()
        shutdownNeeded {
            KrontabScheduler(
                period = sqsJobTimeSeconds(),
                timeUnit = timeUnit,
                executor = Executors.newSingleThreadExecutor(),
            )
        }.schedule {
            meteredBNLBufferCalculationSqsListenerJob.execute()
        }
    }
}
