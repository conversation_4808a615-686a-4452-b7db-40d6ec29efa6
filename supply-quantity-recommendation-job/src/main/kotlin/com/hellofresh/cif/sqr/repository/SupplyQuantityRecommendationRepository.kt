package com.hellofresh.cif.sqr.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sqr.schema.Tables.SUPPLY_QUANTITY_RECOMMENDATION
import com.hellofresh.cif.sqr.schema.enums.Uom
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_GAL
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_KG
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_OZ
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_UNRECOGNIZED
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_UNSPECIFIED
import com.hellofresh.cif.sqr.schema.tables.records.SupplyQuantityRecommendationRecord
import java.math.BigDecimal
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Condition
import org.jooq.TableField
import org.jooq.impl.DSL

object SupplyQuantityRecommendationRepository : Logging {

    fun MetricsDSLContext.upsertSupplyQuantityRecommendations(
        supplyQuantityRecommendations: List<SupplyQuantityRecommendation>
    ): List<SupplyQuantityRecommendation> {
        val records: List<SupplyQuantityRecommendationRecord> = toRecords(supplyQuantityRecommendations)
        val updatedSqrs = this.withTagName("upsert-supply-quantity-recommendations")
            .insertInto(SUPPLY_QUANTITY_RECOMMENDATION)
            .set(records)
            .onDuplicateKeyUpdate()
            .set(SUPPLY_QUANTITY_RECOMMENDATION.SQR, DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION.SQR))
            .set(
                SUPPLY_QUANTITY_RECOMMENDATION.INVENTORY_ROLLOVER,
                DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION.INVENTORY_ROLLOVER),
            )
            .set(SUPPLY_QUANTITY_RECOMMENDATION.DEMAND, DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION.DEMAND))
            .set(
                SUPPLY_QUANTITY_RECOMMENDATION.RECOMMENDATION_ENABLED,
                DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION.RECOMMENDATION_ENABLED),
            )
            .set(
                SUPPLY_QUANTITY_RECOMMENDATION.MULTI_WEEK_ENABLED,
                DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION.MULTI_WEEK_ENABLED),
            )
            .set(SUPPLY_QUANTITY_RECOMMENDATION.SAFETY_STOCK, DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION.SAFETY_STOCK))
            .set(
                SUPPLY_QUANTITY_RECOMMENDATION.INBOUND,
                DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION.INBOUND),
            )
            .set(
                SUPPLY_QUANTITY_RECOMMENDATION.INCOMING_POS,
                DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION.INCOMING_POS),
            )
            .set(
                SUPPLY_QUANTITY_RECOMMENDATION.UNUSABLE_STOCK,
                DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION.UNUSABLE_STOCK),
            )
            .set(
                SUPPLY_QUANTITY_RECOMMENDATION.UOM,
                DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION.UOM),
            )
            .where(
                isDistinctFromExcluded(SUPPLY_QUANTITY_RECOMMENDATION.SQR)
                    .or(isDistinctFromExcluded(SUPPLY_QUANTITY_RECOMMENDATION.INVENTORY_ROLLOVER))
                    .or(isDistinctFromExcluded(SUPPLY_QUANTITY_RECOMMENDATION.DEMAND))
                    .or(isDistinctFromExcluded(SUPPLY_QUANTITY_RECOMMENDATION.RECOMMENDATION_ENABLED))
                    .or(isDistinctFromExcluded(SUPPLY_QUANTITY_RECOMMENDATION.MULTI_WEEK_ENABLED))
                    .or(isDistinctFromExcluded(SUPPLY_QUANTITY_RECOMMENDATION.SAFETY_STOCK))
                    .or(isDistinctFromExcluded(SUPPLY_QUANTITY_RECOMMENDATION.INBOUND))
                    .or(isDistinctFromExcluded(SUPPLY_QUANTITY_RECOMMENDATION.INCOMING_POS))
                    .or(isDistinctFromExcluded(SUPPLY_QUANTITY_RECOMMENDATION.UNUSABLE_STOCK))
                    .or(isDistinctFromExcluded(SUPPLY_QUANTITY_RECOMMENDATION.UOM)),
            )
            .returning()
            .fetch()
            .map { record -> toSupplyQuantityRecommendation(record) }
        return updatedSqrs
    }

    private fun toSupplyQuantityRecommendation(record: SupplyQuantityRecommendationRecord) =
        SupplyQuantityRecommendation(
            dcCode = record[SUPPLY_QUANTITY_RECOMMENDATION.DC_CODE],
            skuId = record[SUPPLY_QUANTITY_RECOMMENDATION.SKU_ID],
            productionWeek = record[SUPPLY_QUANTITY_RECOMMENDATION.WEEK],
            supplyQuantityRecommendationValue = record[SUPPLY_QUANTITY_RECOMMENDATION.SQR].mapToSkuQuantity(
                record.uom,
            ),
            aggregatedDemand = record[SUPPLY_QUANTITY_RECOMMENDATION.DEMAND].mapToSkuQuantity(record.uom),
            inventoryRollover = record[SUPPLY_QUANTITY_RECOMMENDATION.INVENTORY_ROLLOVER].mapToSkuQuantity(
                record.uom,
            ),
            safetyStock = record[SUPPLY_QUANTITY_RECOMMENDATION.SAFETY_STOCK]?.mapToSkuQuantity(record.uom),
            recommendationEnabled = record[SUPPLY_QUANTITY_RECOMMENDATION.RECOMMENDATION_ENABLED],
            multiWeekEnabled = record[SUPPLY_QUANTITY_RECOMMENDATION.MULTI_WEEK_ENABLED],
            inbound = record[SUPPLY_QUANTITY_RECOMMENDATION.INBOUND].mapToSkuQuantity(record.uom),
            incomingPos = record[SUPPLY_QUANTITY_RECOMMENDATION.INCOMING_POS].mapToSkuQuantity(record.uom),
            unusableStock = record[SUPPLY_QUANTITY_RECOMMENDATION.UNUSABLE_STOCK].mapToSkuQuantity(record.uom),
        )

    private fun toRecords(supplyQuantityRecommendations: List<SupplyQuantityRecommendation>): List<SupplyQuantityRecommendationRecord> =
        supplyQuantityRecommendations.map { supplyQuantityRecommendation ->
            SupplyQuantityRecommendationRecord().apply {
                dcCode = supplyQuantityRecommendation.dcCode
                skuId = supplyQuantityRecommendation.skuId
                week = supplyQuantityRecommendation.productionWeek
                sqr = supplyQuantityRecommendation.supplyQuantityRecommendationValue.getValue()
                inventoryRollover = supplyQuantityRecommendation.inventoryRollover.getValue()
                demand = supplyQuantityRecommendation.aggregatedDemand.getValue()
                safetyStock = supplyQuantityRecommendation.safetyStock?.getValue()
                recommendationEnabled = supplyQuantityRecommendation.recommendationEnabled
                multiWeekEnabled = supplyQuantityRecommendation.multiWeekEnabled
                uom = mapToUom(supplyQuantityRecommendation.uom)
                inbound = supplyQuantityRecommendation.inbound.getValue()
                incomingPos = supplyQuantityRecommendation.incomingPos.getValue()
                unusableStock = supplyQuantityRecommendation.unusableStock.getValue()
            }
        }

    private fun <T> isDistinctFromExcluded(field: TableField<SupplyQuantityRecommendationRecord, T>): Condition =
        field.isDistinctFrom(DSL.excluded(field))

    fun BigDecimal.mapToSkuQuantity(uom: Uom) =
        SkuQuantity.fromBigDecimal(this, mapToSkuUom(uom))

    fun mapToSkuUom(uom: Uom) =
        when (uom) {
            UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
            UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
            UOM_UNIT -> SkuUOM.UOM_UNIT
            UOM_KG -> SkuUOM.UOM_KG
            UOM_LBS -> SkuUOM.UOM_LBS
            UOM_GAL -> SkuUOM.UOM_GAL
            UOM_LITRE -> SkuUOM.UOM_LITRE
            UOM_OZ -> SkuUOM.UOM_OZ
        }

    fun mapToUom(skuUOM: SkuUOM) =
        when (skuUOM) {
            SkuUOM.UOM_UNSPECIFIED -> UOM_UNSPECIFIED
            SkuUOM.UOM_UNRECOGNIZED -> UOM_UNRECOGNIZED
            SkuUOM.UOM_UNIT -> UOM_UNIT
            SkuUOM.UOM_KG -> UOM_KG
            SkuUOM.UOM_LBS -> UOM_LBS
            SkuUOM.UOM_GAL -> UOM_GAL
            SkuUOM.UOM_LITRE -> UOM_LITRE
            SkuUOM.UOM_OZ -> UOM_OZ
        }
}

data class SupplyQuantityRecommendation(
    val dcCode: String,
    val skuId: UUID,
    val productionWeek: String,
    val supplyQuantityRecommendationValue: SkuQuantity,
    val aggregatedDemand: SkuQuantity,
    val inventoryRollover: SkuQuantity,
    val incomingPos: SkuQuantity,
    val inbound: SkuQuantity,
    val unusableStock: SkuQuantity,
    val safetyStock: SkuQuantity?,
    val recommendationEnabled: Boolean,
    val multiWeekEnabled: Boolean
) {
    val uom: SkuUOM
        get() = supplyQuantityRecommendationValue.unitOfMeasure

    companion object
}
