plugins {
    id("com.hellofresh.cif.application-conventions")
    `test-functional`
    hellofresh.`test-fixtures`
    alias(libs.plugins.jooq)
}

group = "$group.sqr"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "calculation|uom|supply_quantity_recommendation|dc_config|dc_config_weight_view|sqr_short_shelf_life" +
                            "|sku_specification|sku_specification_view|purchase_orders_view|goods_received_note|" +
                            "purchase_order"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(projects.supplyQuantityRecommendationLib)
    implementation(projects.safetyStock.safetyStockLib)
    implementation(projects.distributionCenterLib)
    implementation(projects.lib.db)
    implementation(projects.lib)
    implementation(libs.ktor.core)
    implementation(libs.coroutines.core)
    implementation(libs.coroutines.jdk8)
    implementation(projects.lib.featureflags)
    implementation(projects.purchaseOrder.purchaseOrderLib)
    implementation(projects.lib.sqs)
    implementation(projects.lib.s3)
    implementation(libs.protobuf.grpc)
    implementation(projects.lib.fileConsumer)
    implementation(projects.skuInputsLib)

    testImplementation(libs.mockk)
    testImplementation(testFixtures(projects.distributionCenterModels))
    testImplementation(testFixtures(projects.supplyQuantityRecommendationJob))

    testFunctionalImplementation(projects.libTests)
    testFunctionalImplementation(testFixtures(projects.distributionCenterModels))
    testFunctionalImplementation(testFixtures(projects.lib.featureflags))
    testFunctionalImplementation(projects.purchaseOrder.purchaseOrderLib)
    testImplementation(testFixtures(projects.supplyQuantityRecommendationJob))
}
