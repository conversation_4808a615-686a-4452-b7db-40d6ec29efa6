package com.hellofresh.cif.business.stockupdate.model

import com.hellofresh.cif.business.stock_update.schema.enums.Uom
import com.hellofresh.cif.models.SkuUOM

object SkuQuantityMapper {
    fun mapToSkuUom(uom: Uom) =
        when (uom) {
            Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
            Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
            Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
            Uom.UOM_KG -> SkuUOM.UOM_KG
            Uom.UOM_LBS -> SkuUOM.UOM_LBS
            Uom.UOM_GAL -> SkuUOM.UOM_GAL
            Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
            Uom.UOM_OZ -> SkuUOM.UOM_OZ
        }

    fun mapToDbUom(skuUOM: SkuUOM) =
        when (skuUOM) {
            SkuUOM.UOM_UNSPECIFIED -> Uom.UOM_UNSPECIFIED
            SkuUOM.UOM_UNRECOGNIZED -> Uom.UOM_UNRECOGNIZED
            SkuUOM.UOM_UNIT -> Uom.UOM_UNIT
            SkuUOM.UOM_KG -> Uom.UOM_KG
            SkuUOM.UOM_LBS -> Uom.UOM_LBS
            SkuUOM.UOM_GAL -> Uom.UOM_GAL
            SkuUOM.UOM_LITRE -> Uom.UOM_LITRE
            SkuUOM.UOM_OZ -> Uom.UOM_OZ
        }
}
