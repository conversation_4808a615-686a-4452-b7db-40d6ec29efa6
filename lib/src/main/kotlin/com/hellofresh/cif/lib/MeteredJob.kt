package com.hellofresh.cif.lib

import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.Timer
import org.apache.logging.log4j.kotlin.Logging

class MeteredJob(meterRegistry: MeterRegistry, private val jobName: String, private val fn: suspend () -> Unit) {

    private val jobDuration = Timer.builder("import_duration")
        .description("Record the elapsed time of the execution of the job")
        .tags(tags(jobName))
        .publishPercentileHistogram()
        .register(meterRegistry)

    private val successMetrics = Counter.builder("component_success")
        .description("count of successful runs of a job")
        .tags(tags(jobName))
        .register(meterRegistry)

    private val failureMetrics = Counter.builder("component_failure")
        .description("count of failed runs of a job")
        .tags(tags(jobName))
        .register(meterRegistry)

    @Suppress("TooGenericExceptionCaught")
    suspend fun execute() {
        try {
            logger.info("Starting $jobName job")
            recordSuspended(jobDuration, fn)
            successMetrics.increment()
        } catch (e: Throwable) {
            logger.error("Error in $jobName job", e)
            failureMetrics.increment()
        }
    }

    companion object : Logging {
        fun tags(jobName: String) = listOf(Tag.of("name", jobName))
    }
}
