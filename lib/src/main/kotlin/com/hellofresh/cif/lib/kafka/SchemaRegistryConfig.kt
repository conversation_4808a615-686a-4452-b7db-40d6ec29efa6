package com.hellofresh.cif.lib.kafka

import com.hellofresh.cif.config.ConfigurationLoader
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClientConfig

fun loadSchemaRegistryClientConfig() = mapOf(
    SchemaRegistryClientConfig.BASIC_AUTH_CREDENTIALS_SOURCE to "USER_INFO",
    SchemaRegistryClientConfig.USER_INFO_CONFIG
        to "${
            ConfigurationLoader.getStringOrDefault(
                "HF_SCHEMA_REGISTRY_AIVEN_USERNAME",
                ConfigurationLoader.aivenUsername,
            )
        }:${
            ConfigurationLoader.getStringOrDefault(
                "HF_SCHEMA_REGISTRY_AIVEN_PASSWORD",
                ConfigurationLoader.aivenPassword,
            )
        }",
)
