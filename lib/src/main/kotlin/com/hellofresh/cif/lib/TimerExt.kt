package com.hellofresh.cif.lib

import io.micrometer.core.instrument.Timer
import java.time.Duration
import java.time.Instant
import java.util.concurrent.CompletionStage

fun <T> record(timer: Timer, fn: () -> CompletionStage<T>): CompletionStage<T> {
    val startTime = Instant.now()
    return fn().whenComplete { _, _ ->
        timer.record(Duration.between(startTime, Instant.now()))
    }
}

fun <T> recordFunction(timer: Timer, fn: () -> T): T {
    val startTime = Instant.now()
    return try {
        fn()
    } finally {
        timer.record(Duration.between(startTime, Instant.now()))
    }
}

suspend fun <T> recordSuspended(timer: Timer, fn: suspend () -> T): T {
    val startTime = Instant.now()
    return try {
        fn()
    } finally {
        timer.record(Duration.between(startTime, Instant.now()))
    }
}
