package com.hellofresh.cif.lib.kafka.serde

import com.google.protobuf.Message
import com.google.protobuf.Parser
import kotlin.reflect.KClass
import org.apache.kafka.common.serialization.Deserializer
import org.apache.kafka.common.serialization.Serdes.WrapperSerde
import org.apache.kafka.common.serialization.Serializer

open class ProtoSerde<T : Message>(parser: Parser<T>) : WrapperSerde<T>(ProtoSerializer(), ProtoDeserializer(parser)) {
    companion object {
        inline operator fun <reified T : Message> invoke(): ProtoSerde<T> =
            ProtoSerde(T::class.protoParser)
    }
}

open class ProtoSerializer<T : Message> : Serializer<T> {
    override fun serialize(topic: String?, data: T?): ByteArray? =
        data?.toByteArray()
}

open class ProtoDeserializer<T : Message>(private val parser: Parser<T>) : Deserializer<T> {
    override fun deserialize(topic: String?, data: ByteArray?): T? =
        data?.let(parser::parseFrom)

    companion object {
        inline operator fun <reified T : Message> invoke(): ProtoDeserializer<T> =
            ProtoDeserializer(T::class.protoParser)
    }
}

@PublishedApi
internal val <T : Message> Class<T>.protoParser: Parser<T>
    get() = get("PARSER")

/** @see [Message.getParserForType] */
@PublishedApi
internal val <T : Message> KClass<T>.protoParser: Parser<T>
    get() = java.protoParser

@Suppress("UNCHECKED_CAST")
private operator fun <T : Message, R : Any> Class<T>.get(field: String): R =
    getDeclaredField(field).apply { isAccessible = true }.get(null) as R
