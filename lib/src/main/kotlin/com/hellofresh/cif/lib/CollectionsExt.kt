package com.hellofresh.cif.lib

inline fun <T, K> Iterable<T>.groupByToSet(keySelector: (T) -> K): Map<K, Set<T>> =
    groupByToSet(keySelector) { it }

inline fun <T, K, V> Iterable<T>.groupByToSet(keySelector: (T) -> K, valueTransform: (T) -> V): Map<K, Set<V>> =
    buildMap<K, MutableSet<V>> {
        <EMAIL> { element ->
            val key = keySelector(element)
            val set = getOrPut(key) { mutableSetOf<V>() }
            set.add(valueTransform(element))
        }
    }

fun <T, R> Iterable<Pair<T, R>>.groupByFirst(): Map<T, List<R>> = groupBy({ it.first }) { it.second }

fun <T, R> Iterable<Pair<T, R>>.groupByFirstToSet(): Map<T, Set<R>> = groupByToSet({ it.first }) { it.second }

fun <T, R> Iterable<Pair<T, R>>.groupBySecond(): Map<R, List<T>> = groupBy({ it.second }) { it.first }

fun <T, R> Iterable<Pair<T, R>>.groupBySecondToSet(): Map<R, Set<T>> = groupByToSet({ it.second }) { it.first }

inline fun <T, R> Collection<T>.letIfNotEmpty(block: (Collection<T>) -> R): R? =
    if (isNotEmpty()) block(this) else null
