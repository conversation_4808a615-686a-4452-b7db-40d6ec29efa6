package com.hellofresh.cif.lib

import dev.inmo.krontab.builder.buildSchedule
import dev.inmo.krontab.doInfinity
import java.time.LocalDateTime
import java.time.ZoneOffset.UTC
import java.util.concurrent.ExecutorService
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeUnit.HOURS
import java.util.concurrent.TimeUnit.MINUTES
import java.util.concurrent.TimeUnit.SECONDS
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import org.apache.logging.log4j.kotlin.Logging

fun interface Scheduler : AutoCloseable {
    fun schedule(fn: suspend () -> Unit)
    override fun close() = Unit // Nop default implementation
}

class KrontabScheduler(
    private val period: Int,
    private val timeUnit: TimeUnit,
    private val executor: ExecutorService,
) : Scheduler {
    private val kronScheduler = buildSchedule {
        val now = LocalDateTime.now(UTC)
        when (timeUnit) {
            SECONDS -> seconds { 0 every period }
            MINUTES -> {
                seconds { at(now.second) }
                minutes { 0 every period }
            }

            HOURS -> {
                seconds { at(now.second) }
                minutes { at(now.minute) }
                hours { 0 every period }
            }

            else -> error("TimeUnit: ${timeUnit.name} is unsupported")
        }
    }

    private val scope = CoroutineScope(executor.asCoroutineDispatcher())

    override fun schedule(fn: suspend () -> Unit) {
        scope
            .launch {
                kronScheduler.doInfinity { fn() }
            }
            .invokeOnCompletion {
                if (it is CancellationException) {
                    logger.warn("Scheduler is cancelled.", it)
                } else {
                    logger.error("Scheduler has failed!", it)
                }
                close()
            }
    }

    override fun close() {
        executor.shutdownNow()
        scope.cancel()
    }

    companion object : Logging
}
