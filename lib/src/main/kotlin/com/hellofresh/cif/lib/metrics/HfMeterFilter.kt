package com.hellofresh.cif.lib.metrics

import io.micrometer.core.instrument.Meter.Id
import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.config.MeterFilter

/**
 * MeterFilter which prefixes the metric name with the given prefix when the
 * prefix is provided and is not blank string.
 *
 * In addition, it tags all the metrics with application and service tags. So it
 * could be possible to drill down the metric granularity to the service level.
 *
 * @param namePrefix the prefix for the metric name.
 * @param serviceName the application name, generally it is the gradle module name.
 */
class HfMeterFilter(
    namePrefix: String = "",
    serviceName: String,
) : MeterFilter {

    private val metricNamePrefix = if (namePrefix.isNotBlank()) "$namePrefix." else ""

    private val commonTags = setOf(
        Tag.of("application", serviceName),
        Tag.of("service", serviceName),
    )

    override fun map(id: Id): Id = id
        .withName("$metricNamePrefix${id.name}")
        .withTags(commonTags)
}
