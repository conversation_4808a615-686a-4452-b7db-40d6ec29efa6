package com.hellofresh.cif.lib

import com.hellofresh.cif.checks.CheckResult
import com.hellofresh.cif.checks.Checks
import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.shutdown.shutdownNeeded
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.ServerReady
import io.ktor.server.engine.embeddedServer
import io.ktor.server.netty.Netty
import io.ktor.server.response.respondText
import io.ktor.server.routing.get
import io.ktor.server.routing.routing
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.prometheus.PrometheusMeterRegistry
import java.util.concurrent.atomic.AtomicBoolean
import org.apache.logging.log4j.kotlin.Logging

object StatusServer : Logging {

    private val isUp = AtomicBoolean(false)

    fun run(
        meterRegistry: MeterRegistry?,
        port: Int,
        healthCheck: Checks = HealthChecks,
        startUpCheck: Checks = StartUpChecks,
        additionalModule: Application.() -> Unit = {},
    ) {
        embeddedServer(Netty, port = port) {
            routing {
                get("/health") {
                    val result = healthCheck.check().result
                    logger.info("Health Probe: $result")
                    call.response.status(
                        if (result) HttpStatusCode.OK else HttpStatusCode.Locked,
                    )
                }
                if (meterRegistry == null || meterRegistry !is PrometheusMeterRegistry) {
                    logger.warn("No metrics server will be started")
                } else {
                    get("/prometheus") {
                        call.respondText(meterRegistry.scrape())
                    }
                }
                get("/startup") {
                    val result = startUpCheck.check().result
                    logger.info("StartUp Probe: $result")
                    call.response.status(
                        if (result) HttpStatusCode.OK else HttpStatusCode.TooEarly,
                    )
                }
            }
            additionalModule()
        }
            .also {
                shutdownNeeded {
                    AutoCloseable {
                        it.stop(
                            gracePeriodMillis = 15_000,
                            timeoutMillis = 15_000,
                        )
                        isUp.set(false)
                    }
                }
                it.monitor.subscribe(ServerReady) {
                    isUp.set(true)
                    logger.info("Status Server ready")
                }
                StartUpChecks.add(::check)
            }.start(false)
    }

    private fun check() = CheckResult("StatusServer", isUp.get())
}
