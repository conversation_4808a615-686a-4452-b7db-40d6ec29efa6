package com.hellofresh.cif.lib.kafka

import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.time.Duration
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.common.TopicPartition
import org.junit.jupiter.api.Test

class CoroutinesProcessorProcessorStrategyTest {
    private val kafkaConsumerMock: KafkaConsumer<String, String> = mockk(relaxed = true)
    private val pollConfig = PollConfig(
        kotlin.time.Duration.parse("PT1M"),
        10,
        kotlin.time.Duration.parse("PT100S"),
    )
    private val meterRegistry = SimpleMeterRegistry()

    @Test
    fun `Should not advance kafka offset and continue if CoroutinesProcessor is configured with IgnoreAndContinueProcessing`() {
        val coroutinesProcessor = CoroutinesProcessor(
            pollConfig = pollConfig,
            consumer = kafkaConsumerMock,
            meterRegistry = meterRegistry,
            handleDeserializationException = IgnoreExceptions,
            process = { throw IllegalArgumentException() },
            recordProcessingExceptionStrategy =
            IgnoreAndContinueProcessing(meterRegistry, "XXL"),
        )
        pollOnceThenExit(coroutinesProcessor)

        runBlocking {
            coroutinesProcessor.run()
        }
        verify { kafkaConsumerMock.poll(any<Duration>()) }
        verify(exactly = 0) { kafkaConsumerMock.commitAsync() }
    }

    @Test
    fun `Should not advance kafka offset and stop CoroutinesProcessor if CoroutinesProcessor is configured with FailProcessing`() {
        val coroutinesProcessor = spyk(
            CoroutinesProcessor(
                pollConfig = pollConfig,
                consumer = kafkaConsumerMock,
                meterRegistry = meterRegistry,
                handleDeserializationException = IgnoreExceptions,
                process = { throw IllegalArgumentException() },
                recordProcessingExceptionStrategy =
                FailProcessing(meterRegistry, "XXL"),
            ),
        )
        pollOnceThenExit(coroutinesProcessor)

        runBlocking {
            coroutinesProcessor.run()
        }
        verify(exactly = 1) { kafkaConsumerMock.poll(any<Duration>()) }
        verify(exactly = 0) { kafkaConsumerMock.commitAsync() }
        verify { kafkaConsumerMock.close() }
    }

    @Test
    fun `should retry processing and not commit Kafka offset when using RetryThenContinueProcessing`() {
        val process: suspend (ConsumerRecords<String, String>) -> Unit = mockk(relaxed = true)
        val numRetries = 3
        val coroutinesProcessor = CoroutinesProcessor(
            pollConfig = pollConfig,
            consumer = kafkaConsumerMock,
            meterRegistry = meterRegistry,
            handleDeserializationException = IgnoreExceptions,
            process = process,
            recordProcessingExceptionStrategy =
            RetryAndContinueProcessing(numRetries, meterRegistry, "XXL"),
        )
        coEvery { process.invoke(any()) } throws IllegalArgumentException("fails")
        pollOnceThenExit(coroutinesProcessor)

        runBlocking {
            coroutinesProcessor.run()
        }
        verify { kafkaConsumerMock.poll(any<Duration>()) }
        coVerify(exactly = numRetries) { process.invoke(any()) }
        verify(exactly = 0) { kafkaConsumerMock.commitAsync() }
    }

    private fun pollOnceThenExit(coroutinesProcessor: CoroutinesProcessor<String, String>) {
        val consumerRecord = mockk<ConsumerRecord<String, String>>(relaxed = true)
        every {
            kafkaConsumerMock.poll(any<Duration>())
        } returns ConsumerRecords(mapOf(mockk<TopicPartition>() to listOf(consumerRecord))) andThenAnswer {
            coroutinesProcessor.close()
            ConsumerRecords.empty()
        }
    }
}
