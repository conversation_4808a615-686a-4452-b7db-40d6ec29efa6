package com.hellofresh.cif.lib.com.hellofresh.cif.lib.models

import com.fasterxml.jackson.databind.JsonMappingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.inventory.models.InventoryValue
import java.math.BigDecimal
import kotlin.test.assertTrue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class InventoryQuantityDeserializerTest {

    private val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()

    private val inventoryJsonWithUnitOfMeasure = """
        {
            "inventory": [
                {
                    "qty": {
                        "value": 440,
                        "uom": "UOM_UNIT"
                    },
                    "expiry_date": "2024-09-20",
                    "location": {
                        "id": "SP-05-08-4",
                        "type": "LOCATION_TYPE_STAGING",
                        "transportModuleId": null
                    }
                }
            ]
        }
    """.trimIndent()

    private val inventoryJsonWithoutUnitOfMeasure = """
        {
            "inventory": [
                {
                    "qty": 440,
                    "expiry_date": "2024-09-20",
                    "location": {
                        "id": "SP-05-08-4",
                        "type": "LOCATION_TYPE_STAGING",
                        "transportModuleId": null
                    }
                }
            ]
        }
    """.trimIndent()

    private val inventoryJsonWithInvalidQty = """
        {
            "inventory": [
                {
                    "qty": "invalid_quantity",
                    "expiry_date": "2024-09-20",
                    "location": {
                        "id": "SP-05-08-4",
                        "type": "LOCATION_TYPE_STAGING",
                        "transportModuleId": null
                    }
                }
            ]
        }
    """.trimIndent()

    @Test
    fun `should deserialize inventory sku quantity with value and uom successfully`() {
        val inventoryValue = objectMapper.readValue(inventoryJsonWithUnitOfMeasure, InventoryValue::class.java)
        val skuQuantity = inventoryValue.inventory.first().qty

        assertEquals(
            BigDecimal(440),
            skuQuantity.getValue()
        )
        assertEquals(SkuUOM.UOM_UNIT, skuQuantity.unitOfMeasure)
    }

    @Test
    fun `should deserialize inventory sku quantity with value and uom successfully - without unit of measurement`() {
        val inventoryValue = objectMapper.readValue(inventoryJsonWithoutUnitOfMeasure, InventoryValue::class.java)
        val skuQuantity = inventoryValue.inventory.first().qty

        assertEquals(
            BigDecimal(440),
            skuQuantity.getValue()
        )
        assertEquals(SkuUOM.UOM_UNIT, skuQuantity.unitOfMeasure)
    }

    @Test
    fun `should throw exception while deserializing empty inventory sku quantity`() {
        val exception = assertThrows<JsonMappingException> {
            objectMapper.readValue(inventoryJsonWithInvalidQty, InventoryValue::class.java)
        }
        assertTrue(
            exception.message?.contains(
                "Unknown format for SkuQuantity, exception while parsing Inventory SkuQuantity : \"invalid_quantity\" ",
            )!!,
        )
    }
}
