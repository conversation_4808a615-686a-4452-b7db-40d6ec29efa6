package com.hellofresh.cif.lib.kafka.serde

import kotlin.test.Test
import kotlin.test.assertEquals

internal class JacksonSerdeTest {
    @Test fun jacksonSerde() {
        val expected = JsonTestRecord("test")
        val serde = serde<JsonTestRecord>()
        val bytes = serde.serializer().serialize("topic", expected)
        val actual = serde.deserializer().deserialize("topic", bytes)

        assertEquals(expected, actual)
    }

    data class JsonTestRecord(val value: String)
}
