package com.hellofresh.cif.lib.com.hellofresh.cif.lib.dcbalancer

import com.hellofresh.cif.lib.dcbalancer.DcWeight
import com.hellofresh.cif.lib.dcbalancer.DcWeightRoundRobinLoadSplitter
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import org.junit.jupiter.api.Test

class DcWeightRoundRobinDcLoadSplitterTest {

    @Test fun `split DCs equally based on total number of instances`() {
        val dcConfig = setOf(
            DcWeight("AA", Random.nextLong()),
            DcWeight("BB", Random.nextLong()),
            DcWeight("CC", Random.nextLong()),
            Dc<PERSON>eight("DD", Random.nextLong()),
            DcWeight("EE", Random.nextLong()),
            <PERSON><PERSON><PERSON><PERSON><PERSON>("FF", Random.nextLong()),
            D<PERSON><PERSON>ei<PERSON>("GG", Random.nextLong()),
            <PERSON><PERSON><PERSON><PERSON><PERSON>("HH", Random.nextLong()),
        )

        val instanceZeroFilteredDcCodes = DcWeightRoundRobinLoadSplitter(2, "ci-calculator-job-0")
            .filterDcConfigs(dcConfig)
        val instanceOneFilteredDcCodes = DcWeightRoundRobinLoadSplitter(2, "ci-calculator-job-1")
            .filterDcConfigs(dcConfig)

        assertEquals(4, instanceZeroFilteredDcCodes.size)
        assertEquals(4, instanceOneFilteredDcCodes.size)
        assertNotEquals(instanceZeroFilteredDcCodes, instanceOneFilteredDcCodes)
    }

    @Test
    fun `split DCs equally and using weight order on total number of instances`() {
        val dcConfig = setOf(
            DcWeight("AA", 500),
            DcWeight("BB", 400),
            DcWeight("CC", 300),
            DcWeight("DD", 200),
            DcWeight("EE", 100),
        )

        val instanceZeroFilteredDcCodes = DcWeightRoundRobinLoadSplitter(2, "ci-calculator-job-0")
            .filterDcConfigs(dcConfig)
        val instanceOneFilteredDcCodes = DcWeightRoundRobinLoadSplitter(2, "ci-calculator-job-1")
            .filterDcConfigs(dcConfig)

        assertEquals(3, instanceZeroFilteredDcCodes.size)
        assertEquals(setOf("AA", "CC", "EE"), instanceZeroFilteredDcCodes)
        assertEquals(2, instanceOneFilteredDcCodes.size)
        assertEquals(setOf("BB", "DD"), instanceOneFilteredDcCodes)
    }
}
