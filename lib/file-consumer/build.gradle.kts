plugins {
    id("com.hellofresh.cif.common-conventions")
    `test-functional`
    hellofresh.`test-fixtures`
}

dependencies {
    api(projects.lib.logging)
    implementation(projects.lib.configuration)

    implementation("org.jetbrains.kotlin:kotlin-reflect")
    api(libs.apache.commonscsv)
    api(libs.apache.parquet.avro)

    testImplementation(projects.libTests)
    testImplementation(libs.testcontainers.postgresql)
    testImplementation(libs.mockk)
    testImplementation(libs.coroutines.core)
    testImplementation(libs.coroutines.jdk8)
}
