package com.hellofresh.cif.db

import io.mockk.every
import io.mockk.mockk
import kotlin.IllegalStateException
import kotlin.test.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.Result

class DbDslCheckTest {

    private val dsLContext: DSLContext = mockk(relaxed = true)

    @Test fun `check gives ok`() {
        every { dsLContext.fetch(any<String>()) } returns mockk<Result<Record>>()

        assertTrue(DbDslCheck.isHealthy(dsLContext))
    }

    @Test fun `check gives ko`() {
        every { dsLContext.fetch(any<String>()) } throws IllegalStateException("DB DSL check gives ko")

        assertFalse(DbDslCheck.isHealthy(dsLContext))
    }
}
