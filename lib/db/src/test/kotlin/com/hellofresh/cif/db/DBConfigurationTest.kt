package com.hellofresh.cif.db

import com.hellofresh.cif.db.DBConfiguration.getMasterDatabaseConfig
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

internal class DBConfigurationTest : AbstractDbTest() {

    @Test
    fun `jooq dsl context can perform queries`() {
        val jooqDslContext = DBConfiguration.jooqDslContext(databaseConfig, 2, SimpleMeterRegistry())

        jooqDslContext.insertInto(table).values(1).execute()

        val count = jooqDslContext.selectCount().from(table).where(idField.eq(1)).execute()

        assertEquals(1, count)
    }

    @Test
    fun `should get the valid database config`() {
        System.setProperty("HF_INVENTORY_DB_MASTER_HOST", "test-database-hostname")
        System.setProperty("HF_INVENTORY_DB_USERNAME", "test-database-username")
        System.setProperty("HF_INVENTORY_DB_PASSWORD", "test-database-password")
        val databaseConfig = getMasterDatabaseConfig()
        databaseConfig.apply {
            kotlin.test.assertEquals("test-database-hostname", hostName)
            kotlin.test.assertEquals("test-database-password", password)
            kotlin.test.assertEquals("test-database-username", userName)
        }
    }

    @Test
    fun `should throw exception when database master db value not present`() {
        Assertions.assertThrows(IllegalArgumentException::class.java) {
            getMasterDatabaseConfig()
        }
    }

    @Test
    fun `should throw exception when database username value not present`() {
        System.setProperty("HF_INVENTORY_DB_MASTER_HOST", "test-inventory-db-master-host")
        Assertions.assertThrows(IllegalArgumentException::class.java) {
            getMasterDatabaseConfig()
        }
    }

    @Test
    fun `should throw exception when database password not present`() {
        System.setProperty("HF_INVENTORY_DB_MASTER_HOST", "test-inventory-db-master-host")
        System.setProperty("HF_INVENTORY_DB_USERNAME", "test-inventory-user-name")
        Assertions.assertThrows(IllegalArgumentException::class.java) {
            getMasterDatabaseConfig()
        }
    }

    @AfterEach
    fun cleanUp() {
        System.clearProperty("HF_INVENTORY_DB_MASTER_HOST")
        System.clearProperty("HF_INVENTORY_DB_USERNAME")
        System.clearProperty("HF_INVENTORY_DB_PASSWORD")
    }
}
