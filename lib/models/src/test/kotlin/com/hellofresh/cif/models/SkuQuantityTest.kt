package com.hellofresh.cif.models

import com.hellofresh.cif.models.SkuUOM.UOM_KG
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import java.math.BigDecimal
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import org.junit.jupiter.api.Assertions.assertThrows

class SkuQuantityTest {

    @Test
    fun `should create SkuQuantity from 0 BigDecimal`() {
        val quantity = SkuQuantity.fromBigDecimal(BigDecimal("0"), UOM_UNIT)
        assertEquals(
            BigDecimal("0.00000").stripTrailingZeros().toPlainString().toBigDecimal(),
            quantity.getValue(),
            "Not able to create Sku Quantity from BigDecimal",
        )
    }

    @Test
    fun `should create SkuQuantity from negative BigDecimal`() {
        val quantity = SkuQuantity.fromBigDecimal(BigDecimal("-100"), UOM_UNIT)
        assertEquals(
            BigDecimal("-100.00000").stripTrailingZeros().toPlainString(),
            quantity.getValue().toPlainString(),
            "Not able to create Sku Quantity from BigDecimal",
        )
    }

    @Test
    fun `should create SkuQuantity from BigDecimal`() {
        val quantity = SkuQuantity.fromBigDecimal(BigDecimal("0.123456"), UOM_UNIT)
        assertEquals(BigDecimal("0.12346"), quantity.getValue(), "Not able to create Sku Quantity from BigDecimal")
    }

    @Test
    fun `should create SkuQuantity from Long`() {
        val quantity = SkuQuantity.fromLong(123456L, UOM_UNIT)
        assertEquals(
            BigDecimal("123456.00000").stripTrailingZeros().toPlainString().toBigDecimal(),
            quantity.getValue(),
            "Not able to create SkuQuantity from Long",
        )
    }

    @Test
    fun `should create SkuQuantity from Double`() {
        val quantity = SkuQuantity.fromDouble(0.123456, UOM_UNIT)
        assertEquals(BigDecimal("0.12346"), quantity.getValue(), "Not able to create SkuQuantity from Double")
    }

    @Test
    fun `should create SkuQuantity from String`() {
        val quantity = SkuQuantity.fromString("0.123456", UOM_UNIT)
        assertEquals(BigDecimal("0.12346"), quantity.getValue(), "Not able to create SkuQuantity from String")
    }

    @Test
    fun `should be able to add SkuQuantity`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("0.123456"), UOM_UNIT)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("0.654321"), UOM_UNIT)
        val result = quantity1 + quantity2
        assertEquals(BigDecimal("0.77778"), result.getValue(), "Rounding error for addition")
    }

    @Test
    fun `should be able to add SkuQuantity with default unitOfMeasure`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("0.123456"))
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("0.654321"))
        val result = quantity1 + quantity2
        assertEquals(BigDecimal("0.77778"), result.getValue(), "Rounding error for addition")
    }

    @Test
    fun `should be able to subtract SkuQuantity`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("0.654321"), UOM_UNIT)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("0.123456"), UOM_UNIT)
        val result = quantity1 - quantity2
        assertEquals(BigDecimal("0.53086"), result.getValue(), "Rounding error for subtraction")
    }

    @Test
    fun `should be able to multiply SkuQuantity`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("0.123456"), UOM_UNIT)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("2"), UOM_UNIT)
        val result = quantity1 * quantity2
        assertEquals(BigDecimal("0.24692"), result.getValue(), "Rounding error for multiplication")
    }

    @Test
    fun `should be able to divide SkuQuantity`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("0.246912"), UOM_UNIT)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("2"), UOM_UNIT)
        val result = quantity1 / quantity2
        assertEquals(BigDecimal("0.12346"), result.getValue(), "Rounding error for division")
    }

    @Test
    fun `should unary minus on SkuQuantity`() {
        val quantity = SkuQuantity.fromBigDecimal(BigDecimal("0.123456"), UOM_UNIT)
        val result = -quantity
        assertEquals(BigDecimal("-0.12346"), result.getValue(), "Rounding error for unary minus")
    }

    @Test
    fun `should be able to increment SkuQuantity`() {
        val quantity = SkuQuantity.fromBigDecimal(BigDecimal("0.123456"), UOM_UNIT)
        val result = quantity.inc()
        assertEquals(BigDecimal("1.12346"), result.getValue(), "Rounding error for increment")
    }

    @Test
    fun `should be able to decrement SkuQuantity`() {
        val quantity = SkuQuantity.fromBigDecimal(BigDecimal("0.123456"), UOM_UNIT)
        val result = quantity.dec()
        assertEquals(BigDecimal("-0.87654"), result.getValue(), "Rounding error for decrement")
    }

    @Test
    fun `should throw a SkuUOM mismatch while adding SkuQuantities`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("10.0"), SkuUOM.UOM_UNIT)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("10.0"), SkuUOM.UOM_GAL)

        val exception = assertThrows(IllegalArgumentException::class.java) {
            quantity1 + quantity2
        }

        assertEquals("SkuUOM mismatch: cannot perform operation between UOM_UNIT and UOM_GAL", exception.message)
    }

    @Test
    fun `should throw a SkuUOM mismatch while subtracting SkuQuantities`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("10.0"), SkuUOM.UOM_KG)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("5.0"), SkuUOM.UOM_LBS)

        val exception = assertThrows(IllegalArgumentException::class.java) {
            quantity1 - quantity2
        }

        assertEquals("SkuUOM mismatch: cannot perform operation between UOM_KG and UOM_LBS", exception.message)
    }

    @Test
    fun `should throw a SkuUOM mismatch while multiplying SkuQuantities`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("3.0"), SkuUOM.UOM_LITRE)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("2.0"), SkuUOM.UOM_OZ)

        val exception = assertThrows(IllegalArgumentException::class.java) {
            quantity1 * quantity2
        }

        assertEquals("SkuUOM mismatch: cannot perform operation between UOM_LITRE and UOM_OZ", exception.message)
    }

    @Test
    fun `should throw a SkuUOM mismatch while dividing SkuQuantities`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("10.0"), SkuUOM.UOM_LITRE)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("2.0"), SkuUOM.UOM_GAL)

        val exception = assertThrows(IllegalArgumentException::class.java) {
            quantity1 / quantity2
        }

        assertEquals("SkuUOM mismatch: cannot perform operation between UOM_LITRE and UOM_GAL", exception.message)
    }

    @Test
    fun `should be able to add SkuQuantity with matching SkuUOM`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("5.0"), UOM_UNIT)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("10.0"), UOM_UNIT)

        val result = quantity1 + quantity2

        assertEquals(
            BigDecimal("15.00000").stripTrailingZeros().toPlainString().toBigDecimal(),
            result.getValue(),
        )
        assertEquals(SkuUOM.UOM_UNIT, result.unitOfMeasure)
    }

    @Test
    fun `should be able to subtract SkuQuantity with matching SkuUOM`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("20.0"), SkuUOM.UOM_KG)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("5.0"), SkuUOM.UOM_KG)

        val result = quantity1 - quantity2

        assertEquals(
            BigDecimal("15.00000").stripTrailingZeros().toPlainString().toBigDecimal(),
            result.getValue(),
        )
        assertEquals(SkuUOM.UOM_KG, result.unitOfMeasure)
    }

    @Test
    fun `should be able to multiply SkuQuantity with matching SkuUOM`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("3.0"), SkuUOM.UOM_LITRE)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("2.0"), SkuUOM.UOM_LITRE)

        val result = quantity1 * quantity2

        assertEquals(
            BigDecimal("6.00000").stripTrailingZeros().toPlainString().toBigDecimal(),
            result.getValue(),
        )
        assertEquals(SkuUOM.UOM_LITRE, result.unitOfMeasure)
    }

    @Test
    fun `should be able to divide SkuQuantity with matching SkuUOM`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("10.0"), SkuUOM.UOM_UNIT)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("2.0"), SkuUOM.UOM_UNIT)

        val result = quantity1 / quantity2

        assertEquals(
            BigDecimal("5.00000").stripTrailingZeros().toPlainString().toBigDecimal(),
            result.getValue(),
        )
        assertEquals(SkuUOM.UOM_UNIT, result.unitOfMeasure)
    }

    @Test
    fun `should return the higher SkuQuantity in max function`() {
        val value1 = SkuQuantity.fromBigDecimal(BigDecimal("5.5"), SkuUOM.UOM_UNIT)
        val value2 = SkuQuantity.fromBigDecimal(BigDecimal("3.3"), SkuUOM.UOM_UNIT)
        val result = SkuQuantity.max(value1, value2)

        assertEquals(value1, result)
    }

    @Test
    fun `should return SkuUOM mismatch error while finding max`() {
        val value1 = SkuQuantity.fromBigDecimal(BigDecimal("8.4"), SkuUOM.UOM_UNIT)
        val value2 = SkuQuantity.fromBigDecimal(BigDecimal("6.2"), SkuUOM.UOM_KG)

        assertThrows(IllegalArgumentException::class.java) {
            SkuQuantity.max(value1, value2)
        }
    }

    @Test
    fun `should return same quantity when adding zero with different uom`() {
        val value1 = SkuQuantity.fromBigDecimal(BigDecimal("8.4"), SkuUOM.UOM_UNIT)
        val value2 = SkuQuantity.fromBigDecimal(BigDecimal.ZERO, SkuUOM.UOM_KG)

        assertEquals(value1, value1 + value2)
    }

    @Test
    fun `should git rid of trailing zeros`() {
        val quantity1 = SkuQuantity.fromBigDecimal(BigDecimal("10.0010"), UOM_UNIT)
        val quantity2 = SkuQuantity.fromBigDecimal(BigDecimal("2.200"), UOM_UNIT)

        val result1 = quantity1.getValue()
        val result2 = quantity2.getValue()

        assertEquals(
            BigDecimal("10.001"),
            result1,
        )
        assertEquals(
            BigDecimal("2.2"),
            result2,
        )
    }

    @Test
    fun `should sum SkuQuantities correctly with correct UOM`() {
        val quantities = listOf(
            SkuQuantity.fromBigDecimal(BigDecimal("1.12345"), UOM_KG),
            SkuQuantity.fromBigDecimal(BigDecimal("2.23456"), UOM_KG),
            SkuQuantity.fromBigDecimal(BigDecimal("3.34567"), UOM_KG),
        )
        val sumResult = quantities.sum()
        val sumOfResult = quantities.sumOf { it }
        assertEquals(BigDecimal("6.70368"), sumResult.getValue())
        assertEquals(BigDecimal("6.70368"), sumOfResult.getValue())
        assertEquals(UOM_KG, sumResult.unitOfMeasure)
        assertEquals(UOM_KG, sumOfResult.unitOfMeasure)
    }

    @Test
    fun `should return zero SkuQuantity for empty list`() {
        val quantities = emptyList<SkuQuantity>()
        val sumResult = quantities.sum()
        val sumOfResult = quantities.sumOf { it }
        assertEquals(BigDecimal.ZERO, sumResult.getValue())
        assertEquals(BigDecimal.ZERO, sumOfResult.getValue())
        assertEquals(UOM_UNIT, sumOfResult.unitOfMeasure)
        assertEquals(UOM_UNIT, sumResult.unitOfMeasure)
    }

    @Test
    fun `should return absolute value`() {
        val value = BigDecimal("10.0010")
        assertEquals(
            SkuQuantity.fromBigDecimal(value.abs(), UOM_UNIT),
            SkuQuantity.fromBigDecimal(value, UOM_UNIT).abs(),
        )

        val negativeValue = BigDecimal("-10.0010")
        assertEquals(
            SkuQuantity.fromBigDecimal(negativeValue.abs(), UOM_UNIT),
            SkuQuantity.fromBigDecimal(negativeValue, UOM_UNIT).abs(),
        )
    }

    @Test
    fun `should return zero check`() {
        assertTrue(SkuQuantity.fromBigDecimal(BigDecimal.ZERO, SkuUOM.entries.random()).isZero())
        assertFalse(SkuQuantity.fromBigDecimal(BigDecimal.ZERO, SkuUOM.entries.random()).isNotZero())
        assertFalse(SkuQuantity.fromBigDecimal(BigDecimal("10.0010"), SkuUOM.entries.random()).isZero())
        assertTrue(SkuQuantity.fromBigDecimal(BigDecimal("10.0010"), SkuUOM.entries.random()).isNotZero())
        assertFalse(SkuQuantity.fromBigDecimal(BigDecimal("0.0010"), SkuUOM.entries.random()).isZero())
        assertTrue(SkuQuantity.fromBigDecimal(BigDecimal("0.0010"), SkuUOM.entries.random()).isNotZero())
    }

    @Test
    fun `positive and negative check`() {
        assertTrue(SkuQuantity.fromBigDecimal(BigDecimal.ONE, SkuUOM.entries.random()).isPositive())
        assertFalse(SkuQuantity.fromBigDecimal(BigDecimal.ONE.negate(), SkuUOM.entries.random()).isPositive())

        assertTrue(SkuQuantity.fromBigDecimal(BigDecimal.ONE.negate(), SkuUOM.entries.random()).isNegative())
        assertFalse(SkuQuantity.fromBigDecimal(BigDecimal.ONE, SkuUOM.entries.random()).isNegative())

        assertFalse(SkuQuantity.fromBigDecimal(BigDecimal.ZERO, SkuUOM.entries.random()).isPositive())
        assertFalse(SkuQuantity.fromBigDecimal(BigDecimal.ZERO, SkuUOM.entries.random()).isNegative())
    }

    @Test
    fun `should return be able to compare with zero with different uom`() {
        val value1 = SkuQuantity.fromBigDecimal(BigDecimal("8.4"), SkuUOM.UOM_UNIT)
        val value2 = SkuQuantity.fromBigDecimal(BigDecimal.ZERO, SkuUOM.UOM_KG)

        assertTrue(value1 > value2)
        assertFalse(value1 < value2)
        assertFalse(value1 == value2)
    }
}
