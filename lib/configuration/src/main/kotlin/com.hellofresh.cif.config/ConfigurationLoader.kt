package com.hellofresh.cif.config

import org.apache.commons.configuration.CompositeConfiguration
import org.apache.commons.configuration.Configuration
import org.apache.commons.configuration.EnvironmentConfiguration
import org.apache.commons.configuration.PropertiesConfiguration
import org.apache.commons.configuration.SystemConfiguration

/**
 * ConfigurationLoader class helps to load the configurations from environment variables,
 * application-{HF_TIER}.properties, application.properties.whenever the ConfigurationLoader
 * class is accessed the configurations are loaded in the init block in the following order.
 *  a. Environment variables
 *  b. application-{HF_TIER}.properties
 *  c. application.properties
 */
private var configuration: CompositeConfiguration = CompositeConfiguration()

object ConfigurationLoader : AbstractConfigurationLoader(configuration) {
    private var profileConfig = mutableMapOf<String, List<Configuration>>()

    init {
        loadConfigurations()
    }

    suspend fun <T> withProfile(profile: String, fn: suspend (AbstractConfigurationLoader) -> T): T {
        val config = listOf(
            "/application-${getEnvironment()}-$profile.properties",
            "/application-$profile.properties",
        ).mapNotNull { file ->
            ConfigurationLoader::class.java.getResource(file)
        }.map { configFileUrl ->
            PropertiesConfiguration(configFileUrl)
        }

        profileConfig[profile] = config

        return CompositeConfiguration().apply {
            config.forEach { addConfiguration(it) }
            addConfiguration(configuration)
        }.let {
            fn(object : AbstractConfigurationLoader(it) {})
        }
    }

    private fun loadConfigurations(profile: String? = null) {
        configuration.addConfiguration(SystemConfiguration())
        configuration.addConfiguration(EnvironmentConfiguration())

        configuration.keys.forEach { key ->
            val newKey = if (key.contains("hf.")) {
                key.replace('.', '_').uppercase()
            } else if (profile != null && key.contains("$profile.")) {
                key.replace("$profile.", "").uppercase()
            } else {
                key
            }
            configuration.addProperty(newKey, configuration.getProperty(key))
        }

        listOf(
            "/application-" + getEnvironment() + ".properties",
            "/application.properties",
        ).mapNotNull { file ->
            ConfigurationLoader::class.java.getResource(file)
        }.forEach { configFileUrl ->
            configuration.addConfiguration(PropertiesConfiguration(configFileUrl))
        }
    }
}
