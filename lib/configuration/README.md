# configuration

The Configuration library provides a generic configuration interface which enables applications to read
configuration data from a variety of sources (i.e *.properties, etc).

Configuration library provides access to single configuration parameters as demonstrated by the following code:
  val pollTimeout = ConfigurationLoader.getString("poll.timeout")
  val parallelism = ConfigurationLoader.getInteger("parallelism", 1) // 1 is the default value.

## How to include the 'configuration library' in the application ?
  in `build.gradle.kts`
  `implementation(projects.lib.configuration)`

please set the `application.name` in application.properties file, it will be used
for preparing prometheus configuration.

## What is the order in which the configuration library loads the properties ?

1. Environment configurations.
2. Properties related to HF_TIER i.e `application-live.properties` if the HF_TIER is set to `live`.
3. application-prometheus.properties.

## How to override the properties ?

Environment variable takes the higher preference, then the properties placed in application-{HF_TIER}.properties
example - `application-live.properties`.

## What are the mandatory properties ?

Following are the mandatory properties if the application use Kafka topic consumer / producer.
`"group.id",`
`"bootstrap.servers",`
`"fetch.max.wait.ms",`
`"max.poll.records",`
`"fetch.min.bytes",`
`"max.poll.interval.ms",`
`"auto.offset.reset"`

[Note: only required for live/staging profile]

`"sasl.jaas.config",`
`"sasl.mechanism",`
`"security.protocol",`
`"ssl.endpoint.identification.algorithm",`
`"ssl.truststore.location",`
`"ssl.truststore.password",`

Following are the mandatory properties if the application uses Database.

`HF_INVENTORY_DB_MASTER_HOST`
`HF_INVENTORY_DB_USERNAME`
`HF_INVENTORY_DB_USERNAME`
