package com.hellofresh.cif.s3

import com.hellofresh.cif.config.ConfigurationLoader
import java.io.InputStream
import java.net.URI
import software.amazon.awssdk.auth.credentials.AnonymousCredentialsProvider
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider
import software.amazon.awssdk.regions.Region.EU_WEST_1
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.sts.StsClient
import software.amazon.awssdk.services.sts.auth.StsAssumeRoleCredentialsProvider
import software.amazon.awssdk.services.sts.model.AssumeRoleRequest

const val AUTHOR_NAME_METADATA = "author_name"
const val AUTHOR_EMAIL_METADATA = "author_email"

class S3Importer(role: String? = null, sessionName: String = "") {
    private val s3Client: S3Client = if (ConfigurationLoader.isLocal()) {
        localS3Client(ConfigurationLoader.getStringOrFail("aws.s3.host"))
    } else {
        if (role != null) {
            createS3ClientWithAssumedRole(role, sessionName)
        } else {
            createS3ClientWithDefaultCredentials()
        }
    }

    fun fetchObjectContent(bucketName: String, key: String): InputStream {
        val getObjectRequest = GetObjectRequest.builder()
            .bucket(bucketName)
            .key(key)
            .build()
        return s3Client.getObject(getObjectRequest)
    }

    fun fetchObjectMetadata(bucketName: String, key: String): Map<String, String> {
        val getObjectRequest = GetObjectRequest.builder()
            .bucket(bucketName)
            .key(key)
            .build()

        val objectMetadata = s3Client.getObject(getObjectRequest)
            .response()
            .metadata()

        return objectMetadata
    }

    private fun localS3Client(host: String): S3Client =
        S3Client.builder()
            .region(EU_WEST_1)
            .endpointOverride(URI.create(host))
            .credentialsProvider(AnonymousCredentialsProvider.create())
            .forcePathStyle(true)
            .build()

    private fun createS3ClientWithDefaultCredentials(): S3Client =
        S3Client.builder()
            .credentialsProvider(
                DefaultCredentialsProvider.create(),
            )
            .region(EU_WEST_1)
            .build()

    private fun createS3ClientWithAssumedRole(role: String, sessionName: String?): S3Client {
        val stsClient = StsClient.builder()
            .region(EU_WEST_1)
            .build()
        val assumeRoleRequest = AssumeRoleRequest.builder()
            .roleArn(role)
            .roleSessionName(sessionName)
            .build()
        val credentialsProvider = StsAssumeRoleCredentialsProvider.builder()
            .stsClient(stsClient)
            .refreshRequest(assumeRoleRequest)
            .build()
        return S3Client.builder()
            .region(EU_WEST_1)
            .credentialsProvider(credentialsProvider)
            .build()
    }
}
