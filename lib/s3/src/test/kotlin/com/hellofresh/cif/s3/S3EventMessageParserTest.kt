package com.hellofresh.cif.s3

import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class S3EventMessageParserTest {
    private val s3EventMessageParser = S3EventMessageParser()
    private val bucketName = "testBucket"
    private val key = "test-key.csv"

    @Test
    fun `should return correct bucket name and key for valid event`() {
        val message =
            "{\"Records\":[{\"s3\":{\"bucket\":{\"name\":\"$bucketName\"},\"object\":{\"key\":\"$key\"}}}]}"
        val result = s3EventMessageParser.getS3File(message)

        assertNotNull(result)
        assertEquals(bucketName, result.bucket)
        assertEquals(key, result.key)
    }

    @Test
    fun `should return correct bucket name and key for encode key`() {
        val expectedKey = "test-key 1 test.csv"
        val encodedKey = "test-key+1+test.csv"
        val message =
            "{\"Records\":[{\"s3\":{\"bucket\":{\"name\":\"$bucketName\"},\"object\":{\"key\":\"$encodedKey\"}}}]}"

        val result = s3EventMessageParser.getS3File(message)

        assertNotNull(result)
        assertEquals(bucketName, result.bucket)
        assertEquals(expectedKey, result.key)
    }

    @Test
    fun `should return correct bucket name and key for valid sns event`() {
        val snsMessage =
            "{\"Type\": \"Notification\", \"MessageId\": \"c09dac83-c57b-4597-a42a-dc7f6525f08f\", \"TopicArn\": " +
                "\"arn:aws:sns\", \"Message\": \"{\\\"Records\\\":[{\\\"s3\\\":{\\\"bucket\\\":{\\\"name\\\":" +
                "\\\"$bucketName\\\"},\\\"object\\\":{\\\"key\\\":\\\"$key\\\"}}}]}\" }"

        val result = s3EventMessageParser.getS3File(snsMessage)

        assertNotNull(result)
        assertEquals(bucketName, result.bucket)
        assertEquals(key, result.key)
    }

    @ParameterizedTest(name = "{index} => {1}")
    @MethodSource("provideInvalidCases")
    fun `should not process for invalid event formats`(message: String) {
        val result = s3EventMessageParser.getS3File(message)
        assertNull(result)
    }

    companion object {
        @Suppress("unused")
        @JvmStatic
        fun provideInvalidCases(): Stream<Arguments> = Stream.of(
            Arguments.of(
                "{\"Records\":[{\"s3\":{\"bucket\":{\"name\":\"\"},\"object\":{\"key\":\"test-key.csv\"}}}]}",
                "should return null by reason of empty bucket name",
            ),
            Arguments.of(
                "{\"Records\":[{\"s3\":{\"bucket\":{\"name\":\"test\"},\"object\":{\"key\":\"\"}}}]}",
                "should return null by reason of empty key",
            ),
            Arguments.of(
                "{\"Records\":[{\"eventName\":\"TestEvent\",\"s3\":{\"bucket\":{\"name\":\"test\"},\"object\":{\"key\":\"test-key.csv\"}}}]}",
                "should return null by reason of test event",
            ),
        )
    }
}
