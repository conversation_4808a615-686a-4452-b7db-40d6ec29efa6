package com.hellofresh.cif.sqs

import com.hellofresh.cif.s3.S3EventMessageParser
import org.apache.logging.log4j.kotlin.Logging
import software.amazon.awssdk.services.sqs.model.Message

class SQSMessageProxy(
    private val processor: MessageHandlerServiceInterface,
    private val s3EventMessageParser: S3EventMessageParser,
) {
    suspend fun processMessage(message: Message) {
        val name = processor.name()

        logger.info("Processing the $name SQS message: ${message.body()} started.")

        kotlin.runCatching {
            val event = s3EventMessageParser.getS3Event(message.body())

            if (!s3EventMessageParser.isValidEvents(event)) {
                logger.warn("Ignoring the $name SQS message as it is a test event. Message: ${message.body()}.")
                return
            }

            val s3File = s3EventMessageParser.getS3File(message.body())

            if (s3File != null) {
                logger.info("Latest $name S3 file to be processed: $s3File.")
                processor.process(s3File)
                logger.info("Completed Reading the $name SQS message: ${message.messageId()}.")
            } else {
                throw InvalidFileNameException(
                    "Invalid $name file in message id: ${message.messageId()} received in S3 with empty name, will " +
                        "not be processed , message = ${message.body()}.",
                )
            }
            logger.info("Processing the $name SQS message: ${message.body()} completed.")
        }.onFailure { error ->
            if (error is InvalidFileNameException) {
                logger.warn("Failed to process $name SQS messages: " + error.message)
            } else {
                logger.error("Error while processing the $name SQS messages: " + error.message)
            }
            throw error
        }
    }

    companion object : Logging
}

class InvalidFileNameException(message: String) : Exception(message)
