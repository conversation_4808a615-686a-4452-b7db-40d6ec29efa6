package com.hellofresh.cif.checks

import io.mockk.coVerify
import io.mockk.mockk
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class WarmupChecksTest {

    @Test
    fun `warmup check run warmup and is ok if no error`() {
        val warmupCheck = WarmupCheck("name") {}
        val check = runBlocking { warmupCheck.check() }
        assertTrue(check.result)
    }

    @Test
    fun `warmup check run warmup and is ok if there is error`() {
        val warmupCheck = WarmupCheck("name") {
            throw NullPointerException()
        }

        val check = runBlocking { warmupCheck.check() }
        assertTrue(check.result)
    }

    @Test
    fun `async warmup launches warmup and is ok just when warmup ends`() {
        val flagMock = mockk<Warmup>(relaxed = true)

        val asyncWarmupCheck = AsyncWarmupCheck(
            WarmupCheck("name") {
                delay(1000L)
                flagMock.run()
            },
        )

        assertFalse(runBlocking { asyncWarmupCheck.check().result })
        asyncWarmupCheck.fireAndForget()
        coVerify(exactly = 1, timeout = 2000) { flagMock.run() }

        runBlocking { delay(1000L) }
        assertTrue(runBlocking { asyncWarmupCheck.check().result })
    }

    @Test
    fun `async warmup launches warmup and is ko just when warmup ends with error`() {
        val asyncWarmupCheck = AsyncWarmupCheck(
            WarmupCheck("name") {
                delay(1000L)
                throw IllegalArgumentException()
            },
        )

        assertFalse(runBlocking { asyncWarmupCheck.check().result })
        asyncWarmupCheck.fireAndForget()
        runBlocking { delay(1000L) }
        assertFalse(runBlocking { asyncWarmupCheck.check().result })
    }

    @Test
    fun `adding async warmup to checks launches warmup and add check to list`() {
        val flagMock = mockk<Warmup>(relaxed = true)

        val checks = Checks("testChecks")

        val asyncWarmupCheck = AsyncWarmupCheck(
            WarmupCheck("name") {
                flagMock.run()
            },
        )
        checks.fireAndAddWarmUp(asyncWarmupCheck)

        coVerify(exactly = 1, timeout = 1000) { flagMock.run() }
        runBlocking { delay(1000L) }
        assertTrue(runBlocking { checks.check().result })
    }
}
