package com.hellofresh.cif.shutdown

import org.apache.logging.log4j.kotlin.Logging
import org.jetbrains.annotations.VisibleForTesting

// Thread safety guaranteed by mutex on ShutdownTasks
private var hookAdded = false

private object ShutdownTasks : Logging, MutableList<AutoCloseable> by mutableListOf()

fun <T : AutoCloseable> shutdownNeeded(fn: () -> T): T = fn().also(::shutdownHook)

@VisibleForTesting
fun executeShutdown() {
    synchronized(ShutdownTasks) {
        ShutdownTasks.logger.info("Shutdown initiated")
        ShutdownTasks.reverse()
        ShutdownTasks.forEach {
            synchronized(it) {
                it.close()
            }
        }
    }
}

fun <T : AutoCloseable> shutdownHook(autoCloseable: T): T = autoCloseable.also {
    synchronized(ShutdownTasks) {
        if (!hookAdded) {
            Runtime.getRuntime().addShutdownHook(
                Thread { executeShutdown() },
            )
            hookAdded = true
        }
        ShutdownTasks.add(it)
    }
}
