package com.hellofresh.cif.safetystock.reader

import com.hellofresh.cif.safetystock.model.FileType
import java.io.File
import java.nio.file.Files
import java.util.Random
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test

class CSVFileUploadReaderTest {

    @Test
    fun `returns accepted while processing file successfully`() {
        val forecast = readFileContent("ip_safety_stock/safety_stock_multiplier.csv")

        with(CSVFileUploadReader.toCsvRecords(forecast, FileType.SAFETY_STOCK_MULTIPLIER)) {
            assertTrue(parsedFile?.data?.isNotEmpty() ?: false)
            assertEquals(2, parsedFile?.data?.minOfOrNull { it.recordNumber })
            assertTrue(errors.isEmpty())
        }
    }

    @Test
    fun `returns accepted regardless of the file header order`() {
        val demand = readFileContent("ip_safety_stock/safety_stock_multiplier_different_header_order.csv")

        with(CSVFileUploadReader.toCsvRecords(demand, FileType.SAFETY_STOCK_MULTIPLIER)) {
            assertTrue(parsedFile?.data?.isNotEmpty() ?: false)
            assertTrue(errors.isEmpty())
        }
    }

    @Test
    fun `return bad request if file type is unknown or corrupted`() {
        val badContent = ByteArray(10).apply { Random().nextBytes(this) }
        with(CSVFileUploadReader.toCsvRecords(badContent, FileType.SAFETY_STOCK_MULTIPLIER)) {
            assertNull(parsedFile)
            assertTrue(errors.isNotEmpty())
        }
    }

    @Test
    fun `return bad request if file type has wrong delimiter`() {
        val recipe = readFileContent("ip_safety_stock/safety_stock_multiplier_wrong_delimiter.csv")
        with(CSVFileUploadReader.toCsvRecords(recipe, FileType.SAFETY_STOCK_MULTIPLIER)) {
            assertNull(parsedFile)
            assertTrue(errors.isNotEmpty())
        }
    }

    @Test
    fun `return bad request if file type has wrong headers`() {
        val forecast = readFileContent("ip_safety_stock/safety_stock_multiplier_wrong_headers.csv")

        with(CSVFileUploadReader.toCsvRecords(forecast, FileType.SAFETY_STOCK_MULTIPLIER)) {
            assertNull(parsedFile)
            assertTrue(errors.isNotEmpty())
        }
    }

    private fun readFileContent(fileName: String): ByteArray =
        with(this::class.java.classLoader) {
            File(getResource(fileName)!!.toURI())
        }.let {
            Files.readAllBytes(it.toPath())
        }
}
