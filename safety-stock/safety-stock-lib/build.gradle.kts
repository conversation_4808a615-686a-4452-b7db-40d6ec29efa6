plugins {
    id("com.hellofresh.cif.common-conventions")
    `test-functional`
    hellofresh.`test-fixtures`
    alias(libs.plugins.jooq)
}

description = "Safety Stock lib"
group = "$group.safetystocklib"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "safety_stocks|safety_stock_conf|sku_risk_rating"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    api(projects.distributionCenterModels)
    api(projects.lib.db)
    api(libs.apache.commonscsv)
    api(libs.apache.parquet.avro)
    api(libs.apache.avro.avro)
    api(libs.apache.hadoop.common)
    api(libs.apache.hadoop.client)

    testImplementation(projects.libTests)
    testImplementation(testFixtures(projects.distributionCenterModels))
}
