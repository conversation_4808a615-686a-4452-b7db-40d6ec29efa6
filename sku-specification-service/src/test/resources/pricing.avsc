{"name": "pricing", "namespace": "com.hellofresh.planning.suppliersku.pricing", "type": "record", "fields": [{"name": "event_type", "type": {"name": "event_types", "type": "enum", "symbols": ["entity_created", "entity_updated"]}}, {"name": "id", "type": {"type": "string", "logicalType": "uuid"}, "doc": "Global identifier for the pricing in procurement"}, {"name": "supplier_sku_id", "type": {"type": "string", "logicalType": "uuid"}, "doc": "Global unique identifier for supplierskus, common across all systems, and unique across all countries"}, {"name": "distribution_center", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "start_date", "type": "string", "default": ""}, {"name": "end_date", "type": "string", "default": ""}, {"name": "currency", "type": "string", "default": ""}, {"name": "price_type", "type": "string", "default": ""}, {"name": "price", "type": "int", "doc": "value of price multiplied by 10000 to accommodate for 4 decimal points"}, {"name": "units_per_case", "type": "int"}, {"name": "min_order_quantity", "type": "int"}, {"name": "buffer_percent", "type": "int", "doc": "value of buffer_percent multiplied by 100 to accommodate for 2 decimal points"}, {"name": "cases_per_pallet", "type": "int"}, {"name": "pallet_type", "type": "string"}, {"name": "supplier_packaging", "type": "string"}, {"name": "extra_cost_repacking", "type": "int", "doc": "value of extra_cost_repacking multiplied by 10000 to accommodate for 4 decimal points"}, {"name": "extra_cost_packaging", "type": "int", "doc": "value of extra_cost_packing multiplied by 10000 to accommodate for 4 decimal points"}, {"name": "extra_cost_labelling", "type": "int", "doc": "value of extra_cost_labelling multiplied by 10000 to accommodate for 4 decimal points"}, {"name": "extra_cost_storage", "type": "int", "doc": "value of extra_cost_storage multiplied by 10000 to accommodate for 4 decimal points"}, {"name": "extra_cost_freight", "type": "int", "doc": "value of extra_cost_freight multiplied by 10000 to accommodate for 4 decimal points"}, {"name": "extra_cost_tariff", "type": "int", "default": 0, "doc": "value of extra_cost_tariff multiplied by 10000 to accommodate for 4 decimal points"}, {"name": "conversion_rate", "type": "int", "doc": "value of conversion rate multiplied by 10000 to accommodate for 4 decimal points"}, {"name": "total_extra_costs", "type": "int", "default": 0, "doc": "value of sum of all extra costs multiplied by 10000 to accommodate for 4 decimal points"}, {"name": "total_price", "type": "double", "default": 0, "doc": "value of total price ((price + extra_costs)*conversion_rate)"}, {"name": "market", "type": "string"}, {"name": "created_by", "type": "string", "default": ""}, {"name": "updated_by", "type": "string", "default": ""}, {"name": "created_at", "type": "string"}, {"name": "updated_at", "type": "string"}, {"name": "enabled", "type": "boolean", "default": true}, {"name": "lead_time", "type": "int", "default": 0, "doc": "Material lead time in days (from ordering to delivery in HF location)"}, {"name": "incremental_order_quantity", "type": "int", "default": 0, "doc": "Incremental order quantity (above MOQ) defines the increment to increase the order once MOQ is met"}]}