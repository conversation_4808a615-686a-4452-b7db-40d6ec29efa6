package com.hellofresh.cif.skuspecification.supplierskupricing

import io.mockk.coVerify
import io.mockk.mockk
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition

class SupplierSkuPricingServiceTest {
    private val supplierSkuPricingRepositoryMock: SupplierSkuPricingRepository = mockk(relaxed = true)
    private val supplierSkuPricingService = SupplierSkuPricingService(supplierSkuPricingRepositoryMock)
    private val today = LocalDate.now()

    @Test
    fun `should save non null SupplierSku pricing records`() {
        // given
        val id = UUID.randomUUID()
        val leadTime = 9
        val supplierSkuPricing = SupplierSkuPricing(
            id,
            UUID.randomUUID(),
            leadTime,
            true,
            "ca",
            today,
            today.plusMonths(10)
        )

        // when
        runBlocking {
            supplierSkuPricingService.saveSupplierSkuPricing(
                ConsumerRecords(
                    mapOf(
                        TopicPartition("test", 0) to
                            listOf(ConsumerRecord("test", 0, 0, UUID.randomUUID().toString(), supplierSkuPricing))
                    )
                )
            )
        }

        // then
        coVerify { supplierSkuPricingRepositoryMock.saveSupplierSkuPricing(setOf(supplierSkuPricing)) }
    }
}
