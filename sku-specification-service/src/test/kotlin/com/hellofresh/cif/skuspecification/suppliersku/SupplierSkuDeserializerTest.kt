package com.hellofresh.cif.skuspecification.suppliersku

import com.hellofresh.cif.skuspecification.suppliersku.SupplierSkuAvro.avroRecordOf
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class SupplierSkuDeserializerTest {
    private val supplierSkuDeserializer = SupplierSkuDeserializer(SupplierSkuAvro.supplierSkuSchemaRegistryClient)

    @Test
    fun `should deserialize supplier sku avro value`() {
        // given
        val skuId = UUID.randomUUID()
        val supplierSkuId = UUID.randomUUID()
        val status = "Active"
        val mlorDays = 9
        val supplierSkuAvro = avroRecordOf(skuId, supplierSkuId, status, mlorDays)

        // when
        val supplierSkuResult = supplierSkuDeserializer.deserialize("test", supplierSkuAvro)

        // then
        assertNotNull(supplierSkuResult)
        assertEquals(skuId, supplierSkuResult.skuId)
        assertEquals(supplierSkuId, supplierSkuResult.supplierSkuId)
        assertEquals(status, supplierSkuResult.status)
        assertEquals(mlorDays, supplierSkuResult.mlor)
    }

    @Test
    fun `should deserialize into null when the supplierSku record has no mlor info`() {
        // given
        val supplierSkuAvroNoMlor = avroRecordOf(UUID.randomUUID(), UUID.randomUUID(), "Active", null)
        // when
        val supplierSkuResult = supplierSkuDeserializer.deserialize("test", supplierSkuAvroNoMlor)

        // then
        assertNull(supplierSkuResult)
    }
}
