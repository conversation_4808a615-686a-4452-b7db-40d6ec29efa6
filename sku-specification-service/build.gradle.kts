plugins {
    id("com.hellofresh.cif.application-conventions")
    alias(libs.plugins.jooq)
}

description = "Publishes the culnary sku's into sku specifications topic."
group = "$group.skuSpecification"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        includes = "supplier_sku|supplier_sku_pricing|sku_specification_yf|uom"
                        inputSchema = "public"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = true
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(projects.lib)
    implementation(projects.lib.models)
    implementation(projects.lib.db)
    implementation(libs.kafka.avro.serializer)
    implementation(projects.skuModels)
    implementation(libs.apache.commonscsv)

    testImplementation(libs.mockk)
    testImplementation(projects.libTests)
    testImplementation(projects.skuModels)
    testImplementation(libs.kafka.clients)
    testImplementation(libs.testcontainers.core)
    testImplementation(libs.testcontainers.kafka)
    testImplementation(libs.testcontainers.junit)
    testImplementation(libs.awaitility)

    modules {
        module("com.hellofresh:logging") {
            replacedBy("com.hellofresh.cif.lib:logging")
        }
    }
}
