package com.hellofresh.cif.scripts

import Arguments
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class ArgumentsTest {

    @ParameterizedTest
    @CsvSource(
        "--topic=public.sku-demand-forecast.v1,public.sku-demand-forecast.v1",
        "--bootstrap-servers=localhost:8080,localhost:8080",
    )
    fun `set arguments values when argument exist`(arg: String, value: String) {
        val args = arrayOf(arg)
        val arguments = Arguments(args)
        assertEquals(value, arguments[arg.split("=")[0]])
    }

    @ParameterizedTest
    @CsvSource(
        "--topic, --bootstrap-servers",
        "public.sku-demand-forecast.v1, localhost:8080"
    )
    fun `fail when argument list is malformed`(arg: String, value: String) {
        val args = arrayOf(arg, value)
        assertFailsWith<IllegalArgumentException> {
            Arguments(args)
        }
    }

    @ParameterizedTest
    @CsvSource(
        "--non-existent-option=public.sku-demand-forecast.v1,\"\""
    )
    fun `does not set arguments values when argument does not exist`(arg: String, value: String) {
        val args = arrayOf(arg, value)
        assertFailsWith<IllegalArgumentException> {
            Arguments(args)
        }
    }
}
