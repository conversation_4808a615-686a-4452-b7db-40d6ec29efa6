plugins {
    id("com.hellofresh.cif.application-conventions")
    alias(libs.plugins.shadow)
}

group = "$group.${project.name}"
description = "The module contains the scripts to be shared by the repository owners. Have to be executed only on the local environment"
extra["deploy"] = false

dependencies {
    implementation(projects.lib)
    implementation("org.jetbrains.kotlin:kotlin-script-runtime")
    implementation(libs.hellofresh.schemaregistry) {
        exclude(group = "com.google.api.grpc", module = "proto-google-common-protos")
    }
    implementation(libs.protobuf.grpc)
    implementation(libs.protobuf.java.util)
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs.add("-Xuse-fir-lt=false")
    }
}

tasks {
    withType<Jar>().configureEach {
        duplicatesStrategy = DuplicatesStrategy.INCLUDE
        manifest {
            attributes["Main-Class"] = "com.hellofresh.cif.scripts.Main"
        }
        from(sourceSets.main.get().output)

        dependsOn(configurations.runtimeClasspath)
        from(
            {
                configurations.runtimeClasspath.get().filter { it.name.endsWith("jar") }.map {
                    zipTree(it)
                }
            },
        )
    }
    shadowJar {
        transform(com.github.jengelman.gradle.plugins.shadow.transformers.Log4j2PluginsCacheFileTransformer::class.java)
    }
}
