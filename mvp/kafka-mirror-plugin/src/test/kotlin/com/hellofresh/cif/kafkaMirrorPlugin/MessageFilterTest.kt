@file:Suppress("DEPRECATION")

package com.hellofresh.cif.kafkaMirrorPlugin

import kafka.consumer.BaseConsumerRecord
import kotlin.test.Test
import kotlin.test.assertEquals
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.kafka.common.record.TimestampType

class MessageFilterTest {
    private val topic = "topic-name"
    private val partition = 0
    private val offset = 0L
    private val timestamp = 0L
    private val filterSubstr = "xxx"
    private val defaultKey = "some key"
    private val defaultValue = "some value"

    @Test fun `filter out messages by substring`() {
        val messageFilter = MessageFilter(filterSubstr)
        assertEquals(
            0,
            messageFilter.handle(createConsumerRecord(defaultKey, defaultValue)).size,
        )
    }

    @Test fun `keep message if key and-or value contains substring`() {
        val messageFilter = MessageFilter(filterSubstr)

        listOf(
            Pair("some key with $filterSubstr", defaultValue),
            Pair(defaultKey, "some val $filterSubstr"),
        ).forEach {
            val key = it.first
            val value = it.second
            val msgs = messageFilter.handle(createConsumerRecord(key, value))

            assertEquals(1, msgs.size)
            val msg = msgs.first()
            assertEquals(msg.key().decodeToString(), key)
            assertEquals(msg.value().decodeToString(), value)
        }
    }

    @Test fun `correctly handle null keys and values`() {
        val messageFilter = MessageFilter(filterSubstr)

        listOf(
            Pair("some key with $filterSubstr", null),
            Pair(null, "some val $filterSubstr"),
        ).forEach {
            val key = it.first
            val value = it.second
            assertEquals(1, messageFilter.handle(createConsumerRecord(key, value)).size)
        }
    }

    @Test fun `can filter by multiple substrings`() {
        val filter1 = "foo"
        val filter2 = "brra"
        val messageFilter = MessageFilter("$filter1 $filter2")

        listOf(
            Pair("some key with $filter1", null),
            Pair("some key with $filter2", null),
        ).forEach {
            val key = it.first
            val value = it.second
            assertEquals(1, messageFilter.handle(createConsumerRecord(key, value)).size)
        }
    }

    private fun createConsumerRecord(k: String?, v: String?) = BaseConsumerRecord(
        topic,
        partition,
        offset,
        timestamp,
        TimestampType.NO_TIMESTAMP_TYPE,
        k?.toByteArray(),
        v?.toByteArray(),
        RecordHeaders(),
    )
}
