import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("com.hellofresh.cif.common-conventions")
    alias(libs.plugins.shadow)
}

group = "$group.${project.name}"
description = "Set of filters for the Kafka Mirror"

dependencies {
    api(libs.kafka.kafka213)
}

tasks {
    withType<KotlinCompile>().configureEach {
        kotlinOptions {
            jvmTarget = JavaVersion.VERSION_1_8.toString()
            freeCompilerArgs = freeCompilerArgs - listOf(
                "-Xstring-concat=indy",
            )
        }
    }

    withType<JavaCompile>().configureEach {
        this.sourceCompatibility = JavaVersion.VERSION_1_8.toString()
    }
}
