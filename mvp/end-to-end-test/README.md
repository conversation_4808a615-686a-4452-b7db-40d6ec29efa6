# End-to-end tests

End-to-end test runs scenario on a target Kafka cluster and assert the expected
calculations in the target database. The target can be set by using variables.

## Variables

Following environment variables could be used to execute end-to-end tests

```shell
HF_PROFILES={staging,e2e,kafka or local,e2e,kafka}
HF_TIER={staging or local}
HF_AIVEN_PASSWORD={e2e_password}
```

The passwords could be found here -

- [e2e_password staging](https://vault.secrets.hellofresh.io/ui/vault/secrets/staging%2Fkey-value/kv/list?namespace=services%2Fcsku-inventory-forecast)

### How to set variables

> _How do I provide the password, profile, and tier?_

There are a few ways to do so listed below.

Environment variables:

```shell
export HF_PROFILES=qa
export HF_TIER=local
export HF_AIVEN_PASSWORD=secret
export HF_SCHEMA_REGISTRY_URL=https://kafka-live-hellofresh-live.aivencloud.com:23411
```

Java system properties:

```properties
hf.profiles=qa
hf.tier=local
hf.aiven.password=secret
schema-registry.url=https://kafka-live-hellofresh-live.aivencloud.com:23411
```

Programmatically:

```kotlin
fun main() {
  System.setProperty("hf.profiles", "qa")
  System.setProperty("hf.tier", "local")
  System.setProperty("hf.aiven.password", "secret")
  System.setProperty("schema-registry.url", "https://kafka-live-hellofresh-live.aivencloud.com:23411")

  runStreamsApplication { /* … */ }
}
```

You can also create an `application-private-$tier.properties` file in the
[kafka-streams library resources](lib/kafka-streams/src/main/resources) where
you place your passwords. Those files are [ignored by Git](.gitignore) and then
you only have to enable the `private` profile together with the `qa` profile
instead setting it separately. The following is an example for such a file:

```properties
aiven.password=secret
```

The set profiles to `qa,private` and it will be loaded automatically for either
`staging` or `live` depending on which tier you configured.

## How to run

### Locally

First, set the following variables up:

```shell
    # variables to run requred apps
    export HF_KAFKA_PASSWORD_LIVE=<secret>
    export HF_KAFKA_PASSWORD_STAGING=<secret>
    export HF_KAFKA_PASSWORD_READONLY_LIVE=<secret>
    export HF_KAFKA_PASSWORD_READONLY_STAGING=<secret>
    export HF_KAFKA_TRUSTSTORE_PASSWORD=<secret>
    # variables needed by the tests
    export HF_AIVEN_PASSWORD=$HF_KAFKA_PASSWORD_LIVE
    export HF_PROFILES=local,e2e,kafka
    export HF_TIER=local
    export HF_SCHEMA_REGISTRY_URL=https://kafka-live-hellofresh-live.aivencloud.com:23411
```

then run the make task:

```shell
    env=local make e2e-apps-up run-e2e-test local-down
```

### Against staging

Set the following variables up:

```shell
    export HF_AIVEN_PASSWORD={e2e_password}
    export HF_PROFILES=staging,e2e,kafka
    export HF_TIER=staging
```

Tip: to find correct values for `HF_AIVEN_PASSWORD`, check out 'Variables' section.

Execute one of the commands:

```shell
    make run-e2e-test
```

or

```shell
    ./gradlew :mvp:end-to-end-test:exec
```
