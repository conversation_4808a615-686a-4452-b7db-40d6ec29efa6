package com.hellofresh.cif.endToEndTest.kafka.producer

import com.fasterxml.jackson.databind.JsonNode
import com.google.type.Decimal
import com.hellofresh.cif.endToEndTest.SEND_TIMEOUT
import com.hellofresh.cif.endToEndTest.TOPIC_PURCHASE_ORDERS
import com.hellofresh.cif.endToEndTest.objectMapper
import com.hellofresh.cif.endToEndTest.testCases.TestCase
import com.hellofresh.cif.lib.kafka.serde.ProtoSerde
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderExpectedArrival
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderItem
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderNumber
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderRevision
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrderRevision.PurchaseOrderType
import java.time.OffsetDateTime
import java.util.Properties
import java.util.concurrent.TimeUnit
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.StringSerializer

class KafkaPurchaseOrderProtoProducer(properties: Properties) :
    KafkaProducer<String, PurchaseOrder>(
        properties,
        StringSerializer(),
        ProtoSerde<PurchaseOrder>().serializer(),
    ) {

    fun produceData(testCase: TestCase) {
        val json = objectMapper.readTree(testCase.purchaseOrder.value)

        val poNumber = testCase.purchaseOrder.key
        val po = createPo(json)

        send(
            ProducerRecord(
                TOPIC_PURCHASE_ORDERS,
                null,
                poNumber,
                po,
            ),
        ).get(SEND_TIMEOUT, TimeUnit.SECONDS)
    }

    private fun createPo(json: JsonNode): PurchaseOrder? =

        PurchaseOrder.newBuilder()
            .setRevision(
                PurchaseOrderRevision.newBuilder()
                    .setNumber(PurchaseOrderNumber.newBuilder().setFormatted(json["po_nr"].textValue()))
                    .setFormatted(json["po_nr"].textValue() + "_" + json["po_rev"].textValue())
                    .setType(PurchaseOrderType.PURCHASE_ORDER_TYPE_STANDARD)
                    .build(),
            ).setId(json["po_id"].textValue())
            .setDistributionCenterCode(json["dc_code"].textValue())
            .setStatus(PurchaseOrder.State.STATE_INITIATED)
            .setExpectedArrival(
                PurchaseOrderExpectedArrival.newBuilder()
                    .apply {
                        startTime = OffsetDateTime.parse(json["expectedArrivalStartTime"].textValue()).toProtoDate()
                        endTime = OffsetDateTime.parse(json["expectedArrivalEndTime"].textValue()).toProtoDate()
                    },
            )
            .setSupplierId(json["supplier_id"].textValue())
            .addOrderItems(
                PurchaseOrderItem.newBuilder()
                    .setSkuId(json["sku_id"].textValue())
                    .setQuantity(Decimal.newBuilder().setValue(json["qty"].intValue().toString()))
                    .setUnitPackaging(PurchaseOrderItem.UnitPackaging.newBuilder().build()),
            )
            .setUpdateTime(OffsetDateTime.parse(json["update_time"].textValue()).toProtoDate())
            .setSendTime(OffsetDateTime.parse(json["send_time"].textValue()).toProtoDate())
            .build()

    private fun OffsetDateTime.toProtoDate() =
        com.google.protobuf.Timestamp.newBuilder()
            .setSeconds(this.toEpochSecond())
            .setNanos(this.nano)
            .build()
}
