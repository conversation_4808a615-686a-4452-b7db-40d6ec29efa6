package com.hellofresh.cif.endToEndTest.db

import javax.sql.DataSource
import org.apache.logging.log4j.kotlin.Logging

class PurchaseOrderViewsRepository(private val dataSource: DataSource) {

    fun waitForPurchaseOrdersViewForPOInfo() {
        waitFor(
            "No purchase orders view record found",
            { refreshPurchaseOrdersView() },
            {
                dataSource.connection.use { conn ->
                    conn.createStatement()
                        .executeQuery(" select 1 from purchase_orders_view where po_ref is not null").next()
                }
            },
        )
        logger.info("Purchase orders view with PO information ready")
    }

    fun waitForPurchaseOrdersView() {
        waitFor(
            "No purchase orders view record found",
            { refreshPurchaseOrdersView() },
            {
                dataSource.connection.use { conn ->
                    conn.createStatement()
                        .executeQuery(
                            " select 1 from purchase_orders_view where po_ref is not null and grn_po_ref is not null"
                        ).next()
                }
            },
        )
        logger.info("Purchase orders view ready")
    }

    private fun refreshPurchaseOrdersView() {
        dataSource.connection.use { conn ->
            logger.info("Refreshing purchase orders view")
            conn.createStatement()
                .execute("refresh materialized view concurrently purchase_orders_view")
        }
    }

    companion object : Logging
}
