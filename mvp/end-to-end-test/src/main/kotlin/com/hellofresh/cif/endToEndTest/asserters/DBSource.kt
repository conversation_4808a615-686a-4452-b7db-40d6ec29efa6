package com.hellofresh.cif.endToEndTest.asserters

import com.hellofresh.cif.endToEndTest.asserters.DBSource.Companion.CalculationMode.PREPROD
import com.hellofresh.cif.endToEndTest.asserters.DBSource.Companion.CalculationMode.PROD
import com.hellofresh.cif.endToEndTest.model.DataRecord
import com.hellofresh.cif.endToEndTest.objectMapper
import java.sql.ResultSet.CONCUR_READ_ONLY
import java.sql.ResultSet.TYPE_FORWARD_ONLY
import java.time.Duration
import java.time.Instant
import javax.sql.DataSource
import kotlin.time.toKotlinDuration
import kotlinx.coroutines.delay
import org.apache.logging.log4j.kotlin.Logging
import org.intellij.lang.annotations.Language
import org.postgresql.jdbc.PgResultSetMetaData

data class Diff(val name: String, val expected: Any?, val actual: Any?)

private const val DELAY_BETWEEN_POLLS = 5L

class DBSource(private val dataSource: DataSource, private val calculationMode: CalculationMode) {
    // Since our DB eventually gets consistent, this test retry for the given key for 60s before failing.
    suspend fun assertEventually(
        expected: DataRecord<String, String>,
        timeout: Duration = Duration.ofSeconds(ASSERTION_TIMEOUT_SECONDS),
    ) {
        val fieldsToIgnore = setOf("issued_at")
        val startTime = Instant.now()

        val expectedKey = objectMapper.readTree(expected.key)
        val cskuId = expectedKey["csku_id"].asText()
        val date = expectedKey["date"].asText()

        var diffs: MutableList<Diff>
        var actualRecord: Map<String, Any?>? = null

        do {
            diffs = mutableListOf()
            actualRecord = getData(cskuId, date) ?: continue

            logger.info("Checking $calculationMode record: $actualRecord")

            objectMapper.readTree(expected.value).fieldNames().forEach { name ->
                val expected = objectMapper.readTree(expected.value).get(name)
                if (!fieldsToIgnore.contains(name) && (!actualRecord.containsKey(name) || actualRecord[name] != expected.textValue())) {
                    diffs.add(Diff(name, expected, actualRecord[name]))
                }
            }

            if (diffs.size == 0) return
            delay(Duration.ofSeconds(DELAY_BETWEEN_POLLS).toKotlinDuration())
        } while (Duration.between(startTime, Instant.now()) < timeout)

        val msg = if (actualRecord == null) {
            "Unable to find the expected $calculationMode record for $cskuId and $date"
        } else {
            "Found ${diffs.size} $calculationMode difference(s) for $cskuId and $date: $diffs"
        }
        logger.info(msg)
        throw AssertionError(msg)
    }

    @Suppress("NestedBlockDepth")
    private fun getData(cskuId: String, date: String): Map<String, Any?>? =
        dataSource.connection.use { conn ->
            conn.autoCommit = false
            conn.createStatement(TYPE_FORWARD_ONLY, CONCUR_READ_ONLY).use { stmt ->
                stmt.executeQuery(sql(calculationMode).format(cskuId, date)).use { rs ->
                    (rs.takeIf { it.next() }?.metaData as PgResultSetMetaData?)?.let { meta ->
                        meta.columnCount.downTo(1).associate { i ->
                            meta.getColumnName(i) to rs.getString(i)
                        }
                    }
                }
            }
        }

    companion object : Logging {
        private const val ASSERTION_TIMEOUT_SECONDS = 120L

        @Language("PostgreSQL")
        private fun sql(calculationMode: CalculationMode): String {
            val table = when (calculationMode) {
                PROD -> "calculation"
                PREPROD -> "pre_production_calculation"
            }
            return "SELECT * FROM $table WHERE csku_id = '%s' AND date = '%s'"
        }

        enum class CalculationMode {
            PROD,
            PREPROD
        }
    }
}
