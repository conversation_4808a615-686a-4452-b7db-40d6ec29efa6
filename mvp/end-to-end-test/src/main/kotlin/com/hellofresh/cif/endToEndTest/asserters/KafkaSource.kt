package com.hellofresh.cif.endToEndTest.asserters

import java.time.Duration
import java.util.Properties
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.serialization.Serde
import org.apache.logging.log4j.kotlin.Logging
import org.awaitility.Awaitility.await
import org.awaitility.core.ConditionTimeoutException

private const val ASSERTION_TIMEOUT_IN_SECONDS = 720L
private const val KAFKA_POLLING_TIMEOUT_IN_SECONDS = 5L

class KafkaSource<K, V>(
    private val topic: String,
    properties: Properties,
    serdeKey: Serde<K>,
    serdeValue: Serde<V>
) : Logging {
    private val consumer = KafkaConsumer(
        properties,
        serdeKey.deserializer(),
        serdeValue.deserializer(),
    )

    init {
        logger.info("Kafka source consumer group id. [id=${properties[ConsumerConfig.GROUP_ID_CONFIG]}]")
        val partitions = consumer.partitionsFor(topic).map { TopicPartition(topic, it.partition()) }
        consumer.assign(partitions)
        consumer.endOffsets(partitions).forEach { logger.info("Partition Assigned: ${it.key}, Offset:. ${it.value}") }
        consumer.seekToEnd(partitions)
        // Don't delete this, seekToEnd is lazily evaluated.
        consumer.poll(Duration.ofSeconds(KAFKA_POLLING_TIMEOUT_IN_SECONDS))
    }

    fun assertEventually(
        expected: Map<K, V>,
        timeout: Duration = Duration.ofSeconds(ASSERTION_TIMEOUT_IN_SECONDS),
    ) {
        val allRecords = mutableMapOf<K, V>()

        try {
            await()
                .atMost(timeout)
                .until { assertRecords(allRecords, expected) }
        } catch (e: ConditionTimeoutException) {
            logger.warn("Failed to assert. [\n\tallRecords=$allRecords\n\texpected=$expected]")
            throw AssertionError("Failed to assert the expected kafka messages", e)
        }
    }

    private fun assertRecords(
        allRecords: MutableMap<K, V>,
        expected: Map<K, V>
    ): Boolean {
        logger.info { "$topic records consumer ${allRecords.count()}." }
        val timeout = Duration.ofSeconds(KAFKA_POLLING_TIMEOUT_IN_SECONDS)
        val records = consumer.poll(timeout)
        allRecords.putAll(records.map { it.key() to it.value() })
        return if (allRecords.keys.containsAll(expected.keys)) {
            val actualValues = allRecords
                .filter { expected.keys.contains(it.key) }
                .map { it.value }
                .toSet()

            expected.values.toSet() == actualValues
        } else {
            false
        }
    }
}
