package com.hellofresh.cif.endToEndTest.kafka.producer

import com.google.type.DateTime
import com.google.type.Decimal
import com.google.type.TimeZone
import com.hellofresh.cif.endToEndTest.SEND_TIMEOUT
import com.hellofresh.cif.endToEndTest.TOPIC_GOODS_RECEIVED_NOTES_TOPIC
import com.hellofresh.cif.endToEndTest.objectMapper
import com.hellofresh.cif.endToEndTest.testCases.TestCase
import com.hellofresh.cif.lib.kafka.serde.ProtoSerde
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteKey
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryLineState
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryLineState.DELIVERY_LINE_STATE_CLOSED
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDelivery
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.UnitOfMeasure.UNIT_OF_MEASURE_UNIT
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.Properties
import java.util.concurrent.TimeUnit
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.header.internals.RecordHeader
import org.apache.kafka.common.header.internals.RecordHeaders

const val WMS_HEADER_NAME = "wmsName"
const val WMS_HEADER_VALUE_FCMS = "FCMS"

class KafkaGRNProtoProducer(properties: Properties) :
    KafkaProducer<GoodsReceivedNoteKey, GoodsReceivedNoteValue>(
        properties,
        ProtoSerde<GoodsReceivedNoteKey>().serializer(),
        ProtoSerde<GoodsReceivedNoteValue>().serializer(),
    ) {
    @Suppress("MagicNumber")
    fun produceGRNData(testCase: TestCase) {
        val json = objectMapper.readTree(testCase.goodsReceivedNote.value)

        val quantity = json["quantity"].longValue()
        val poRef = json["po_ref"].textValue()

        val closedDelivery = newPoDelivery(
            testCase.date,
            deliveries = listOf(
                PoDeliveryLine(testCase.testCskuCode, quantity, DELIVERY_LINE_STATE_CLOSED),
            ),
        )
        val grnKey = GoodsReceivedNoteKey.newBuilder()
            .setDcCode(testCase.dcCode)
            .setReference(poRef)
            .build()
        val grnValue = GoodsReceivedNoteValue.newBuilder()
            .setDcCode(testCase.dcCode)
            .addAllDeliveries(listOf(closedDelivery))
            .build()
        val headers = RecordHeaders(listOf(RecordHeader(WMS_HEADER_NAME, WMS_HEADER_VALUE_FCMS.toByteArray())))
        send(
            ProducerRecord(
                TOPIC_GOODS_RECEIVED_NOTES_TOPIC,
                null,
                grnKey,
                grnValue,
                headers,
            ),
        ).get(SEND_TIMEOUT, TimeUnit.SECONDS)
    }

    private fun newPoDelivery(
        poDate: LocalDate,
        expectedDeliveryStartTime: LocalDate = poDate,
        deliveries: List<PoDeliveryLine>
    ): PurchaseOrderDelivery {
        val deliveryLines = deliveries.map {
            newPoDeliveryLine(it.skuCode, it.qty, it.deliveryLineState)
        }

        return PurchaseOrderDelivery.newBuilder()
            .setDeliveryTime(
                DateTime.newBuilder()
                    .setYear(poDate.year)
                    .setMonth(poDate.monthValue)
                    .setDay(poDate.dayOfMonth)
                    .setTimeZone(TimeZone.newBuilder().setId("UTC").build())
                    .build(),
            )
            .setExpectedDeliveryStartTime(
                DateTime.newBuilder()
                    .setYear(expectedDeliveryStartTime.year)
                    .setMonth(expectedDeliveryStartTime.monthValue)
                    .setDay(expectedDeliveryStartTime.dayOfMonth)
                    .setTimeZone(TimeZone.newBuilder().setId(ZoneOffset.UTC.id).build())
                    .build(),
            )
            .addAllLines(deliveryLines)
            .build()
    }

    private fun newPoDeliveryLine(skuCode: String, qty: Long, deliveryLineState: DeliveryLineState) =
        GoodsReceivedNoteValue.PurchaseOrderDeliveryLine.newBuilder()
            .setSkuCode(skuCode)
            .setSkuUom(UNIT_OF_MEASURE_UNIT)
            .setState(deliveryLineState)
            .setPalletizedQuantity(Decimal.newBuilder().setValue(qty.toString()))
            .build()

    private data class PoDeliveryLine(
        val skuCode: String,
        val qty: Long,
        val deliveryLineState: DeliveryLineState,
    )
}
