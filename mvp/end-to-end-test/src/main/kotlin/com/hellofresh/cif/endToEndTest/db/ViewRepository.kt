package com.hellofresh.cif.endToEndTest.db

import java.time.Duration
import java.time.Instant
import kotlin.time.toKotlinDuration
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

@SuppressWarnings("MagicNumber")
private val defaultTimeout = Duration.ofSeconds(60)
private val defaultDelay = Duration.ofSeconds(2).toKotlinDuration()

fun waitFor(
    messageError: String,
    refreshFn: () -> Unit,
    fn: () -> <PERSON><PERSON><PERSON>,
    timeout: Duration = defaultTimeout,
    delay: kotlin.time.Duration = defaultDelay
) {
    val startTime = Instant.now()
    do {
        if (fn()) {
            return
        } else {
            runBlocking { delay(delay) }.also { refreshFn() }
        }
    } while (Duration.between(startTime, Instant.now()) < timeout)
    throw AssertionError(messageError)
}
