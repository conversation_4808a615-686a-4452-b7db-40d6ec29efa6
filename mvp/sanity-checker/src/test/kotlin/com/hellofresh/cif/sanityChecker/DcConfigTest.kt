package com.hellofresh.cif.sanityChecker

import InfraPreparation.getMigratedDataSource
import com.zaxxer.hikari.HikariDataSource
import io.github.resilience4j.core.IntervalFunction
import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import java.time.DayOfWeek
import java.time.Duration
import java.time.ZoneId
import java.util.concurrent.Executors
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration

private val retry = RetryRegistry.of(
    RetryConfig.custom<Any>()
        .maxAttempts(MAX_DATABASE_CALLS_RETRIES)
        .intervalFunction(
            IntervalFunction.ofExponentialBackoff(
                Duration.ofMillis(INITIAL_INTERVAL_MS_DATABASE_CALLS),
                2.0,
            ),
        )
        .failAfterMaxAttempts(true)
        .build(),
)
    .retry("sanity-checker-database-fetch")

class DcConfigTest {
    private val datasource: HikariDataSource =
        getMigratedDataSource(nestedFolderCount = 2) as HikariDataSource

    private val dsl = DSL.using(
        DefaultConfiguration()
            .apply {
                setSQLDialect(SQLDialect.POSTGRES)
                setDataSource(datasource)
                setExecutor(Executors.newSingleThreadExecutor())
            },
    )

    @BeforeTest
    fun setUp() {
        datasource.connection.use { conn ->
            conn.createStatement().execute(
                """
                INSERT INTO dc_config (dc_code, market, production_start, cleardown,zone_id, enabled, record_timestamp,
                created_at, updated_at, has_cleardown)
                VALUES (
                    'VE', 'DACH','FRIDAY', 'FRIDAY', 'Europe/Berlin', true, now(), now(),now(), true
                )
                """,
            )
        }
    }

    @AfterTest
    fun tearDown() {
        datasource.close()
    }

    @Test
    fun `should load the DC's`() = runBlocking {
        val expectedDcCode = "VE"
        DcConfig.loadDCs(retry, dsl)
        assertEquals(1, DcConfig.dcStore.size)
        assertTrue(DcConfig.dcStore.containsKey(expectedDcCode))
        DcConfig.dcStore[expectedDcCode]?.let { dcConfig ->
            with(dcConfig) {
                assertEquals(DayOfWeek.FRIDAY, productionStart)
                assertEquals(DayOfWeek.FRIDAY, cleardown)
                assertEquals("DACH", market)
                assertEquals(ZoneId.of("Europe/Berlin"), zoneId)
                assertTrue(enabled)
                assertTrue(hasCleardown)
            }
        }
    }
}
