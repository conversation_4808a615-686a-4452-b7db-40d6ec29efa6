package com.hellofresh.cif.sanityChecker

import com.hellofresh.cif.sanityChecker.models.fetchData
import com.hellofresh.cif.sanityChecker.models.fetchDataList
import com.zaxxer.hikari.HikariDataSource
import io.github.resilience4j.core.IntervalFunction
import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import io.mockk.every
import io.mockk.mockk
import java.io.IOException
import java.sql.Connection
import java.sql.ResultSet
import java.time.Duration
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

const val MAX_DATABASE_CALLS_RETRIES = 3
const val INITIAL_INTERVAL_MS_DATABASE_CALLS = 100L
private val retry = RetryRegistry.of(
    RetryConfig.custom<Any>()
        .maxAttempts(MAX_DATABASE_CALLS_RETRIES)
        .intervalFunction(
            IntervalFunction.ofExponentialBackoff(
                Duration.ofMillis(INITIAL_INTERVAL_MS_DATABASE_CALLS),
                2.0,
            ),
        )
        .failAfterMaxAttempts(true)
        .build(),
)
    .retry("sanity-checker-database-fetch")

private val dataSource = mockk<HikariDataSource>(relaxed = true)
private val connection = mockk<Connection>(relaxed = true)

class HikariDataSourceTest {
    @ParameterizedTest
    @MethodSource("fetchData")
    fun `test fetchData retries on SQLException`(fetchData: (String, (ResultSet) -> Pair<Any, Any>?) -> Unit) {
        // given
        every { dataSource.connection } returns connection
        every { connection.autoCommit } returns false
        every {
            connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY)
        } throws IOException("db unavailable")

        val processResultSet: (ResultSet) -> Pair<Any, Any>? = { null }

        // when
        val exception = assertThrows<IOException> {
            fetchData("SELECT * FROM table", processResultSet)
        }
        // then
        assertEquals("db unavailable", exception.message)
    }

    companion object {
        @JvmStatic
        fun fetchData() = listOf(
            { query: String, processResultSet: (ResultSet) -> Pair<Any, Any>? ->
                dataSource.fetchData(retry, query, processResultSet)
            },
            { query: String, processResultSet: (ResultSet) -> Pair<Any, Any>? ->
                dataSource.fetchDataList(retry, query, processResultSet)
            },
        )
    }
}
