package com.hellofresh.cif.sanityChecker

import com.hellofresh.cif.sanityChecker.models.Dimension
import com.hellofresh.cif.sanityChecker.models.Quantity
import kotlin.math.abs

private fun ignoreSkuPrefix(skuCode: String) = setOf("XYZ", "TEST").any { skuCode.startsWith(it) }
private const val LIMIT = 100

@Suppress("LongMethod")
fun compare(
    source: String,
    metric: ComparisonMetrics,
    sourceOfTruth: Map<Dimension, Quantity>,
    cifData: Map<Dimension, Quantity>
) {
    val comparison = mutableMapOf<ComparisonKey, Comparison>()
    cifData.forEach { (k, _) ->
        val comparisonKey = ComparisonKey(k.dcCode, k.productionWeek)
        comparison.putIfAbsent(comparisonKey, Comparison())
        val comp = comparison[comparisonKey]!!
        comp.totalCifCount++
        val sourceOfTruthData = sourceOfTruth[k]
        if (sourceOfTruthData == null) {
            comp.missedSourceOfTruthCount++
            comp.missingSourceOfTruthSkus.add(k.sku)
        }
    }

    sourceOfTruth.forEach { (k, v) ->
        val comparisonKey = ComparisonKey(k.dcCode, k.productionWeek)
        comparison.putIfAbsent(comparisonKey, Comparison())
        val comp = comparison[comparisonKey]!!
        comp.totalSourceOfTruthCount++
        if (ignoreSkuPrefix(k.sku)) {
            return@forEach
        }
        val cifValue = cifData[k]
        if (cifValue == null) {
            comp.missedCifCount++
            comp.missingCifSkus.add(k.sku)
            return@forEach
        }
        val mismatch = abs(cifValue - v)
        if (mismatch != 0L) {
            comp.mismatchCount++
            comp.totalMismatch += mismatch
            comp.mismatchingSkus.add(
                MismatchingSku(k.sku, v, cifValue),
            )
        } else {
            comp.matches++
        }
    }

    comparison.forEach { (key, comp) ->
        metric.set(source, key.dcCode, key.productionWeek.orEmpty(), comp)
        val logPrefix =
            """

                DC: ${key.dcCode}
                Week: ${key.productionWeek ?: "N/A"}
                Dimension: $source

            """.trimIndent()
        log.info(
            logPrefix +
                """
                Count in SoT: ${comp.totalSourceOfTruthCount}
                Count in CIF: ${comp.totalCifCount}
                Number of Matches: ${comp.matches}
                """.trimIndent(),
        )
        if (comp.mismatchingSkus.isNotEmpty()) {
            log.info(
                logPrefix +
                    """
                    Number of Mismatches = ${comp.mismatchCount}
                    Amount of Mismatches = ${comp.totalMismatch}
                    Mismatching (first $LIMIT) : ${comp.mismatchingSkus.take(LIMIT)}
                    """.trimIndent(),
            )
        }
        if (comp.missingCifSkus.isNotEmpty()) {
            log.info(
                logPrefix +
                    """
                    Number of Misses in CIF = ${comp.missedCifCount}
                    Missing (first $LIMIT) : ${comp.missingCifSkus.take(LIMIT)}
                    """.trimIndent(),
            )
        }
        if (comp.missingSourceOfTruthSkus.isNotEmpty()) {
            log.info(
                logPrefix +
                    """
                    Number of Misses in SoT = ${comp.missedSourceOfTruthCount}
                    Missing (first $LIMIT) : ${comp.missingSourceOfTruthSkus.take(LIMIT)}
                    """.trimIndent(),
            )
        }
    }

    metric.push(source)
}

data class ComparisonKey(
    val dcCode: String,
    val productionWeek: String? = null,
)

@Suppress("DataClassShouldBeImmutable")
data class Comparison(
    val missingCifSkus: MutableList<String> = mutableListOf(),
    val missingSourceOfTruthSkus: MutableList<String> = mutableListOf(),
    val mismatchingSkus: MutableList<MismatchingSku> = mutableListOf(),
    var totalCifCount: Int = 0,
    var totalSourceOfTruthCount: Int = 0,
    var missedCifCount: Int = 0,
    var missedSourceOfTruthCount: Int = 0,
    var mismatchCount: Int = 0,
    var totalMismatch: Long = 0,
    var matches: Long = 0,
)

data class MismatchingSku(
    val skuCode: String,
    val sourceOfTruthQty: Quantity,
    val cifQty: Quantity,
) {
    override fun toString() = "(skuCode=$skuCode, sourceOfTruthQty=$sourceOfTruthQty, cifQty=$cifQty)"
}
