package com.hellofresh.cif.sanityChecker

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.inventory.InventoryRepositoryImpl
import com.hellofresh.cif.inventory.LiveInventoryRepositoryImpl
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.sumOf
import com.hellofresh.cif.sanity_checker.schema.Tables
import com.hellofresh.cif.sanity_checker.schema.tables.records.DcConfigRecord
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.LocationType
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.prometheus.client.Gauge
import io.prometheus.client.exporter.PushGateway
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import org.jooq.DSLContext
import org.jooq.Result

private val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()
suspend fun matchLiveInventory(
    prometheus: PushGateway,
    dslContext: DSLContext
) {
    val liveInventoryMetrics = LiveInventoryMetrics(prometheus)

    val now = OffsetDateTime.now(ZoneOffset.UTC)
    val dcConfigs = dslContext.selectFrom(Tables.DC_CONFIG).where(Tables.DC_CONFIG.ENABLED.isTrue).fetch()

    val inventoryRepository = InventoryRepositoryImpl(dslContext.withMetrics(SimpleMeterRegistry()))
    val liveInventoryRepository = LiveInventoryRepositoryImpl(dslContext.withMetrics(SimpleMeterRegistry()))

    val (inventorySnapshots, liveInventorySnapshots) = fetchSnapshotsData(
        dcConfigs,
        inventoryRepository,
        now,
        liveInventoryRepository,
    )

    dcConfigs.forEach { dc ->

        val inventorySnapshot = inventorySnapshots[dc.dcCode]
        val liveInventorySnapshot = liveInventorySnapshots[dc.dcCode]
        if (inventorySnapshot != null || liveInventorySnapshot != null) {
            val snapshotComparison =
                if (inventorySnapshot != null && liveInventorySnapshot != null) {
                    compareSnapshots(inventorySnapshot, liveInventorySnapshot)
                } else if (inventorySnapshot != null) {
                    log.info(
                        "No Live Inventory snapshot to compare with. DC: ${inventorySnapshot.dcCode} " +
                            "Date: ${inventorySnapshot.snapshotTime.toLocalDate()}",
                    )
                    SnapshotComparison(inventorySnapshot.dcCode, emptyList(), inventorySnapshot.skus, emptyList())
                } else {
                    log.info(
                        "No Inventory snapshot to compare with. DC: ${liveInventorySnapshot!!.dcCode} " +
                            "Date: ${liveInventorySnapshot.snapshotTime.toLocalDate()}",
                    )
                    SnapshotComparison(
                        liveInventorySnapshot.dcCode,
                        liveInventorySnapshot.skus,
                        emptyList(),
                        emptyList(),
                    )
                }

            pushMetrics(liveInventoryMetrics, snapshotComparison)
        }
    }
}

private fun pushMetrics(liveInventoryMetrics: LiveInventoryMetrics, snapshotComparison: SnapshotComparison) {
    val dcCode = snapshotComparison.dcCode
    if (snapshotComparison.missingSnapshotSkus.isNotEmpty()) {
        log.info(
            "DC: $dcCode Missing Inventory Snapshots Skus: ${
                objectMapper.writeValueAsString(
                    snapshotComparison.missingSnapshotSkus,
                )
            }",
        )

        liveInventoryMetrics.setSkuComparison(dcCode, "missing_snapshot", snapshotComparison.missingSnapshotSkus.size)
    }

    if (snapshotComparison.missingLiveSnapshotSkus.isNotEmpty()) {
        log.info(
            "DC: $dcCode Missing Live Inventory Snapshots Skus: ${
                objectMapper.writeValueAsString(
                    snapshotComparison.missingLiveSnapshotSkus,
                )
            }",
        )

        liveInventoryMetrics.setSkuComparison(
            dcCode,
            "missing_live_snapshot",
            snapshotComparison.missingLiveSnapshotSkus.size,
        )
    }
    if (snapshotComparison.skuComparisons.isNotEmpty()) {
        addSkuComparison(liveInventoryMetrics, snapshotComparison)
    }

    liveInventoryMetrics.push()
}

private fun addSkuComparison(liveInventoryMetrics: LiveInventoryMetrics, snapshotComparison: SnapshotComparison) {
    val dcCode = snapshotComparison.dcCode
    var mismatchCount = 0
    snapshotComparison.skuComparisons.map {
        if (it.snapshot != it.liveSnapshot) {
            log.info(
                "DC: $dcCode Mismatch Sku ${it.skuId}: Live Inventory${
                    objectMapper.writeValueAsString(
                        it.liveSnapshot,
                    )
                }",
            )
            log.info(
                "DC: $dcCode Mismatch Sku ${it.skuId}: Inventory${objectMapper.writeValueAsString(it.snapshot)}",
            )
            mismatchCount += 1
        }
        it.snapshot to it.liveSnapshot
    }.reduce { acc, pair ->
        aggregateLocationQuantities(acc.first, pair.first) to aggregateLocationQuantities(acc.second, pair.second)
    }.also { (inventory, liveInventory) ->
        liveInventoryMetrics.setSkuComparison(dcCode, "mismatch_sku", mismatchCount)
        inventory.forEach { (locationType, total) ->
            liveInventoryMetrics.setInventory(
                dcCode,
                locationType,
                total.getValue(),
            )
        }
        liveInventory.forEach { (locationType, total) ->
            liveInventoryMetrics.setLiveInventory(
                dcCode,
                locationType,
                total.getValue(),
            )
        }
    }
}

private fun aggregateLocationQuantities(
    quantities1: Map<LocationType, SkuQuantity>,
    quantities2: Map<LocationType, SkuQuantity>
) =
    (quantities1.entries.toList() + quantities2.entries.toList())
        .groupBy({ it.key }) { it.value }
        .mapValues { it.value.sumOf { quantity -> quantity } }

private fun compareSnapshots(inventorySnapshot: InventorySnapshot, liveInventorySnapshot: LiveInventorySnapshot): SnapshotComparison {
    val missingSnapshotSkus = mutableListOf<SkuLiveInventory>()
    val missingLiveSnapshotSkus = mutableListOf<SkuInventory>()
    val skuComparisons = mutableListOf<SkuInventoryComparison>()
    val skus = inventorySnapshot.skus.associateBy { it.skuId }
    val liveSkus = liveInventorySnapshot.skus.associateBy { it.skuId }
        .asSequence()
        .map { (k, v) -> k to v.copy(inventory = v.inventory.filter { it.qty.getValue() != BigDecimal.ZERO }) }
        .filter { (_, v) -> v.inventory.isNotEmpty() }
        .toMap()
    (skus.keys + liveSkus.keys).forEach { skuId ->
        if (skus.contains(skuId) && !liveSkus.contains(skuId)) {
            missingLiveSnapshotSkus.add(skus[skuId]!!)
        } else if (!skus.contains(skuId) && liveSkus.contains(skuId)) {
            missingSnapshotSkus.add(liveSkus[skuId]!!)
        } else {
            skuComparisons.add(
                toSkuInventoryComparison(skuId, skus[skuId]!!, liveSkus[skuId]!!),
            )
        }
    }
    return SnapshotComparison(inventorySnapshot.dcCode, missingSnapshotSkus, missingLiveSnapshotSkus, skuComparisons)
}

private suspend fun fetchSnapshotsData(
    dcConfig: Result<DcConfigRecord>,
    inventoryRepository: InventoryRepositoryImpl,
    now: OffsetDateTime,
    liveInventoryRepository: LiveInventoryRepositoryImpl
) =
    dcConfig.groupBy({ ZoneId.of(it.zoneId) }) { it.dcCode }
        .map { (zoneId, dcCodes) ->

            val todayDateRange = DateRange.oneDay(now.atZoneSameInstant(zoneId).toLocalDate())
            val liveSnapshots = liveInventoryRepository.fetchBy(dcCodes.toSet(), todayDateRange)
                .associateBy { it.dcCode }

            val snapshots = inventoryRepository.fetchBy(dcCodes.toSet(), todayDateRange)
                .associateBy { it.dcCode }

            snapshots to liveSnapshots
        }.reduce { acc, pair ->
            (acc.first + pair.first) to (acc.second + pair.second)
        }

private fun toSkuInventoryComparison(skuId: UUID, skuInventory: SkuInventory, skuLiveInventory: SkuLiveInventory) =
    SkuInventoryComparison(
        skuId,
        skuInventory.inventory.groupBy { it.location.type }
            .mapValues {
                it.value.sumOf { inventory ->
                    inventory.qty
                }
            },
        skuLiveInventory.inventory.groupBy { it.location.type }
            .mapValues {
                it.value.sumOf { inventory ->
                    inventory.qty
                }
            },
    )

private data class SnapshotComparison(
    val dcCode: String,
    val missingSnapshotSkus: List<SkuLiveInventory>,
    val missingLiveSnapshotSkus: List<SkuInventory>,
    val skuComparisons: List<SkuInventoryComparison>
)

private data class SkuInventoryComparison(
    val skuId: UUID,
    val snapshot: Map<LocationType, SkuQuantity>,
    val liveSnapshot: Map<LocationType, SkuQuantity>
)

private class LiveInventoryMetrics(pushGateway: PushGateway) {

    private val source = "live_inventory"
    private val prometheusClient = PrometheusClient(pushGateway)

    private val skuComparisonMetric = Gauge.build(
        "procurement_csku_inventory_forecast_sanity_checker_live_inventory_comparison_skus",
        "Measuring sku results of comparison between live inventory snapshot and inventory snapshots in CIF",
    ).labelNames("dc", "type").create()

    private val totalQuantities = Gauge.build(
        "procurement_csku_inventory_forecast_sanity_checker_live_inventory_comparison_total",
        "Measuring total inventory quantities by location type between live inventory snapshot and inventory snapshots in CIF",
    ).labelNames("dc", "inventorySource", "locationType").create()

    fun setSkuComparison(dcCode: String, type: String, count: Int) =
        skuComparisonMetric.labels(dcCode, type).set(count.toDouble())

    fun setInventory(dcCode: String, locationType: LocationType, count: BigDecimal) =
        totalQuantities.labels(dcCode, "inventory", locationType.toMetricType()).set(count.toDouble())

    fun setLiveInventory(dcCode: String, locationType: LocationType, count: BigDecimal) =
        totalQuantities.labels(dcCode, "liveInventory", locationType.toMetricType()).set(count.toDouble())

    private fun LocationType.toMetricType() = if (isStaging()) "staging" else "storage"
    fun push() {
        prometheusClient.push(skuComparisonMetric, source)
        prometheusClient.push(totalQuantities, source)
    }
}
