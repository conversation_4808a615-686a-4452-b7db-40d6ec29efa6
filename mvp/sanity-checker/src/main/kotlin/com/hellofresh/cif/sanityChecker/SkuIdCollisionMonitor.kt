package com.hellofresh.cif.sanityChecker

import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.sanity_checker.schema.Tables.SKU_SPECIFICATION
import com.hellofresh.cif.sanity_checker.schema.Tables.SKU_SPECIFICATION_YF
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.prometheus.client.Gauge
import io.prometheus.client.exporter.PushGateway
import kotlinx.coroutines.future.await
import org.jooq.DSLContext
import org.jooq.impl.DSL

suspend fun monitorSkuIdCollision(
    prometheus: PushGateway,
    dslContext: DSLContext,
) {
    val mx = dslContext.withMetrics(SimpleMeterRegistry())
    val idCollisionsQuery = dslContext
        .selectCount()
        .from(SKU_SPECIFICATION)
        .innerJoin(SKU_SPECIFICATION_YF)
        .on(SKU_SPECIFICATION.ID.eq(SKU_SPECIFICATION_YF.ID))

    val codeMarketCollisionsQuery = dslContext
        .selectCount()
        .from(SKU_SPECIFICATION)
        .innerJoin(SKU_SPECIFICATION_YF)
        .on(
            SKU_SPECIFICATION.CODE.eq(SKU_SPECIFICATION_YF.CODE)
                .and(SKU_SPECIFICATION.MARKET.eq(SKU_SPECIFICATION_YF.MARKET))
        )

    val query = dslContext
        .select(
            DSL.field(idCollisionsQuery).`as`("id_collisions"),
            DSL.field(codeMarketCollisionsQuery).`as`("code_market_collisions"),
        )

    val result = mx
        .withTagName("SkuIdCollisionMonitor")
        .fetchAsync(query)
        .await()

    val (idCollision, codeCollision) = if (result.size > 0) {
        val record = result.first()
        Pair(
            record.get("id_collisions").toString().toDouble(),
            record.get("code_market_collisions").toString().toDouble()
        )
    } else {
        Pair(0.0, 0.0)
    }

    if (idCollision > 0) {
        log.info("SkuIdCollisionMonitor: id_collisions=$idCollision")
    }
    if (codeCollision > 0) {
        log.info("SkuIdCollisionMonitor: code_market_collisions=$codeCollision")
    }

    val skuCollisionMetrics = SkuCollisionMetrics(prometheus)
    skuCollisionMetrics.setSkuIdCollisionCount(idCollision)
    skuCollisionMetrics.setSkuCodeMarketCollisionCount(codeCollision)
    skuCollisionMetrics.push()
}

class SkuCollisionMetrics(pushGateway: PushGateway) {
    private val source = "sku-id-collision-monitor"
    private val client = PrometheusClient(pushGateway)

    private val idCollisionGauge = Gauge.build(
        "procurement_csku_inventory_forecast_sanity_checker_sku_id_collision",
        "Number of collisions between sku_specification and sku_specification_yf by id",
    ).create()

    private val codeMarketCollisionGauge = Gauge.build(
        "procurement_csku_inventory_forecast_sanity_checker_sku_code_market_collision",
        "Number of collisions between sku_specification and sku_specification_yf by code, market",
    ).create()

    fun setSkuIdCollisionCount(count: Double) {
        idCollisionGauge.set(count)
    }

    fun setSkuCodeMarketCollisionCount(count: Double) {
        codeMarketCollisionGauge.set(count)
    }

    fun push() {
        client.push(idCollisionGauge, source)
        client.push(codeMarketCollisionGauge, source)
    }
}
