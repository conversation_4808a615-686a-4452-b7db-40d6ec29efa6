package com.hellofresh.cif.sanityChecker

import com.hellofresh.cif.sanityChecker.models.fetchData
import com.zaxxer.hikari.HikariDataSource
import io.github.resilience4j.retry.Retry
import java.sql.Timestamp

private fun getLastJobExecutionQuery(jobId: Int) = """
    select status,start_time from cron.job_run_details where jobid=$jobId order by runid desc limit 1;
"""

private const val FETCH_JOB_DETAILS_QUERY = """SELECT t.jobid, t.jobname, t.schedule FROM cron.job t"""
private const val JOBNAME = "jobname"
private const val JOBID = "jobid"
private const val CRON_EXPRESSION = "schedule"
private const val STATUS = "status"
private const val START_TIME = "start_time"

class InventoryCronJobsDB(private val retry: Retry, private val dataSource: HikariDataSource) {
    fun fetchJobData(): Map<Int, JobDetail> = dataSource.fetchData(retry, FETCH_JOB_DETAILS_QUERY) { row ->
        val jobId: Int = row.getInt(JOBID)
        jobId to JobDetail(row.getString(JOBNAME), row.getString(CRON_EXPRESSION))
    }

    fun fetchLastJobStatus(jobId: Int): JobStatus? =
        dataSource.fetchData(retry, getLastJobExecutionQuery(jobId)) { row ->
            Pair(jobId, JobStatus(row.getString(STATUS), row.getTimestamp(START_TIME)))
        }[jobId]
}

data class JobDetail(val jobName: String, val cronExpression: String)
data class JobStatus(val status: String, val startTime: Timestamp?)
