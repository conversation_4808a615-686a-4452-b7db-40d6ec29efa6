package com.hellofresh.cif.sanityChecker

import com.cronutils.model.CronType.UNIX
import com.cronutils.model.definition.CronDefinitionBuilder
import com.cronutils.model.time.ExecutionTime
import com.cronutils.parser.CronParser
import com.zaxxer.hikari.HikariDataSource
import io.github.resilience4j.retry.Retry
import io.prometheus.client.exporter.PushGateway
import java.sql.Timestamp
import java.time.ZonedDateTime

private const val FAILED = "failed"
private const val LAST_PG_CRON_JOB_EXECUTION_LABEL = "last_pg_cron_job_execution"
private const val LAST_PG_CRON_SCHEDULED_JOB_EXECUTION_STATUS_LABEL = "last_pg_cron_scheduled_job_execution_status"
private const val PG_CRON_JOB_SOURCE = "pg_cron_job"

fun monitorInventoryCronJobs(retry: Retry, prometheus: PushGateway, cifDatasource: HikariDataSource) {
    val metrics = failedCronJobMetrics(prometheus)
    val inventoryCronJobsDB = InventoryCronJobsDB(retry, cifDatasource)
    val jobDetails = inventoryCronJobsDB.fetchJobData()
    jobDetails.forEach { (jobId, jobDetail) ->
        inventoryCronJobsDB.fetchLastJobStatus(jobId)
            ?.let {
                checkLastJobStatus(metrics, jobDetail.jobName, it)
                checkLastRanJobAndNotify(metrics, jobDetail, it)
            }
    }
    metrics.push(PG_CRON_JOB_SOURCE)
}

fun checkLastJobStatus(
    metrics: CronJobMetrics,
    jobName: String,
    jobStatus: JobStatus
) {
    log.info("check the cron job status and notify for : $jobName job.")
    if (jobStatus.status == FAILED) {
        setMetric(metrics, jobName, 1, LAST_PG_CRON_JOB_EXECUTION_LABEL)
        log.info("$jobName job has completed with failed status.")
    } else {
        setMetric(metrics, jobName, 0, LAST_PG_CRON_JOB_EXECUTION_LABEL)
    }
}

fun checkLastRanJobAndNotify(
    metrics: CronJobMetrics,
    jobDetail: JobDetail,
    jobStatus: JobStatus
) {
    log.info("check the last ran cron job status and notify for : ${jobDetail.jobName} job.")
    val expectedLastJobRunTime = getExpectedLastJobRun(jobDetail.cronExpression)
    if (jobStatus.startTime != null) {
        if (expectedLastJobRunTime >= jobStatus.startTime) {
            setMetric(metrics, jobDetail.jobName, 1, LAST_PG_CRON_SCHEDULED_JOB_EXECUTION_STATUS_LABEL)
            log.info("$jobDetail job did not run as expected $expectedLastJobRunTime - status $jobStatus")
        } else {
            setMetric(metrics, jobDetail.jobName, 0, LAST_PG_CRON_SCHEDULED_JOB_EXECUTION_STATUS_LABEL)
        }
    }
}

private fun setMetric(metrics: CronJobMetrics, jobName: String, failedJobCount: Int, numberOfFailedJobCountType: String) {
    metrics.setFailedJobCount(PG_CRON_JOB_SOURCE, failedJobCount, numberOfFailedJobCountType, jobName)
}

private fun getExpectedLastJobRun(cronExpression: String): Timestamp {
    val now = ZonedDateTime.now()
    val cronDefinition = CronDefinitionBuilder.instanceDefinitionFor(UNIX)
    return Timestamp.from(
        ExecutionTime.forCron(
            CronParser(cronDefinition).parse(cronExpression),
        ).lastExecution(now).get().toInstant(),
    )
}
