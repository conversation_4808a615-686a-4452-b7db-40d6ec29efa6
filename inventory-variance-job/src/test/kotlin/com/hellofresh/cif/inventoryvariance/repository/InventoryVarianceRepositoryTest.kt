package com.hellofresh.cif.inventoryvariance.repository

import InfraPreparation
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.type.TypeFactory
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.inventory_variance_job.schema.Tables.INVENTORY_VARIANCE
import com.hellofresh.cif.inventory_variance_job.schema.tables.records.InventoryVarianceRecord
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.InventoryVarianceFixtures
import com.hellofresh.inventory.models.variance.DailyInventoryVarianceData
import com.hellofresh.inventory.models.variance.InventoryVariance
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import org.jooq.SQLDialect
import org.jooq.impl.DefaultConfiguration
import org.jooq.impl.DefaultDSLContext
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

@DisplayName("InventoryVariance Repository")
class InventoryVarianceRepositoryTest {

    @Test
    fun `should persist Variance Records successfully`() {
        val inventoryVariance1 = InventoryVarianceFixtures.default
        val skuId1 = inventoryVariance1.skuId
        val skuId2 = UUID.fromString("f25891b5-8ecd-47a9-b48d-a08ed27688e6")
        val inventoryVariance2 = inventoryVariance1.copy(
            skuId = skuId2,
        )

        val inventoryVarianceRepository = InventoryVarianceRepository(dslContext)

        val size = inventoryVarianceRepository.upsert(listOf(inventoryVariance1, inventoryVariance2))

        assertEquals(2, size)

        val inventoryRecords = fetchInventorVarianceRecords()

        assertEquals(2, inventoryRecords.size)

        inventoryRecords.first { it.skuId == skuId1 }
            .also {
                assertInventoryRecord(inventoryVariance1, it)
            }
        inventoryRecords.first { it.skuId == skuId2 }
            .also {
                assertInventoryRecord(inventoryVariance2, it)
            }
    }

    @Test
    fun `should upsert Variance Records successfully`() {
        val inventoryVariance1 = InventoryVarianceFixtures.default
        val inventoryVarianceRepository = InventoryVarianceRepository(dslContext)

        var size = inventoryVarianceRepository.upsert(listOf(inventoryVariance1))

        assertEquals(1, size)
        var inventoryVarianceRecords = fetchInventorVarianceRecords()
        assertEquals(1, inventoryVarianceRecords.size)
        assertInventoryRecord(inventoryVariance1, inventoryVarianceRecords.first())

        val inventoryVariance2 = inventoryVariance1.copy(
            cleardownVariance = SkuQuantity.fromLong(300L),
            liveVariance = SkuQuantity.fromLong(400L),
            dailyInventoryVarianceData = listOf(inventoryVariance1.dailyInventoryVarianceData.first()),
        )
        size = inventoryVarianceRepository.upsert(listOf(inventoryVariance2))

        assertEquals(1, size)
        inventoryVarianceRecords = fetchInventorVarianceRecords()
        assertEquals(1, inventoryVarianceRecords.size)
        assertInventoryRecord(inventoryVariance2, inventoryVarianceRecords.first())
    }

    private fun fetchInventorVarianceRecords() = dslContext.selectFrom(INVENTORY_VARIANCE).fetch()

    private fun assertInventoryRecord(
        inventoryVariance: InventoryVariance,
        inventoryVarianceRecord: InventoryVarianceRecord
    ) {
        assertEquals(inventoryVariance.skuId, inventoryVarianceRecord.skuId)
        assertEquals(inventoryVariance.dcCode, inventoryVarianceRecord.dcCode)
        assertEquals(inventoryVariance.dcWeek, inventoryVarianceRecord.productionWeek)
        assertEquals(
            inventoryVariance.cleardownVariance,
            SkuQuantity.fromBigDecimal(inventoryVarianceRecord.cleardownVariance)
        )
        assertEquals(inventoryVariance.liveVariance, SkuQuantity.fromBigDecimal(inventoryVarianceRecord.liveVariance))
        assertEquals(
            inventoryVariance.dailyInventoryVarianceData.toSet(),
            objectMapper.readValue(
                inventoryVarianceRecord.value.toString(),
                TypeFactory.defaultInstance()
                    .constructCollectionType(Set::class.java, DailyInventoryVarianceData::class.java),
            ),
        )
    }

    @BeforeEach
    fun beforeEach() {
        dslContext.deleteFrom(INVENTORY_VARIANCE).execute()
    }

    companion object {

        var objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()

        lateinit var dslContext: MetricsDSLContext

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            dslContext = DefaultDSLContext(
                DefaultConfiguration().apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    set(InfraPreparation.getMigratedDataSource())
                    setExecutor(Executors.newSingleThreadExecutor())
                },
            ).withMetrics(SimpleMeterRegistry())
        }
    }
}
