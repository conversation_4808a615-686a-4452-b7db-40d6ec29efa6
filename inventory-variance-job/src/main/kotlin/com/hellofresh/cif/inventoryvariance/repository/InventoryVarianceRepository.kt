package com.hellofresh.cif.inventoryvariance.repository

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_GAL
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_KG
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_OZ
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_UNRECOGNIZED
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_UNSPECIFIED
import com.hellofresh.cif.inventory_variance_job.schema.tables.records.InventoryVarianceRecord
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.inventory.models.variance.DailyInventoryVarianceData
import com.hellofresh.inventory.models.variance.InventoryVariance
import org.jooq.JSONB

class InventoryVarianceRepository(private val masterDSLContext: MetricsDSLContext) {

    private val inventoryVarianceMetricName = "inventory-variance-persist"

    fun upsert(inventoryVariances: List<InventoryVariance>): Int =
        masterDSLContext.withTagName(inventoryVarianceMetricName)
            .batchMerge(
                inventoryVariances.map { inventoryVariance ->
                    InventoryVarianceRecord().apply {
                        dcCode = inventoryVariance.dcCode
                        productionWeek = inventoryVariance.dcWeek
                        skuId = inventoryVariance.skuId
                        cleardownVariance = inventoryVariance.cleardownVariance.getValue()
                        liveVariance = inventoryVariance.liveVariance.getValue()
                        value = toJsonB(inventoryVariance.dailyInventoryVarianceData)
                        uom = inventoryVariance.uom.toDbUOM()
                    }
                },
            ).execute().size

    private fun toJsonB(dailyInventoryVarianceData: List<DailyInventoryVarianceData>): JSONB =
        JSONB.valueOf(objectMapper.writeValueAsString(dailyInventoryVarianceData))

    companion object {
        val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}

fun SkuUOM.toDbUOM(): Uom = when (this) {
    SkuUOM.UOM_UNSPECIFIED -> UOM_UNSPECIFIED
    SkuUOM.UOM_UNRECOGNIZED -> UOM_UNRECOGNIZED
    SkuUOM.UOM_UNIT -> UOM_UNIT
    SkuUOM.UOM_KG -> UOM_KG
    SkuUOM.UOM_LBS -> UOM_LBS
    SkuUOM.UOM_GAL -> UOM_GAL
    SkuUOM.UOM_LITRE -> UOM_LITRE
    SkuUOM.UOM_OZ -> UOM_OZ
}
