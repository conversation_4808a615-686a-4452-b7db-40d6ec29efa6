plugins {
    id("com.hellofresh.cif.application-conventions")
    alias(libs.plugins.jooq)
}

group = "$group.inventory-variance-job"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "inventory_variance|calculation|live_inventory_calculation|inventory_snapshot|uom"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {

    jooqGenerator(libs.postgresql.driver)

    implementation(projects.inventory.inventoryLib)
    implementation(projects.distributionCenterLib)
    implementation(projects.lib)
    implementation(projects.lib.models)
    implementation(projects.lib.configuration)
    implementation(projects.lib.logging)
    implementation(projects.lib.db)
    implementation(libs.prometheus.pushgateway)
    implementation(projects.skuSpecificationLib)
    implementation(projects.skuModels)

    testImplementation(projects.libTests)
    testImplementation(libs.mockk)
    testImplementation(testFixtures(projects.inventoryModels))
    testImplementation(testFixtures(projects.forecastApi))
    testImplementation(testFixtures(projects.lib.featureflags))
    testImplementation(testFixtures(projects.skuModels))
}
