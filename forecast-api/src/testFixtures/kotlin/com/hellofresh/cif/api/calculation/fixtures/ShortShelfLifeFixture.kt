package com.hellofresh.cif.api.calculation.fixtures

import com.hellofresh.cif.api.calculation.generated.model.UomEnum.UNIT
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLife
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeConf
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

fun ShortShelfLife.Companion.default(
    dcCode: String = "DC",
    dcWeek: String = "2025-W08",
    skuId: UUID = UUID.randomUUID(),
    date: LocalDate = LocalDate.now(
        java.time.ZoneOffset.UTC,
    )
) =
    ShortShelfLife(
        dcCode = dcCode,
        skuId = skuId,
        date = date,
        dcWeek = dcWeek,
        skuCategory = "PHF",
        skuCode = "PHF-123456",
        skuName = "Apple 200g",
        openingStock = BigDecimal.TEN,
        unusableStock = BigDecimal.TEN,
        consumption = BigDecimal.TEN,
        stockUpdates = BigDecimal.TEN,
        bufferPercentage = BigDecimal.TEN,
        bufferAdditional = BigDecimal.TEN,
        sqrQuantity = BigDecimal.TEN,
        touchlessOrderingEnabled = false,
        uom = UNIT,
    )

fun SQRShortShelfLifeConf.Companion.default(
    dcCode: String = "DC",
    skuId: UUID = UUID.randomUUID(),
    date: LocalDate = LocalDate.now(
        java.time.ZoneOffset.UTC,
    )
) =
    SQRShortShelfLifeConf(
        dcCode = dcCode,
        skuId = skuId,
        date = date,
        bufferPercentage = BigDecimal.TEN,
        bufferAdditional = BigDecimal.TEN,
        touchlessOrderingEnabled = false,
    )

fun StockUpdate.Companion.default(
    dcCode: String = "DC",
    skuId: UUID = UUID.randomUUID(),
    dcWeek: String,
    date: LocalDate = LocalDate.now(
        java.time.ZoneOffset.UTC,
    )
) =
    StockUpdate(
        skuId = skuId,
        dcCode = dcCode,
        date = date,
        week = dcWeek,
        quantity = SkuQuantity.fromBigDecimal(BigDecimal.TEN),
        reason = "testing",
        reasonDetail = null,
        authorName = null,
        authorEmail = "<EMAIL>",
        version = 4926,
        createdAt = LocalDateTime.now(),
        deleted = false,
    )
