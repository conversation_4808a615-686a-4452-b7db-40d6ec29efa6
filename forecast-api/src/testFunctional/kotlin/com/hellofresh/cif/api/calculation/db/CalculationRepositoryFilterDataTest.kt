package com.hellofresh.cif.api.calculation.db

import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_CONSUMPTION_ONLY
import com.hellofresh.cif.api.calculation.CalculationFilterRequest
import com.hellofresh.cif.api.calculation.CalculationFunctionalTest
import com.hellofresh.cif.api.calculation.fixtures.Default
import com.hellofresh.cif.api.calculation.fixtures.calculationRecord
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.PurchaseOrderRecord
import com.hellofresh.cif.api.schema.tables.records.PurchaseOrderSkuRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.api.schema.tables.records.SupplierRecord
import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.math.BigDecimal
import java.math.BigDecimal.TEN
import java.math.BigDecimal.ZERO
import java.time.DayOfWeek.FRIDAY
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class CalculationRepositoryFilterDataTest : CalculationFunctionalTest() {

    private val dcCodeVE = "VE"
    private val dcWeek = DcWeek(LocalDate.now(), FRIDAY)
    private val calculationFilterRequest =
        CalculationFilterRequest(listOf(dcCodeVE), listOf(dcWeek.toString()), consumptionDaysAhead = 0)

    @Test
    fun `should return empty results when there is no record`() {
        runBlocking {
            val result =
                prodCalculationsRepo.fetchCalculationFilters(calculationFilterRequest)
            assertTrue(result.skuSet.isEmpty())
            assertTrue(result.locationInBox.isEmpty())
            assertTrue(result.ingredientCategories.isEmpty())
            assertTrue(result.suppliers.isEmpty())
        }
    }

    @Test
    fun `should fetch filter data for calculations`() {
        // given
        val supplierId = UUID.randomUUID()
        val poRef = "2022111_01"
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId)
        val startTime = startDateInDcWeek.atStartOfDay(zoneId)

        val calculationRecords = (0..6).map {
            Default.calculationRecord {
                this.cskuId = UUID.randomUUID()
                this.dcCode = dcCodeVE
                this.date = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId).plusDays(it.toLong())
                this.productionWeek = dcWeek.value
            }
        }

        val poRecord = PurchaseOrderRecord(
            poRef, UUID.randomUUID(), poRef, dcCodeVE,
            startTime.toOffsetDateTime(), startTime.plusHours(2).toOffsetDateTime(), supplierId,
            OffsetDateTime.now(), OffsetDateTime.now(), null, null,
        )
        val skuId = calculationRecords.first().cskuId
        val poSkuRecord = PurchaseOrderSkuRecord(skuId, BigDecimal(99), poRecord.poNumber, Uom.UOM_UNIT)

        val skuSpecRecords = toSkuSpecRecords(calculationRecords)
        insertSkuRecords(skuSpecRecords)

        val supplierRecord = SupplierRecord(supplierId, null, "Sup Name")

        @Suppress("SpreadOperator")
        dsl.batchInsert(
            poRecord,
            poSkuRecord,
            *calculationRecords.toTypedArray(),
            supplierRecord,
        )
            .execute()
        dsl.query("refresh materialized view concurrently po_calculations_view").execute()

        // when
        val calculationFiltersData = runBlocking {
            prodCalculationsRepo.fetchCalculationFilters(calculationFilterRequest)
        }

        // then
        assertEquals(skuSpecRecords.size, calculationFiltersData.skuSet.size)
        skuSpecRecords.forEach { record ->
            calculationFiltersData.skuSet.first { record.id == it.skuId }.also { sku ->
                assertEquals(record.code, sku.skuCode)
                assertEquals(record.name, sku.skuName)
            }
        }
        assertEquals(skuSpecRecords.map { it.category }.toSet(), calculationFiltersData.ingredientCategories)
        assertEquals(skuSpecRecords.map { it.packaging }.toSet(), calculationFiltersData.locationInBox)
        assertEquals(1, calculationFiltersData.suppliers.size)
        assertEquals(supplierRecord.id, calculationFiltersData.suppliers.first().id)
        assertEquals(supplierRecord.name, calculationFiltersData.suppliers.first().name)
    }

    @Test
    fun `supplier name from po should have priority`() {
        // given
        val supplierId = UUID.randomUUID()
        val supplierNamePo = UUID.randomUUID().toString()
        val supplierName = UUID.randomUUID().toString()

        val poRef = "2022111_01"
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId)
        val startTime = startDateInDcWeek.atStartOfDay(zoneId)

        val calculationRecords = (0..6).map {
            Default.calculationRecord {
                this.cskuId = UUID.randomUUID()
                this.dcCode = dcCodeVE
                this.date = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId).plusDays(it.toLong())
                this.productionWeek = dcWeek.value
            }
        }

        val poRecord = PurchaseOrderRecord().apply {
            this.poRef = poRef
            poId = UUID.randomUUID()
            poNumber = poRef
            dcCode = dcCodeVE
            expectedArrivalStartTime = startTime.toOffsetDateTime()
            expectedArrivalEndTime = startTime.plusHours(2).toOffsetDateTime()
            this.supplierId = supplierId
            this.supplierName = supplierNamePo
        }
        val skuId = calculationRecords.first().cskuId
        val poSkuRecord = PurchaseOrderSkuRecord(skuId, BigDecimal(99), poRecord.poNumber, Uom.UOM_UNIT)

        val skuSpecRecords = toSkuSpecRecords(calculationRecords)
        insertSkuRecords(skuSpecRecords)

        val supplierRecord = SupplierRecord(supplierId, null, supplierName)

        @Suppress("SpreadOperator")
        dsl.batchInsert(
            poRecord,
            poSkuRecord,
            *calculationRecords.toTypedArray(),
            supplierRecord,
        )
            .execute()
        dsl.query("refresh materialized view concurrently po_calculations_view").execute()

        // when
        val calculationFiltersData = runBlocking {
            prodCalculationsRepo.fetchCalculationFilters(calculationFilterRequest)
        }

        // then
        assertEquals(skuSpecRecords.size, calculationFiltersData.skuSet.size)
        assertEquals(1, calculationFiltersData.suppliers.size)
        assertEquals(supplierId, calculationFiltersData.suppliers.first().id)
        assertEquals(supplierNamePo, calculationFiltersData.suppliers.first().name)
    }

    @Test
    fun `supplier is empty if no supplier found`() {
        // given

        val poRef = "2022111_01"
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId)
        val startTime = startDateInDcWeek.atStartOfDay(zoneId)

        val calculationRecords = (0..6).map {
            Default.calculationRecord {
                this.cskuId = UUID.randomUUID()
                this.dcCode = dcCodeVE
                this.date = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId).plusDays(it.toLong())
                this.productionWeek = dcWeek.value
            }
        }

        val poRecord = PurchaseOrderRecord().apply {
            this.poRef = poRef
            poId = UUID.randomUUID()
            poNumber = poRef
            dcCode = dcCodeVE
            expectedArrivalStartTime = startTime.toOffsetDateTime()
            expectedArrivalEndTime = startTime.plusHours(2).toOffsetDateTime()
            this.supplierId = UUID.randomUUID()
        }
        val skuId = calculationRecords.first().cskuId
        val poSkuRecord = PurchaseOrderSkuRecord(skuId, BigDecimal(99), poRecord.poNumber, Uom.UOM_UNIT)

        val skuSpecRecords = toSkuSpecRecords(calculationRecords)
        insertSkuRecords(skuSpecRecords)

        @Suppress("SpreadOperator")
        dsl.batchInsert(
            poRecord,
            poSkuRecord,
            *calculationRecords.toTypedArray(),
        )
            .execute()
        dsl.query("refresh materialized view concurrently po_calculations_view").execute()

        // when
        val calculationFiltersData = runBlocking {
            prodCalculationsRepo.fetchCalculationFilters(calculationFilterRequest)
        }

        // then
        assertEquals(skuSpecRecords.size, calculationFiltersData.skuSet.size)
        assertEquals(0, calculationFiltersData.suppliers.size)
    }

    @Test
    fun `should fetch filter data for request given dc week and sku`() {
        // given
        val expectedSkuId = UUID.randomUUID()
        val calculationRecords = listOf(
            Default.calculationRecord {
                this.cskuId = expectedSkuId
                this.dcCode = dcCodeVE
                this.date = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId)
                productionWeek = dcWeek.value
            },
            Default.calculationRecord {
                this.cskuId = UUID.randomUUID()
                this.dcCode = dcCodeVE
                this.date = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId)
                productionWeek = dcWeek.value
            },
            Default.calculationRecord {
                this.cskuId = UUID.randomUUID()
                this.dcCode = dcCodeVE
                DcWeek(dcWeek.getStartDateInDcWeek(FRIDAY, zoneId).minusWeeks(1), FRIDAY)
                    .also { previousdWeek ->
                        this.date = previousdWeek.getStartDateInDcWeek(FRIDAY, zoneId)
                        productionWeek = previousdWeek.value
                    }
            },
            Default.calculationRecord {
                this.cskuId = UUID.randomUUID()
                this.dcCode = "DCr"
                this.date = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId)
                productionWeek = dcWeek.value
            },
        )

        val skuSpecRecords = toSkuSpecRecords(calculationRecords)
        insertSkuRecords(skuSpecRecords)

        @Suppress("SpreadOperator")
        dsl.batchInsert(*calculationRecords.toTypedArray()).execute()

        // when
        val calculationFiltersData = runBlocking {
            prodCalculationsRepo.fetchCalculationFilters(
                calculationFilterRequest.copy(
                    dcCodes = listOf(dcCodeVE),
                    skuCodes = skuSpecRecords.filter { it.id == expectedSkuId }.map { it.code },
                    weeks = listOf(dcWeek.toString()),
                ),
            )
        }

        // then
        assertEquals(1, calculationFiltersData.skuSet.size)
        skuSpecRecords.first { it.id == expectedSkuId }.also { record ->
            calculationFiltersData.skuSet.first { record.id == it.skuId }.also { sku ->
                assertEquals(record.code, sku.skuCode)
                assertEquals(record.name, sku.skuName)
            }
            assertEquals(setOf(record.category), calculationFiltersData.ingredientCategories)
            assertEquals(setOf(record.packaging), calculationFiltersData.locationInBox)
        }
        assertEquals(0, calculationFiltersData.suppliers.size)
    }

    @Test
    fun `should fetch filter data for request with additional filter with demand and supplier filter`() {
        // given
        val expectedSkuId = UUID.randomUUID()
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId)
        val startTime = startDateInDcWeek.atTime(12, 0).atZone(ZoneOffset.UTC)
        val calculationRecords = listOf(
            Default.calculationRecord {
                this.cskuId = expectedSkuId
                this.dcCode = dcCodeVE
                this.date = startDateInDcWeek
                this.productionWeek = dcWeek.value
                this.demanded = TEN
            },
            Default.calculationRecord {
                this.cskuId = UUID.randomUUID()
                this.dcCode = dcCodeVE
                this.date = startDateInDcWeek
                this.productionWeek = dcWeek.value
                this.demanded = ZERO
            },
            Default.calculationRecord {
                this.cskuId = UUID.randomUUID()
                this.dcCode = dcCodeVE
                this.date = startDateInDcWeek
                productionWeek = dcWeek.value
                demanded = ZERO
            },
            Default.calculationRecord {
                this.cskuId = UUID.randomUUID()
                this.dcCode = "DCr"
                this.date = startDateInDcWeek
                productionWeek = dcWeek.value
                demanded = ZERO
            },
        )

        val supplierRecord = SupplierRecord(UUID.randomUUID(), null, "Sup Name")
        val poRecord = PurchaseOrderRecord(
            "PoRef", UUID.randomUUID(), "poN", dcCodeVE,
            startTime.toOffsetDateTime(), startTime.plusHours(2).toOffsetDateTime(), supplierRecord.id,
            OffsetDateTime.now(ZoneOffset.UTC), OffsetDateTime.now(ZoneOffset.UTC), null, null,
        )
        val poSkuRecord = PurchaseOrderSkuRecord(expectedSkuId, BigDecimal(99), poRecord.poNumber, Uom.UOM_UNIT)

        val skuSpecRecords = toSkuSpecRecords(calculationRecords)
        insertSkuRecords(skuSpecRecords)

        @Suppress("SpreadOperator")
        dsl.batchInsert(
            poRecord,
            poSkuRecord,
            *calculationRecords.toTypedArray(),
            supplierRecord,
        )
            .execute()
        dsl.query("refresh materialized view concurrently po_calculations_view").execute()

        // when
        val calculationFiltersData = runBlocking {
            prodCalculationsRepo.fetchCalculationFilters(
                calculationFilterRequest.copy(
                    additionalFilters = setOf(WITH_CONSUMPTION_ONLY),
                    supplierIds = listOf(supplierRecord.id),
                ),
            )
        }

        // then
        assertEquals(1, calculationFiltersData.suppliers.size)
        assertEquals(supplierRecord.id, calculationFiltersData.suppliers.first().id)
        assertEquals(supplierRecord.name, calculationFiltersData.suppliers.first().name)

        assertEquals(1, calculationFiltersData.skuSet.size)
        skuSpecRecords.first { it.id == expectedSkuId }.also { record ->
            calculationFiltersData.skuSet.first { record.id == it.skuId }.also { sku ->
                assertEquals(record.code, sku.skuCode)
                assertEquals(record.name, sku.skuName)
            }
            assertEquals(setOf(record.category), calculationFiltersData.ingredientCategories)
            assertEquals(setOf(record.packaging), calculationFiltersData.locationInBox)
        }
    }

    private fun toSkuSpecRecords(calculationRecords: List<CalculationRecord>) =
        calculationRecords.mapIndexed { index, record ->
            SkuSpecificationRecord().apply {
                id = record.cskuId
                parentId = null
                name = "N-${record.cskuId}"
                code = "C-${record.cskuId}"
                category = "CA$index"
                acceptableCodeLife = 1
                coolingType = ""
                packaging = "P-${record.cskuId}"
                market = "dach"
                uom = com.hellofresh.cif.api.schema.enums.Uom.UOM_UNIT
            }
        }
}
