package com.hellofresh.cif.api.fileexport

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.FileExportResponse
import com.hellofresh.cif.api.fileexport.repository.FileExportRequest
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.enums.ExportStatus.Pending
import com.hellofresh.cif.api.schema.tables.records.FileExportRequestRecord
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.junit.jupiter.api.Test

class FileExportApiTest : FunctionalTest() {

    @Test
    fun `should be able to get file Export request`() {
        val fileExportRequest = FileExportRequest(
            requestId = UUID.randomUUID(),
            jsonParameters = "{}",
            status = Pending,
            fileUrl = null,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
        )

        dsl.batchInsert(
            FileExportRequestRecord().apply {
                requestId = fileExportRequest.requestId
                parameters = JSONB.valueOf(fileExportRequest.jsonParameters)
            },
        ).execute()

        runBlocking {
            get("/export/${fileExportRequest.requestId}")
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val response = objectMapper.readValue(
                        bodyAsText(),
                        FileExportResponse::class.java,
                    )

                    with(response) {
                        assertEquals(this.status, fileExportRequest.status.mapToFileExportStatus())
                        assertEquals(this.parameters, fileExportRequest.jsonParameters)
                        assertEquals(this.fileUrl, fileExportRequest.fileUrl)
                        assertEquals(this.requestId, fileExportRequest.requestId)
                    }
                }
        }
    }

    @Test
    fun `should return bad request if the request is is not valid`() {
        val requestId = "invalidRequestId"

        runBlocking {
            get("/export/$requestId")
                .apply {
                    assertEquals(HttpStatusCode.BadRequest, status)
                }
        }
    }

    private fun get(
        getUrl: String
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", "https://test.com"), false)
                fileExportModule(FileExportService(fileExportRequestRepository), timeOutInMillis)()
            }
            response = client.get(getUrl) {
                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
            }
        }
        return response
    }

    companion object {
        val timeOutInMillis: Duration = Duration.parse("PT1S")
        val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
