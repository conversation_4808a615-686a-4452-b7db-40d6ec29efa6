package com.hellofresh.cif.api.supplier

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinFeature
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.calculation.generated.model.ErrorResponse
import com.hellofresh.cif.api.calculation.generated.model.SupplierLeadTimeResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.note.NoteApiFunctionalTest.Companion.timeOutInMillis
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.api.schema.tables.records.SupplierCulinarySkuRecord
import com.hellofresh.cif.api.schema.tables.records.SupplierRecord
import com.hellofresh.cif.api.schema.tables.records.SupplierSkuPricingRecord
import com.hellofresh.cif.api.schema.tables.records.SupplierSkuRecord
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.skuinput.repo.SupplierRepositoryImpl
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeEach

class SupplierApiFunctionalTest {
    private val jwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"
    private val dcCode = "VE"
    private val datasource = getMigratedDataSource()
    private val dbConfig = DefaultConfiguration().apply {
        setSQLDialect(POSTGRES)
        setDataSource(datasource)
        setExecutor(Executors.newSingleThreadExecutor())
    }
    private val dsl = dbConfig.dsl()
    private val skuId = UUID.randomUUID()
    private val supplierSkuId = UUID.randomUUID()
    private val expectedSupplierId = UUID.randomUUID()
    private val supplierParentId = UUID.randomUUID()
    private val culinarySkuId = UUID.randomUUID()
    private val supplierRecord = SupplierRecord(
        expectedSupplierId,
        supplierParentId,
        "Name 1"
    )
    private val supplierCulinarySkuRecord = SupplierCulinarySkuRecord(
        supplierSkuId,
        supplierParentId,
        culinarySkuId,
        "dach",
        "active",
        OffsetDateTime.now(),
        OffsetDateTime.now()
    )

    private val supplierSkuRecord = SupplierSkuRecord(
        supplierSkuId,
        skuId,
        10,
        OffsetDateTime.now(),
        OffsetDateTime.now(),
        "active"
    )

    private val supplierSkuPricingRecord = SupplierSkuPricingRecord(
        UUID.randomUUID(), supplierSkuId, 10, true, "dach", OffsetDateTime.now(), OffsetDateTime.now(), LocalDate.now(),
        LocalDate.now().plusMonths(10)
    )

    private val objectMapper = ObjectMapper()
        .registerModule(KotlinModule.Builder().configure(KotlinFeature.NullIsSameAsDefault, true).build())
        .findAndRegisterModules()
    private val dcConfigRepository = DcRepositoryImpl(dbConfig.dsl().withMetrics(SimpleMeterRegistry()))
    private val dcConfigService = DcConfigService(SimpleMeterRegistry(), dcConfigRepository)
    private val supplierRepository = SupplierRepositoryImpl(dbConfig.dsl().withMetrics(SimpleMeterRegistry()))

    @BeforeEach
    fun initData() {
        dsl.deleteFrom(Tables.SUPPLIER).execute()
        dsl.deleteFrom(Tables.SUPPLIER_SKU).execute()
        dsl.deleteFrom(Tables.SUPPLIER_SKU_PRICING).execute()
        dsl.deleteFrom(Tables.SUPPLIER_CULINARY_SKU).execute()
    }

    @Test
    fun `supplier endpoint returns supplier with lead time for the given skuId`() {
        val supplierSkuPricingRecordOne = SupplierSkuPricingRecord(
            UUID.randomUUID(), supplierSkuId, 10, true, "dach", OffsetDateTime.now(),
            OffsetDateTime.now(), LocalDate.now(),
            LocalDate.now().plusMonths(10),
        )
        dsl.batchInsert(
            supplierRecord,
            supplierCulinarySkuRecord,
            supplierSkuRecord,
            supplierSkuPricingRecord,
            supplierSkuPricingRecordOne,
        ).execute()
        runBlocking {
            getUrl("/suppliers/$dcCode/$skuId").apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.OK, status)
                assertNotNull(body)
                val supplierLeadTimeResponses = objectMapper.readValue<List<SupplierLeadTimeResponse>>(
                    body,
                )
                assertEquals(1, supplierLeadTimeResponses.size)
                val supplierLeadTimeResponse = supplierLeadTimeResponses.first()
                supplierLeadTimeResponse.apply {
                    assertEquals(expectedSupplierId, supplierId)
                    assertEquals("Name 1", supplierName)
                    assertEquals(10, leadTime)
                }
            }
        }
    }

    @Test
    fun `supplier endpoint returns emptylist when the supplier has null leadtime`() {
        dsl.batchInsert(
            supplierRecord,
            supplierCulinarySkuRecord,
            supplierSkuRecord
        ).execute()
        runBlocking {
            getUrl("/suppliers/$dcCode/$skuId").apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.NotFound, status)
                assertEquals("[]", body)
            }
        }
    }

    @Test
    fun `supplier endpoint returns supplier with lead time for the given skuId - supplier with out of range contract excluded`() {
        val supplierSkuPricingRecordOne = SupplierSkuPricingRecord(
            UUID.randomUUID(), supplierSkuId, 10, true, "dach", OffsetDateTime.now(),
            OffsetDateTime.now(), LocalDate.now().plusMonths(10),
            LocalDate.now().plusMonths(15),
        )
        val supplierSkuPricingRecordTwo = SupplierSkuPricingRecord(
            UUID.randomUUID(), supplierSkuId, 15, true, "dach", OffsetDateTime.now(),
            OffsetDateTime.now(), LocalDate.now(),
            LocalDate.now().plusMonths(5),
        )
        dsl.batchInsert(
            supplierRecord,
            supplierCulinarySkuRecord,
            supplierSkuRecord,
            supplierSkuPricingRecordOne,
            supplierSkuPricingRecordTwo
        ).execute()
        runBlocking {
            getUrl("/suppliers/$dcCode/$skuId").apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.OK, status)
                assertNotNull(body)
                val supplierLeadTimeResponses = objectMapper.readValue<List<SupplierLeadTimeResponse>>(
                    body,
                )
                assertEquals(1, supplierLeadTimeResponses.size)
                val supplierLeadTimeResponse = supplierLeadTimeResponses.first()
                supplierLeadTimeResponse.apply {
                    assertEquals(expectedSupplierId, supplierId)
                    assertEquals("Name 1", supplierName)
                    assertEquals(15, leadTime)
                }
            }
        }
    }

    @Test
    fun `supplier endpoint returns HTTP 400 when multiple dccode sent as input`() {
        runBlocking {
            val skuId = UUID.randomUUID()
            getUrl("/suppliers/$dcCode/$skuId?dcCode=VE&dcCode=CR").apply {
                val errorResponse = objectMapper.readValue(bodyAsText(), ErrorResponse::class.java)
                assertEquals(HttpStatusCode.BadRequest, status)
                assertNotNull(errorResponse.reason)
                assertTrue(errorResponse.reason!!.contains("Multiple DCs are not supported"))
            }
        }
    }

    @Test
    fun `supplier endpoint returns HTTP 404 on records not found`() {
        runBlocking {
            val nonExistingSkuId = UUID.randomUUID()
            getUrl("/suppliers/$dcCode/$nonExistingSkuId").apply {
                assertEquals(HttpStatusCode.NotFound, status)
            }
        }
    }

    private fun getUrl(url: String): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            this.application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                supplierRoutingModule(supplierRepository, dcConfigService, timeOutInMillis) ()
            }
            response = client.get(url) {
                addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }
}
