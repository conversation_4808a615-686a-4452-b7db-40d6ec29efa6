package com.hellofresh.cif.api.fileupload

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.FileUploadResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.api.schema.tables.records.FileUploadsRecord
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import java.time.ZoneOffset
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalToIgnoringCase
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class FileUploadApiTest : FunctionalTest() {

    @BeforeEach
    fun cleanupDb() {
        dsl.deleteFrom(Tables.FILE_UPLOADS).execute()
    }

    @ParameterizedTest(name = "{index} => {1}")
    @MethodSource("provideInputsForFileUploads")
    fun `should return list of file uploads`(existingFileUpload: FileUploadsRecord) {
        dsl.insertInto(Tables.FILE_UPLOADS).set(existingFileUpload).execute()

        runBlocking {
            get("/file-uploads?market=${existingFileUpload.market}&fileType=${existingFileUpload.fileType.literal}")
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val response = objectMapper.readValue<List<FileUploadResponse>>(bodyAsText())

                    with(response.first()) {
                        assertEquals(existingFileUpload.id, this.id)
                        assertEquals(existingFileUpload.fileName, this.fileName)
                        assertEquals(existingFileUpload.market, this.market)
                        assertEquals(existingFileUpload.dcs.first(), this.dcCodes.first())
                        assertEquals(existingFileUpload.authorName, this.authorName)
                        assertEquals(existingFileUpload.authorEmail, this.authorEmail)
                        assertEquals(
                            existingFileUpload.createdAt.atOffset(ZoneOffset.UTC).toEpochSecond(),
                            this.createdAt.toEpochSecond(),
                        )
                        assertThat(this.status.value, equalToIgnoringCase(existingFileUpload.status.literal))
                        assertEquals(existingFileUpload.fileType.literal, this.fileType.value)
                    }
                }
        }
    }

    @Test
    fun `should return empty list of file uploads`() {
        val existingFileUpload = getFileUploadStockInventoryData(DACH_MARKET)
        dsl.insertInto(Tables.FILE_UPLOADS).set(existingFileUpload).execute()

        runBlocking {
            get("/file-uploads?market=TEST&fileType=${existingFileUpload.fileType.name}")
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val response = objectMapper.readValue<List<FileUploadResponse>>(bodyAsText())
                    assertTrue(response.isEmpty())
                }
        }
    }

    @Test
    fun `should return bad request if the market not specify`() {
        runBlocking {
            get("/file-uploads")
                .apply {
                    assertEquals(HttpStatusCode.BadRequest, status)
                }
        }
    }

    @Test
    fun `should return bad request if the file type not specify`() {
        runBlocking {
            get("/file-uploads?market=TEST")
                .apply {
                    assertEquals(HttpStatusCode.BadRequest, status)
                }
        }
    }

    private fun get(
        getUrl: String
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "test", "test", "https://test.com"), false)
                fileUploadModule(FileUploadService(fileUploadRepository), timeOutInMillis)()
            }
            response = client.get(getUrl) {
                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
            }
        }
        return response
    }

    companion object {
        val timeOutInMillis: Duration = Duration.parse("PT1S")
        val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()

        @Suppress("unused")
        @JvmStatic
        fun provideInputsForFileUploads(): Stream<Arguments> = Stream.of(
            Arguments.of(
                getFileUploadStockInventoryData(DACH_MARKET),
                "should process the StockInventory file upload",
            ),
            Arguments.of(
                getFileUploadStockUpdateData(DACH_MARKET),
                "should process the StockUpdate file upload",
            ),
        )
    }
}
