package com.hellofresh.cif.api.fileexport

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.api.schema.enums.ExportStatus.Failed
import com.hellofresh.cif.api.schema.enums.ExportStatus.Pending
import com.hellofresh.cif.api.schema.tables.records.FileExportRequestRecord
import com.hellofresh.cif.db.metrics.withMetrics
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit.SECONDS
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class FileExportRequestRepositoryImplTest : FunctionalTest() {

    private val datasource = getMigratedDataSource()
    private val dbConfig = DefaultConfiguration().apply {
        setSQLDialect(POSTGRES)
        setDataSource(datasource)
        setExecutor(Executors.newSingleThreadExecutor())
    }
    private val dsl = DSL.using(dbConfig).withMetrics(SimpleMeterRegistry())

    @BeforeEach
    fun cleanupDb() {
        dbConfig.dsl().deleteFrom(Tables.FILE_EXPORT_REQUEST).execute()
    }

    @Test
    fun `test get file export by request id`() {
        // given
        val requestId = UUID.randomUUID()
        val createdAtTime = LocalDateTime.now()
        val existingExportRequest = FileExportRequestRecord().apply {
            this.requestId = requestId
            fileUrl = "https://example.com/file.csv"
            parameters = JSONB.valueOf("""{"view": "dailyView"}""")
            status = Pending
            createdAt = createdAtTime
        }
        dsl.batchInsert(existingExportRequest).execute()
        // when
        runBlocking {
            val fileExportRequest = fileExportRequestRepository.fetchFileExportRequest(requestId)!!

            // then
            assertEquals(existingExportRequest.requestId, fileExportRequest.requestId)
            assertEquals(existingExportRequest.parameters.data(), fileExportRequest.jsonParameters)
            assertEquals(existingExportRequest.status, fileExportRequest.status)
            assertEquals(
                existingExportRequest.createdAt.truncatedTo(SECONDS),
                fileExportRequest.createdAt.truncatedTo(SECONDS),
            )
        }
    }

    @Test
    fun `test save file export request`() {
        val params = """{"view": "dailyView"}"""
        // when
        runBlocking {
            val fileExportRequest = fileExportRequestRepository.saveFileExportRequest(params)
            val insertedFileRequest = fileExportRequestRepository.fetchFileExportRequest(fileExportRequest.requestId)!!
            // then
            assertEquals(fileExportRequest.requestId, insertedFileRequest.requestId)
            assertEquals(fileExportRequest.jsonParameters, params)
        }
    }

    @Test
    fun `test update file export request status`() {
        val params = """{"view": "dailyView"}"""
        // when
        runBlocking {
            val fileExportRequest1 = fileExportRequestRepository.saveFileExportRequest(params)
            val fileExportRequest2 = fileExportRequestRepository.saveFileExportRequest("{}")
            val updatedFileExportRequest = fileExportRequest1.copy(
                status = Failed,
                fileUrl = "https://example.com/file.csv",
            )
            val updatedFileExportRequestResponse = fileExportRequestRepository.updateFileExportRequest(
                updatedFileExportRequest,
            )!!

            assertNotEquals(fileExportRequest1.requestId, fileExportRequest2.requestId)
            assertEquals(updatedFileExportRequest.requestId, fileExportRequest1.requestId)
            assertEquals(updatedFileExportRequest.status, updatedFileExportRequestResponse.status)
            assertEquals(updatedFileExportRequest.fileUrl, updatedFileExportRequestResponse.fileUrl)
        }
    }
}
