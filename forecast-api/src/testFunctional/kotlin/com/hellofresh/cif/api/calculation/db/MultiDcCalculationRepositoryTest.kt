package com.hellofresh.cif.api.calculation.db

import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.AllPages
import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.Page
import com.hellofresh.cif.api.calculation.SortBy.SKU_CODE
import com.hellofresh.cif.api.calculation.fixtures.Default
import com.hellofresh.cif.api.calculation.fixtures.calculationRecord
import com.hellofresh.cif.api.toSkuSpecification
import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class MultiDcCalculationRepositoryTest : FunctionalTest() {
    private val calculationRequest = CalculationRequest(emptyList(), emptyList(), AllPages, consumptionDaysAhead = 0)

    @Test
    fun `should fetch all daily calculations for multiple DCs without pagination`() {
        // given
        val skuId = UUID.randomUUID()
        val week = "2022-W01"
        val dcCodes = listOf("VE", "BX")
        val calculationRecords = dcCodes.map {
            Default.calculationRecord {
                this.cskuId = skuId
                this.dcCode = it
                productionWeek = week
            }
        }
        dsl.batchInsert(calculationRecords).execute()
        insertSkuRecords(calculationRecords.map(::toSkuSpecification).toSet())

        // when
        val pageableCalculations = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(
                calculationRequest.copy(dcCodes = dcCodes, weeks = listOf(week), sortBy = SKU_CODE),
            )
        }

        // then
        assertEquals(calculationRecords.size, pageableCalculations.calculationPage.size)
        assertEquals(
            calculationRecords.map { it.dcCode }.toSet(),
            pageableCalculations.calculationPage.map { it.dcCode }.toSet(),
        )
    }

    @Test
    fun `should return a single calculation page for multiple DCs for the same sku`() {
        // given
        val week = DcWeek(LocalDate.now(), LocalDate.now().dayOfWeek).value
        val dcCodes = listOf("VE", "BX", "CH")
        val skuId = UUID.randomUUID()
        val daysCount = 7
        val singleDayCalculationRecords = dcCodes.map { dcCode ->
            persistCalculations(daysCount, skuId, dcCode, week)
        }.flatten()
        dsl.batchInsert(singleDayCalculationRecords).execute()
        insertSkuRecords(singleDayCalculationRecords.map(::toSkuSpecification).toSet())

        // when
        val pageableCalculationsAllSkus = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(
                calculationRequest.copy(dcCodes = dcCodes, weeks = listOf(week), sortBy = SKU_CODE),
            )
        }

        assertEquals(daysCount * dcCodes.size, pageableCalculationsAllSkus.calculationPage.size)
        assertEquals(1, pageableCalculationsAllSkus.totalPages)
    }

    @Test
    fun `should return one page per sku for multiple DCs when each has a different sku when requesting a single sku per page`() {
        val week = DcWeek(LocalDate.now(), LocalDate.now().dayOfWeek).value
        val dcCodes = mapOf(
            "VE" to UUID.randomUUID(),
            "BX" to UUID.randomUUID(),
            "CH" to UUID.randomUUID(),
        )
        val daysCount = 7
        val weekCalculationRecords = dcCodes.mapValues { (dcCode, skuId) ->
            persistCalculations(daysCount, skuId, dcCode, week)
        }.values.flatten()
        dsl.batchInsert(weekCalculationRecords).execute()
        insertSkuRecords(weekCalculationRecords.map(::toSkuSpecification).toSet())

        // when
        val pageRequest = calculationRequest.copy(
            dcCodes = dcCodes.keys.toList(),
            weeks = listOf(week),
            pageRequest = Page(1, 1),
        )
        val resultOneSkuPerPage = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(pageRequest)
        }
        // then
        assertEquals(daysCount, resultOneSkuPerPage.calculationPage.size)
        assertEquals(dcCodes.size, resultOneSkuPerPage.totalPages)

        // when
        val resultTwoSkusperPage = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(pageRequest.copy(pageRequest = Page(1, 2)))
        }
        // then
        assertEquals(daysCount * 2, resultTwoSkusperPage.calculationPage.size)
        assertEquals(2, resultTwoSkusperPage.totalPages)
    }

    @Test
    fun `should not include calculations for DCs that are not requested`() {
        // given
        val ve = "VE"
        val dcCodes = listOf(ve, "BX")
        val skuId = UUID.randomUUID()
        val week = "2022-W01"
        val daysCount = 7
        val singleDayCalculationRecords = dcCodes.map { dcCode ->
            persistCalculations(daysCount, skuId, dcCode, week)
        }.flatten()
        dsl.batchInsert(singleDayCalculationRecords).execute()
        insertSkuRecords(singleDayCalculationRecords.map(::toSkuSpecification).toSet())

        // when
        val result = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(
                calculationRequest.copy(dcCodes = listOf(ve), weeks = listOf(week), sortBy = SKU_CODE),
            )
        }

        // then
        assertEquals(daysCount, result.calculationPage.size)
        result.calculationPage.forEach {
            assertEquals(ve, it.dcCode)
        }
    }

    private fun persistCalculations(
        daysCount: Int,
        skuId: UUID,
        dcCode: String,
        week: String
    ) = (0 until daysCount).map {
        Default.calculationRecord {
            date = LocalDate.now().plusDays(it.toLong())
            this.cskuId = skuId
            this.dcCode = dcCode
            productionWeek = week
        }
    }
}
