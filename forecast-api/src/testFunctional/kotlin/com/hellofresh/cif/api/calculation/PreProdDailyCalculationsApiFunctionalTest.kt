package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.CalculationServiceMapper.toDailyCalculation
import com.hellofresh.cif.api.calculation.SortBy.SKU_CODE
import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.api.calculation.db.CommaSeparatedToSetJsonDeserializer.Companion.deserialize
import com.hellofresh.cif.api.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.api.schema.tables.records.PreProductionCalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.ktor.http.HttpStatusCode
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

class PreProdDailyCalculationsApiFunctionalTest : CalculationFunctionalTest() {

    @Test fun `Should return pre production calculations if consumptionDaysAhead is 1`() {
        val dc = "VE"
        val date = DcWeek(LocalDate.now(zoneId), productionStart).getStartDateInDcWeek(productionStart, zoneId)
        val count = 6

        val testData = insertTestData(count, dc, date, productionStart)
        val preProdDBRecords = testData.keys.sortedBy { it.date }
        runBlocking {
            getDailyCalculationsOkResponse(
                CalculationRequest(
                    listOf(dc),
                    testData.map { it.key.productionWeek },
                    Page(page = 1, skuCount = count),
                    consumptionDaysAhead = 1,
                    sortBy = SKU_CODE,
                ),
            )
                .also {
                    val calculationResults = it.calculations
                    assertEquals(preProdDBRecords.size, it.calculations.size)
                    preProdDBRecords.indices.forEach {
                        val record = mapToCommonCalculation(preProdDBRecords[it], testData[preProdDBRecords[it]]!!)
                        val dailyCalculation = toDailyCalculation(record, usableInventoryEvaluator)
                        val resource = calculationResponseMapper.toDailyCalculationResponse(dailyCalculation)
                        assertEquals(resource, calculationResults.find { v -> v.skuId == record.cskuId }!!)
                    }
                }
        }
    }

    @Test fun `Should return 501 not implemented if consumptionDaysAhead is more than 1`() {
        val dc = "VE"
        val week = "2022-W08"
        val count = 5

        insertTestData(count, dc, week)
        getDailyCalculations(
            CalculationRequest(listOf(dc), listOf(week), Page(page = 1, skuCount = count), consumptionDaysAhead = 5),
        )
            .apply {
                assertEquals(HttpStatusCode.NotImplemented, status)
            }
    }

    @Test fun `Should return 400 bad request if consumptionDaysAhead is less than 1`() {
        val dc = "VE"
        val week = "2022-W08"
        val count = 5

        insertTestData(count, dc, week)
        getDailyCalculations(
            CalculationRequest(listOf(dc), listOf(week), Page(page = 1, skuCount = count), consumptionDaysAhead = -10),
        )
            .apply {
                assertEquals(HttpStatusCode.BadRequest, status)
            }
    }

    private fun insertTestData(
        n: Int,
        dc: String,
        paramDate: LocalDate,
        productionStart: DayOfWeek
    ): Map<PreProductionCalculationRecord, SkuSpecificationRecord> {
        val records = (0 until n).map {
            PreProductionCalculationRecord().apply {
                dcCode = dc
                productionWeek = DcWeek(paramDate, productionStart).value
                cskuId = UUID.randomUUID()
                date = paramDate
                expired = randomQty()
                openingStock = randomQty()
                demanded = randomQty()
                actualConsumption = randomQty()
                present = randomQty()
                closingStock = randomQty()
                actualInbound = randomQty()
                expectedInbound = randomQty()
                actualInboundPo = UUID.randomUUID().toString()
                expectedInboundPo = UUID.randomUUID().toString()
                dailyNeeds = randomQty()
                safetystock = randomQty()
                safetystockNeeds = randomQty()
                netNeeds = randomQty()
                uom = UOM_UNIT
            }
        }.associateWith {
            SkuSpecificationRecord()
                .apply {
                    id = it.cskuId
                    this.code = it.cskuId.toString()
                    name = it.cskuId.toString()
                    this.category = it.cskuId.toString().take(3)
                    coolingType = ""
                    packaging = ""
                    acceptableCodeLife = 0
                    market = ""
                }
        }

        insertSkuRecords(records.map { it.value })
        dsl.batchInsert(records.map { it.key }).execute()
        return records
    }

    private fun mapToCommonCalculation(
        record: PreProductionCalculationRecord,
        skuSpecificationRecord: SkuSpecificationRecord
    ) = CalculationRecord(
        record.dcCode,
        record.cskuId,
        code = skuSpecificationRecord.code,
        name = skuSpecificationRecord.name,
        category = skuSpecificationRecord.category,
        record.productionWeek,
        record.date,
        uom = record.uom,
        record.expired,
        record.openingStock,
        null,
        null,
        record.stockUpdate,
        record.demanded,
        record.present,
        record.closingStock,
        record.actualInbound,
        record.expectedInbound,
        deserialize(record.actualInboundPo),
        deserialize(record.expectedInboundPo),
        record.dailyNeeds,
        coolingType = skuSpecificationRecord.coolingType,
        packaging = skuSpecificationRecord.packaging,
        actualConsumption = record.actualConsumption,
        skuAtRisk = record.dailyNeeds > BigDecimal.ZERO || (record.date < LocalDate.now() && record.expectedInbound > record.actualInbound),
        safetyStock = record.safetystock,
        safetyStockNeeds = record.safetystockNeeds,
        poDueIn = record.maxPurchaseOrderDueIn?.toLong(),
        netNeeds = record.netNeeds,
        acceptableCodeLife = skuSpecificationRecord.acceptableCodeLife,
    )
}
