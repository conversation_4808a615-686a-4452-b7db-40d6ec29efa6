package com.hellofresh.cif.api.shortShelfLife

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTCreator
import com.auth0.jwt.algorithms.Algorithm
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.ErrorResponse
import com.hellofresh.cif.api.calculation.generated.model.Reason.OTHER
import com.hellofresh.cif.api.calculation.generated.model.ShortShelfLifeResponse
import com.hellofresh.cif.api.calculation.generated.model.ShortShelfLifeSimulationResponse
import com.hellofresh.cif.api.calculation.generated.model.ShortShelfLifeUpdateRequest
import com.hellofresh.cif.api.calculation.generated.model.ShortShelfLifeUpdateRequestSqrsInner
import com.hellofresh.cif.api.calculation.generated.model.ShortShelfLifeUpdateRequestStockUpdate
import com.hellofresh.cif.api.calculation.generated.model.SqrSimulation
import com.hellofresh.cif.api.calculation.generated.model.SslSqr
import com.hellofresh.cif.api.calculation.generated.model.SslSqrSimulationRequest
import com.hellofresh.cif.api.calculation.generated.model.SslSqrSimulationRequestDataInner
import com.hellofresh.cif.api.calculation.generated.model.UomEnum.UNIT
import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.api.configuration.DcConfiguration
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.tables.records.SqrShortShelfLifeRecord
import com.hellofresh.cif.api.stockupdate.StockUpdateApiFunctionalTest
import com.hellofresh.cif.api.stockupdate.StockUpdateApiService
import com.hellofresh.cif.api.stockupdate.StockUpdateCalculationService
import com.hellofresh.cif.api.stockupdate.StockUpdateInputDataService
import com.hellofresh.cif.api.stockupdate.StockUpdateResults
import com.hellofresh.cif.api.stockupdate.model.StockUpdateInputDataDetails
import com.hellofresh.cif.calculator.CalculatorClient
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.CleardownData
import com.hellofresh.inventory.models.CleardownMode.SCHEDULED
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.sku.models.SkuSpecification
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType.Application
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.auth.AuthScheme
import io.ktor.http.auth.HttpAuthHeader
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.math.BigDecimal.TEN
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ShortShelfLifeApiTest : FunctionalTest() {
    private val testDcCode = "VE"
    private val testMarket = "DE"
    private val testSkuId = UUID.randomUUID()
    private val dcCode = "VE"
    private val jwksURI = "https://test.com/"

    private val jwtCredentials = JwtCredentials(jwtSecret, "test", "test", "testClient", jwksURI)
    private val dcConfigServiceMock = mockk<DcConfigService>(relaxed = true)
    private val stockUpdateInputDataService = mockk<StockUpdateInputDataService>(relaxed = true)
    private val stockUpdateCalculationService = StockUpdateCalculationService(
        CalculatorClient(StatsigTestFeatureFlagClient(emptySet())),
        stockUpdateInputDataService,
    )
    private val stockUpdateApiService1 = mockk<StockUpdateApiService>(relaxed = true)
    private val stockUpdateService = mockk<StockUpdateService>(relaxed = true)
    private val sqrShortShelfLifeConfRepository = SQRShortShelfLifeConfRepository(
        dsl,
    )
    private val configService = mockk<ConfigService>(relaxed = true)
    private val shortShelfLifeService = ShortShelfLifeService(
        shortShelfLifeRepositoryImpl,
        dcConfigServiceMock,
        stockUpdateApiService1,
        stockUpdateCalculationService,
        sqrShortShelfLifeConfRepository,
        stockUpdateService,
    )
    private val today = LocalDate.now()
    private val productionStartClearDownDay = LocalDate.now().dayOfWeek
    private val currentWeek = DcWeek(today, productionStartClearDownDay)

    @BeforeEach
    internal fun setUp() {
        coEvery {
            dcConfigServiceMock.dcConfigurations[testDcCode]
        } returns DistributionCenterConfiguration.default().copy(zoneId = ZoneId.of("UTC"))
        coEvery { configService.fetchByMarket(any()) } returns mapOf(
            testDcCode to
                DcConfiguration(
                    currentWeek.toString(),
                    testMarket,
                    productionStartClearDownDay,
                    productionStartClearDownDay,
                    ZoneId.of("UTC"),
                    null,
                    false,
                ),
        )
    }

    @Test
    fun `should return short shelf life items for a given dc code and week`() {
        val dcCode = "VE"
        val authorEmail = ""
        val date = LocalDate.now()

        insertSkus(setOf(testSkuId))
        val record1 = createSqrShortShelfLifeRecord(date = date)
        val record2 = createSqrShortShelfLifeRecord(date = date.plusDays(1))
        val record3 = createSqrShortShelfLifeRecord(date = date.plusDays(2))
        val record4 = createSqrShortShelfLifeRecord(date = date.plusDays(3))
        val record5 = createSqrShortShelfLifeRecord(date = date.plusDays(4))

        dsl.batchInsert(record1, record2, record3, record4, record5).execute()
        val dcWeek = DcWeek(LocalDate.now(), LocalDate.now().dayOfWeek)

        // execute http request
        runBlocking {
            get("/sqr-short-shelf-life/$dcCode?week=$dcWeek", authorEmail, "") {
                assertEquals(HttpStatusCode.OK, this.status)

                val response = objectMapper.readValue(
                    bodyAsText(),
                    ShortShelfLifeResponse::class.java,
                )

                assertEquals(1, response.totalNoOfSkus.toInt())
                assertEquals(testSkuId, response.skus.first().skuId)

                assertEquals(BigDecimal(10), response.skus.first().data.first().sqrs.first().consumption)
                assertEquals(BigDecimal(0), response.skus.first().data.first().sqrs.first().bufferPercentage)
                assertEquals(BigDecimal(0), response.skus.first().data.first().sqrs.first().bufferAdditional)
                assertFalse(response.skus.first().data.first().touchlessOrdering!!)
                assertNull(response.skus.first().data.first().sqrs.first().stockUpdates)
                assertNull(response.skus.first().data.first().sqrs.first().stockUpdatesVersion)
                assertNull(response.skus.first().data.first().sqrs.last().stockUpdates)
                assertNull(response.skus.first().data.first().sqrs.last().stockUpdatesVersion)
            }
        }
    }

    @Test
    fun `should return short shelf life items for a given dc code and week after running the simulation`() {
        val dcCode = "VE"
        val authorEmail = ""
        val date = LocalDate.parse("2025-02-13")

        insertSkus(setOf(testSkuId))
        val record1 = createSqrShortShelfLifeRecord(date = date)
        val record2 = createSqrShortShelfLifeRecord(date = date.plusDays(1))
        val record3 = createSqrShortShelfLifeRecord(date = date.plusDays(2))
        val record4 = createSqrShortShelfLifeRecord(date = date.plusDays(3))
        val record5 = createSqrShortShelfLifeRecord(date = date.plusDays(4))
        val record6 = createSqrShortShelfLifeRecord(date = date.plusDays(5))
        val record7 = createSqrShortShelfLifeRecord(date = date.plusDays(6))

        dsl.batchInsert(record1, record2, record3, record4, record5, record6, record7).execute()

        val dcWeek = DcWeek(date, date.dayOfWeek)

        listOf(record1, record2, record3, record4, record5, record6, record7).map {
            createStockUpdateRecord(
                dcCodeParam = dcCode,
                skuIdParam = testSkuId,
                weekParam = dcWeek.value,
                dateParam = it.date,
                qtyParam = BigDecimal(1000),
                versionParam = 1,
            )
        }

        listOf(record1, record2, record3, record4, record5, record6, record7).map {
            insertSQRShortShelfLifeConfiguration(
                it.dcCode,
                it.date,
                it.skuId,
                BigDecimal("100"),
                BigDecimal("1000"),
            )
        }

        val dc = DistributionCenterConfiguration.default().copy(zoneId = ZoneId.of("UTC"))
        val inventory = SkuInventory(UUID.randomUUID(), emptyList())
        val cleardownData = listOf(
            CleardownData(
                dcCode = dcCode,
                cleardownTime = LocalDateTime.parse("2025-02-13T00:00:00.00"),
                cleardownMode = SCHEDULED,
                snapshot = InventorySnapshot(
                    dcCode = dcCode,
                    snapshotId = UUID.randomUUID(),
                    snapshotTime = LocalDateTime.parse("2025-02-13T00:00:00.00"),
                    skus = listOf(inventory),
                ),
            ),
        )
        val inventoryList = listOf(InventorySnapshot(dcCode, UUID.randomUUID(), LocalDateTime.now(), listOf(inventory)))
        coEvery { stockUpdateInputDataService.fetchInputData(dcCode, testSkuId, PRODUCTION) } returns
            StockUpdateInputDataDetails(
                inputData = InputData(
                    mode = PRODUCTION,
                    skuDcCandidates = setOf(
                        SkuDcCandidate(
                            skuId = testSkuId,
                            skuSpecification = SkuSpecification(
                                coolingType = "voluptatibus",
                                name = "Rosa Baxter",
                                packaging = "phasellus",
                                skuCode = "mediocrem",
                                category = "quot",
                                parentId = null,
                                acceptableCodeLife = 6198,
                                market = "sanctus",
                                uom = UOM_UNIT,
                            ),
                            dc = dc,
                        ),
                    ),
                    inventory = InventorySnapshots(
                        inventoryList = inventoryList,
                        liveInventoryList = listOf(),
                        cleardownData = cleardownData,
                        inventoryActivities = listOf(),
                    ),
                    purchaseOrderInbounds = PurchaseOrderInbounds(pos = listOf()),
                    demands = Demands(demandList = listOf()),
                    safetyStocks = mapOf(),
                    stockUpdates = mapOf(
                        CalculationKey(testSkuId, dcCode, date) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                        CalculationKey(testSkuId, dcCode, date.plusDays(1)) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                        CalculationKey(testSkuId, dcCode, date.plusDays(2)) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                        CalculationKey(testSkuId, dcCode, date.plusDays(3)) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                        CalculationKey(testSkuId, dcCode, date.plusDays(4)) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                        CalculationKey(testSkuId, dcCode, date.plusDays(5)) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                        CalculationKey(testSkuId, dcCode, date.plusDays(6)) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                    ),
                    supplierSku = mapOf(),
                    preproductionCleardownDcs = emptySet(),
                ),
                demandConsumptionDetails = emptyMap(),
            )

        val stockUpdate = createStockUpdate(dcCode, date)
        coEvery {
            stockUpdateService.getCurrentStockUpdate(dcCode, testSkuId)
        } returns mapOf(
            date to stockUpdate,
            date.plusDays(4) to stockUpdate.copy(date = date.plusDays(4)),
        )
        // execute http request
        runBlocking {
            get("/sqr-short-shelf-life/$dcCode?week=$dcWeek", authorEmail, "") {
                assertEquals(HttpStatusCode.OK, this.status)

                val response = objectMapper.readValue(
                    bodyAsText(),
                    ShortShelfLifeResponse::class.java,
                )

                assertEquals(1, response.totalNoOfSkus.toInt())
                assertEquals(testSkuId, response.skus.first().skuId)

                val sqrs = response.skus.first().data.first().sqrs
                assertTrue(response.skus.first().data.first().touchlessOrdering!!)
                assertEquals(BigDecimal(100), sqrs.find { it.date == date.plusDays(1) }?.bufferPercentage)
                assertEquals(BigDecimal(1000), sqrs.find { it.date == date.plusDays(1) }?.bufferAdditional)
                assertEquals(BigDecimal(100), sqrs.find { it.date == date.plusDays(2) }?.bufferPercentage)
                assertEquals(BigDecimal(1000), sqrs.find { it.date == date.plusDays(2) }?.bufferAdditional)
                assertEquals(BigDecimal(100), sqrs.find { it.date == date.plusDays(3) }?.bufferPercentage)
                assertEquals(BigDecimal(1000), sqrs.find { it.date == date.plusDays(3) }?.bufferAdditional)
                assertEquals(BigDecimal(2000), sqrs.find { it.date == date.plusDays(5) }?.openingStock)

                assertEquals(BigDecimal(1000), sqrs.find { it.date == date.plusDays(4) }?.stockUpdates)
                assertEquals(1, sqrs.find { it.date == date.plusDays(4) }?.stockUpdatesVersion)
            }
        }
    }

    @Test
    fun `should return short shelf life items for a given dc code and week after running the simulation - two weeks`() {
        val dcCode = "VE"
        val authorEmail = ""
        val date = LocalDate.parse("2025-02-14")
        val date21Feb = LocalDate.parse("2025-02-21")
        val date22Feb = LocalDate.parse("2025-02-22")
        val date23Feb = LocalDate.parse("2025-02-23")
        val date24Feb = LocalDate.parse("2025-02-24")

        insertSkus(setOf(testSkuId))
        val record1 = createSqrShortShelfLifeRecord(date = date)
        val record2 = createSqrShortShelfLifeRecord(date = date.plusDays(1))
        val record3 = createSqrShortShelfLifeRecord(date = date.plusDays(2))
        val record4 = createSqrShortShelfLifeRecord(date = date.plusDays(3))
        val record5 = createSqrShortShelfLifeRecord(date = date.plusDays(4))
        val record6 = createSqrShortShelfLifeRecord(date = date.plusDays(5))
        val record7 = createSqrShortShelfLifeRecord(date = date.plusDays(6))
        val record8 = createSqrShortShelfLifeRecord(date = date21Feb)
        val record9 = createSqrShortShelfLifeRecord(date = date22Feb)
        val record10 = createSqrShortShelfLifeRecord(date = date23Feb)
        val record11 = createSqrShortShelfLifeRecord(date = date24Feb)

        dsl.batchInsert(
            record1, record2, record3, record4, record5, record6,
            record7, record8, record9, record10, record11,
        ).execute()

        val dcWeek = DcWeek(date, date.dayOfWeek)
        val dcWeek1 = DcWeek(date.plusDays(7), date.dayOfWeek)

        listOf(
            record1, record2, record3, record4, record5, record6, record7, record8, record9,
            record10, record11,
        ).map {
            createStockUpdateRecord(
                dcCodeParam = dcCode,
                skuIdParam = testSkuId,
                weekParam = dcWeek.value,
                dateParam = it.date,
                qtyParam = BigDecimal(1000),
                versionParam = 1,
            )
        }

        listOf(
            record1, record2, record3, record4, record5, record6, record7, record8, record9,
            record10, record11,
        ).map {
            insertSQRShortShelfLifeConfiguration(
                it.dcCode,
                it.date,
                it.skuId,
                BigDecimal("100"),
                BigDecimal("1000"),
            )
        }

        val dc = DistributionCenterConfiguration.default().copy(zoneId = ZoneId.of("UTC"))
        val inventory = SkuInventory(UUID.randomUUID(), emptyList())
        val cleardownData = listOf(
            CleardownData(
                dcCode = dcCode,
                cleardownTime = LocalDateTime.parse("2025-02-14T00:00:00.00"),
                cleardownMode = SCHEDULED,
                snapshot = InventorySnapshot(
                    dcCode = dcCode,
                    snapshotId = UUID.randomUUID(),
                    snapshotTime = LocalDateTime.parse("2025-02-14T00:00:00.00"),
                    skus = listOf(inventory),
                ),
            ),
        )
        val inventoryList = listOf(InventorySnapshot(dcCode, UUID.randomUUID(), LocalDateTime.now(), listOf(inventory)))
        coEvery { stockUpdateInputDataService.fetchInputData(dcCode, testSkuId, PRODUCTION) } returns
            StockUpdateInputDataDetails(
                inputData = InputData(
                    mode = PRODUCTION,
                    skuDcCandidates = setOf(
                        SkuDcCandidate(
                            skuId = testSkuId,
                            skuSpecification = SkuSpecification(
                                coolingType = "voluptatibus",
                                name = "Rosa Baxter",
                                packaging = "phasellus",
                                skuCode = "mediocrem",
                                category = "quot",
                                parentId = null,
                                acceptableCodeLife = 6198,
                                market = "sanctus",
                                uom = UOM_UNIT,
                            ),
                            dc = dc,
                        ),
                    ),
                    inventory = InventorySnapshots(
                        inventoryList = inventoryList,
                        liveInventoryList = listOf(),
                        cleardownData = cleardownData,
                        inventoryActivities = listOf(),
                    ),
                    purchaseOrderInbounds = PurchaseOrderInbounds(pos = listOf()),
                    demands = Demands(demandList = listOf()),
                    safetyStocks = mapOf(),
                    stockUpdates = mapOf(
                        CalculationKey(testSkuId, dcCode, date) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                        CalculationKey(testSkuId, dcCode, date.plusDays(1)) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                        CalculationKey(testSkuId, dcCode, date.plusDays(2)) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                        CalculationKey(testSkuId, dcCode, date.plusDays(3)) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                        CalculationKey(testSkuId, dcCode, date.plusDays(4)) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                        CalculationKey(testSkuId, dcCode, date.plusDays(10)) to SkuQuantity.fromLong(1000L, UOM_UNIT),
                    ),
                    supplierSku = mapOf(),
                    preproductionCleardownDcs = emptySet(),
                ),
                demandConsumptionDetails = emptyMap(),
            )
        val stockUpdate = createStockUpdate(dcCode, date)
        coEvery {
            stockUpdateService.getCurrentStockUpdate(dcCode, testSkuId)
        } returns mapOf(
            date to stockUpdate,
            date.plusDays(4) to stockUpdate.copy(date = date.plusDays(4)),
        )

        // execute http request
        runBlocking {
            get("/sqr-short-shelf-life/$dcCode?week=$dcWeek&week=$dcWeek1", authorEmail, "") {
                assertEquals(HttpStatusCode.OK, this.status)

                val response = objectMapper.readValue(
                    bodyAsText(),
                    ShortShelfLifeResponse::class.java,
                )

                assertEquals(1, response.totalNoOfSkus.toInt())
                assertEquals(testSkuId, response.skus.first().skuId)

                val sqrs1 = response.skus.first().data[0].sqrs
                val sqrs2 = response.skus.first().data[1].sqrs
                assertTrue(response.skus.first().data[0].touchlessOrdering!!)
                assertTrue(response.skus.first().data[1].touchlessOrdering!!)
                assertEquals(BigDecimal(100), sqrs1.find { it.date == date.plusDays(1) }?.bufferPercentage)
                assertEquals(BigDecimal(1000), sqrs1.find { it.date == date.plusDays(1) }?.bufferAdditional)
                assertEquals(BigDecimal(100), sqrs1.find { it.date == date.plusDays(2) }?.bufferPercentage)
                assertEquals(BigDecimal(1000), sqrs1.find { it.date == date.plusDays(2) }?.bufferAdditional)
                assertEquals(BigDecimal(100), sqrs1.find { it.date == date.plusDays(3) }?.bufferPercentage)
                assertEquals(BigDecimal(1000), sqrs1.find { it.date == date.plusDays(3) }?.bufferAdditional)
                assertEquals(BigDecimal(2000), sqrs1.find { it.date == date.plusDays(5) }?.openingStock)
                assertEquals(BigDecimal(100), sqrs2.find { it.date == date21Feb }?.bufferPercentage)
                assertEquals(BigDecimal(1000), sqrs2.find { it.date == date21Feb }?.bufferAdditional)
                assertEquals(BigDecimal(100), sqrs2.find { it.date == date22Feb }?.bufferPercentage)
                assertEquals(BigDecimal(1000), sqrs2.find { it.date == date22Feb }?.bufferAdditional)
                assertEquals(BigDecimal(100), sqrs2.find { it.date == date23Feb }?.bufferPercentage)
                assertEquals(BigDecimal(1000), sqrs2.find { it.date == date23Feb }?.bufferAdditional)
                assertEquals(BigDecimal(100), sqrs2.find { it.date == date24Feb }?.bufferPercentage)
                assertEquals(BigDecimal(1000), sqrs2.find { it.date == date24Feb }?.bufferAdditional)

                assertEquals(BigDecimal(1000), sqrs1.find { it.date == date.plusDays(4) }?.stockUpdates)
                assertEquals(1, sqrs1.find { it.date == date.plusDays(4) }?.stockUpdatesVersion)
            }
        }
    }

    @Test
    fun `should be able to simulate short shelf life inputs and produce valid supply quantity recommendation values`() {
        val dcCode = "VE"
        val date = LocalDate.parse("2025-02-14")
        val dayCalculation = createDayCalculationResult(testSkuId, todayParam = date)
        insertSkus(setOf(testSkuId))
        val record1 = createSqrShortShelfLifeRecord(date = date)
        val record2 = createSqrShortShelfLifeRecord(date = date.plusDays(1))
        val record3 = createSqrShortShelfLifeRecord(date = date.plusDays(2))
        val record4 = createSqrShortShelfLifeRecord(date = date.plusDays(3))
        val record5 = createSqrShortShelfLifeRecord(date = date.plusDays(4))
        val record6 = createSqrShortShelfLifeRecord(date = date.plusDays(5))
        val record7 = createSqrShortShelfLifeRecord(date = date.plusDays(6))

        dsl.batchInsert(
            record1,
            record2,
            record3,
            record4,
            record5,
            record6,
            record7,
        ).execute()

        val weeks = setOf("2025-W08")
        val expectedStockUpdate = BigDecimal(1000)

        val dc = DistributionCenterConfiguration.default().copy(zoneId = ZoneId.of("UTC"))
        val inventory = SkuInventory(UUID.randomUUID(), emptyList())
        val cleardownData = listOf(
            CleardownData(
                dcCode = dcCode,
                cleardownTime = LocalDateTime.parse("2025-02-14T00:00:00.00"),
                cleardownMode = SCHEDULED,
                snapshot = InventorySnapshot(
                    dcCode = dcCode,
                    snapshotId = UUID.randomUUID(),
                    snapshotTime = LocalDateTime.parse("2025-02-14T00:00:00.00"),
                    skus = listOf(inventory),
                ),
            ),
        )
        val inventoryList = listOf(InventorySnapshot(dcCode, UUID.randomUUID(), LocalDateTime.now(), listOf(inventory)))
        coEvery { stockUpdateInputDataService.fetchInputData(dcCode, testSkuId, PRODUCTION) } returns
            StockUpdateInputDataDetails(
                inputData = InputData(
                    mode = PRODUCTION,
                    skuDcCandidates = setOf(
                        SkuDcCandidate(
                            skuId = testSkuId,
                            skuSpecification = SkuSpecification(
                                coolingType = "voluptatibus",
                                name = "Rosa Baxter",
                                packaging = "phasellus",
                                skuCode = "mediocrem",
                                category = "quot",
                                parentId = null,
                                acceptableCodeLife = 6198,
                                market = "sanctus",
                                uom = UOM_UNIT,
                            ),
                            dc = dc,
                        ),
                    ),
                    inventory = InventorySnapshots(
                        inventoryList = inventoryList,
                        liveInventoryList = listOf(),
                        cleardownData = cleardownData,
                        inventoryActivities = listOf(),
                    ),
                    purchaseOrderInbounds = PurchaseOrderInbounds(pos = listOf()),
                    demands = Demands(demandList = listOf()),
                    safetyStocks = mapOf(),
                    stockUpdates = mapOf(
                        CalculationKey(testSkuId, dcCode, date) to SkuQuantity.fromBigDecimal(expectedStockUpdate),
                    ),
                    supplierSku = mapOf(),
                    preproductionCleardownDcs = emptySet(),
                ),
                demandConsumptionDetails = emptyMap(),
            )
        coEvery {
            stockUpdateService.getCurrentStockUpdate(dcCode, testSkuId)
        } returns mapOf(
            date to createStockUpdate(dcCode, date),
        )

        // execute http request
        runBlocking {
            post(
                "/sqr-short-shelf-life/simulation/$dcCode/$testSkuId",
                UUID.randomUUID().toString(),
                UUID.randomUUID().toString(),
                SslSqrSimulationRequest(
                    data = listOf(
                        SslSqrSimulationRequestDataInner(
                            week = weeks.first(),
                            sqrs = listOf(
                                SqrSimulation(
                                    date = date,
                                    stockUpdates = expectedStockUpdate,
                                    bufferPercentage = TEN,
                                    bufferAdditional = TEN,
                                    uom = UNIT,
                                ),
                            ),
                        ),
                    ),
                ).toRequestBody(),
                shortShelfLifeServiceParam = shortShelfLifeService,
            ) {
                assertEquals(HttpStatusCode.OK, status)
                val shortShelfLifeSimulationResponse = StockUpdateApiFunctionalTest.objectMapper.readValue<ShortShelfLifeSimulationResponse>(
                    bodyAsText(),
                )
                assertEquals(dcCode, shortShelfLifeSimulationResponse.sslSqr.dcCode)
                assertEquals(testSkuId, shortShelfLifeSimulationResponse.sslSqr.skuId)
                assertEquals(1, shortShelfLifeSimulationResponse.sslSqr.data.size)
                val sslSimulatedResult = shortShelfLifeSimulationResponse.sslSqr.data.first().sqrs.first()
                assertEquals(dayCalculation.date, sslSimulatedResult.date)
                assertEquals(BigDecimal(1000), sslSimulatedResult.stockUpdates)
                assertEquals(1, sslSimulatedResult.stockUpdatesVersion)
                assertFalse(shortShelfLifeSimulationResponse.sslSqr.data.first().touchlessOrdering!!)

                val sslSimulatedResult1 = shortShelfLifeSimulationResponse.sslSqr.data.first().sqrs.find {
                    it.date == date.plusDays(1)
                }
                assertEquals(BigDecimal(1000), sslSimulatedResult1?.openingStock)
                assertEquals(dayCalculation.unusable.getValue(), sslSimulatedResult1?.unusableStock)
                assertEquals(BigDecimal.ZERO, sslSimulatedResult1?.consumption)
            }
        }
    }

    @Test
    fun `should be able to upsert short shelf life and return simulated response`() {
        val putSkuId = UUID.randomUUID()
        val dcCode = "VE"
        val authorEmail = ""
        val date = LocalDate.parse("2025-01-28")
        val week = "2025-W05"
        val weeks = setOf(week)
        val expectedStockUpdate = BigDecimal(1000)
        val dayCalculation = createDayCalculationResult(putSkuId, todayParam = date, productionWeekParam = week)
        // prepare data
        insertSkus(setOf(putSkuId))
        val stockUpdateCalculationService = mockk<StockUpdateCalculationService>(relaxed = true)
        val shortShelfLifeService = ShortShelfLifeService(
            shortShelfLifeRepositoryImpl,
            dcConfigServiceMock,
            stockUpdateApiService1,
            stockUpdateCalculationService,
            sqrShortShelfLifeConfRepository,
            stockUpdateService,
        )

        coEvery {
            stockUpdateCalculationService.runStockUpdatesWithoutUom(
                dcCode,
                putSkuId,
                weeks,
                PRODUCTION,
                mapOf(
                    CalculationKey(putSkuId, dcCode, date) to expectedStockUpdate,
                ),
            )
        } returns StockUpdateResults(
            listOf(dayCalculation.copy(stockUpdate = SkuQuantity.fromBigDecimal(expectedStockUpdate))),
            mapOf(
                CalculationKey(putSkuId, dcCode, date) to expectedStockUpdate,
            ).mapValues { (_, value) -> SkuQuantity.fromBigDecimal(value, UOM_UNIT) },
            emptySet(),
            emptyMap(),
        )

        coEvery {
            stockUpdateService.getCurrentStockUpdate(dcCode, putSkuId)
        } returns mapOf(
            date to StockUpdate(
                skuId = putSkuId,
                dcCode = dcCode,
                date = date,
                week = "2025-W08",
                quantity = SkuQuantity.fromBigDecimal(
                    value = BigDecimal.valueOf(1000),
                    unitOfMeasure = UOM_UNIT,
                ),
                reason = "ad",
                reasonDetail = null,
                authorName = null,
                authorEmail = "<EMAIL>",
                version = 1,
                createdAt = date.atStartOfDay(),
                deleted = false,
            ),
        )

        // execute http request
        runBlocking {
            put(
                "/sqr-short-shelf-life/$dcCode/$putSkuId",
                authorEmail,
                "user",
                ShortShelfLifeUpdateRequest(
                    touchlessOrdering = true,
                    stockUpdate = ShortShelfLifeUpdateRequestStockUpdate(
                        reason = OTHER,
                        reasonDetails = "test",
                    ),
                    sqrs = listOf(
                        ShortShelfLifeUpdateRequestSqrsInner(
                            date = date,
                            stockUpdates = BigDecimal(1000),
                            stockUpdatesVersion = null,
                            bufferPercentage = BigDecimal(10),
                            bufferAdditional = BigDecimal(10),
                        ),
                    ),
                )
                    .toRequestBody(),
                shortShelfLifeServiceParam = shortShelfLifeService,
            ) {
                assertEquals(HttpStatusCode.OK, this.status)

                val response = objectMapper.readValue(
                    bodyAsText(),
                    SslSqr::class.java,
                )

                val data = response.data.first().sqrs.first()
                assertTrue(response.data.first().touchlessOrdering!!)
                assertEquals(putSkuId, response.skuId)
                assertEquals(expectedStockUpdate, data.stockUpdates)
                assertEquals(1, data.stockUpdatesVersion)
                assertEquals(dayCalculation.date, data.date)
                assertEquals(dayCalculation.openingStock.getValue(), data.openingStock)
                assertEquals(dayCalculation.unusable.getValue(), data.unusableStock)
                assertEquals(dayCalculation.demanded.getValue(), data.consumption)
            }
        }
    }

    @Test
    fun `should return bad request when no sqrs provided`() {
        val putSkuId = UUID.randomUUID()
        val dcCode = "VE"
        val authorEmail = ""

        runBlocking {
            put(
                "/sqr-short-shelf-life/$dcCode/$putSkuId",
                authorEmail,
                "user",
                ShortShelfLifeUpdateRequest(
                    stockUpdate = null,
                    sqrs = emptyList(),
                )
                    .toRequestBody(),
            ) {
                assertEquals(HttpStatusCode.BadRequest, this.status)
                val response = objectMapper.readValue(
                    bodyAsText(),
                    ErrorResponse::class.java,
                )

                assertEquals("No Short Shelf Life Config data provided.", response.reason)
            }
        }
    }

    @Test
    fun `should return bad request when wrong body provided`() {
        val putSkuId = UUID.randomUUID()
        val dcCode = "VE"
        val authorEmail = ""

        runBlocking {
            put(
                "/sqr-short-shelf-life/$dcCode/$putSkuId",
                authorEmail,
                "user",
                "wrong body",
            ) {
                assertEquals(HttpStatusCode.InternalServerError, this.status)
                val response = objectMapper.readValue(
                    bodyAsText(),
                    ErrorResponse::class.java,
                )
                response.reason?.let {
                    assertTrue { it.contains("Failed to convert request body") }
                }
            }
        }
    }

    private fun createStockUpdate(dcCode: String, date: LocalDate) = StockUpdate(
        skuId = testSkuId,
        dcCode = dcCode,
        date = date,
        week = "2025-W08",
        quantity = SkuQuantity.fromBigDecimal(
            value = BigDecimal.valueOf(1000),
            unitOfMeasure = UOM_UNIT,
        ),
        reason = "ad",
        reasonDetail = null,
        authorName = null,
        authorEmail = "<EMAIL>",
        version = 1,
        createdAt = date.atStartOfDay(),
        deleted = false,
    )

    private suspend fun put(
        putUrl: String,
        authorEmail: String,
        authorName: String,
        requestBody: String,
        shortShelfLifeServiceParam: ShortShelfLifeService = shortShelfLifeService,
        block: suspend HttpResponse.() -> Unit
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(jwtCredentials, true)
                shortShelfLifeModule(
                    shortShelfLifeServiceParam,
                    timeOutInMillis,
                )()
            }
            response = client.put(putUrl) {
                setBody(requestBody)
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, arrayListOf(""))
            }
        }
        response.block()
    }

    private suspend fun post(
        postUrl: String,
        authorEmail: String,
        authorName: String,
        requestBody: String,
        shortShelfLifeServiceParam: ShortShelfLifeService = shortShelfLifeService,
        block: suspend HttpResponse.() -> Unit,
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(jwtCredentials, true)
                shortShelfLifeModule(
                    shortShelfLifeServiceParam,
                    timeOutInMillis,
                )()
            }
            response = client.post(postUrl) {
                setBody(requestBody)
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, arrayListOf(""))
            }
        }
        response.block()
    }

    private suspend fun get(
        getUrl: String,
        authorEmail: String,
        authorName: String,
        block: suspend HttpResponse.() -> Unit
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(jwtCredentials, true)
                shortShelfLifeModule(
                    shortShelfLifeService,
                    timeOutInMillis,
                )()
            }
            response = client.get(getUrl) {
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, arrayListOf(""))
            }
        }
        response.block()
    }

    private fun <T> T.toRequestBody() = objectMapper.writeValueAsString(this)

    private fun HttpRequestBuilder.addAuthHeader(authorEmail: String, authorName: String, roleClaim: ArrayList<String>) {
        val token = buildJwtTokenWithNoAlgorithm {
            this.withClaim("roleclaim", roleClaim)
            this.withClaim("email", authorEmail)
            this.withClaim("name", authorName)
        }

        return this.header(
            HttpHeaders.Authorization,
            HttpAuthHeader.Single(AuthScheme.Bearer, token),
        )
    }

    private fun buildJwtTokenWithNoAlgorithm(jwtBuilderConf: JWTCreator.Builder.() -> Unit): String =
        JWT.create()
            .withClaim("sub", UUID.randomUUID().toString())
            .withKeyId("keyId")
            .withIssuer(jwtCredentials.issuer)
            .withAudience(jwtCredentials.clientId)
            .also(jwtBuilderConf)
            .sign(Algorithm.none())

    private fun createSqrShortShelfLifeRecord(
        dcCode: DcCode = "VE",
        skuId: UUID = testSkuId,
        date: LocalDate = LocalDate.now(),
        bufferPercentage: BigDecimal = BigDecimal.ZERO,
        bufferAdditional: BigDecimal = BigDecimal.ZERO,
        stockUpdate: BigDecimal? = null,
    ): SqrShortShelfLifeRecord = SqrShortShelfLifeRecord().apply {
        this.dcCode = dcCode
        this.skuId = skuId
        this.date = date
        this.sqr = BigDecimal(10)
        this.openingSock = BigDecimal(10)
        this.unusableStock = BigDecimal(10)
        this.consumption = BigDecimal(10)
        this.stockUpdate = stockUpdate
        this.bufferPercentage = bufferPercentage
        this.bufferAdditional = bufferAdditional
        this.uom = com.hellofresh.cif.api.schema.enums.Uom.UOM_UNIT
        this.touchlessOrderingEnabled = false
    }

    companion object {
        val timeOutInMillis: Duration = Duration.parse("PT1000000S")
        val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
