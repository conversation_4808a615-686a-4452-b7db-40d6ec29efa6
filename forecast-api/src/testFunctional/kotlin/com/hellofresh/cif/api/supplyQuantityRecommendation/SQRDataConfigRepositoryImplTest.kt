package com.hellofresh.cif.api.supplyQuantityRecommendation

import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.SupplyQuantityRecommendationRequest
import com.hellofresh.cif.api.schema.Tables.SAFETY_STOCK_CONF
import com.hellofresh.cif.api.schema.Tables.SUPPLY_QUANTITY_RECOMMENDATION_CONF
import com.hellofresh.cif.safetystock.SafetyStockConfiguration.Companion.DEFAULT_RISK_MULTIPLIER
import java.math.BigDecimal
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.provider.Arguments

class SQRDataConfigRepositoryImplTest : FunctionalTest() {
    @Test
    fun `should upsert supply quantity recommendation configuration`() {
        val skuId = UUID.randomUUID()
        val sqrRequest = SupplyQuantityRecommendationRequest(
            dcCode = "VE",
            week = "2024-W20",
            skuId = skuId,
            recommendationEnabled = true,
            multiWeekEnabled = true,
            safetyMultiplier = BigDecimal("0.5"),
        )

        runBlocking {
            val existingSQRConfigRecord1 = createSQRConfigRecord(
                weekParam = "2024-W21",
                skuIdParam = skuId,
            )
            val existingSQRConfigRecord2 = createSQRConfigRecord(
                weekParam = "2024-W22",
                skuIdParam = skuId,
            )
            val existingSQRConfigRecord3 = createSQRConfigRecord(
                weekParam = "2024-W23",
                skuIdParam = skuId,
            )

            val existingSafetyStockConfigRecord1 = createSafetyStockConfigRecord(
                weekParam = "2024-W21",
                skuIdParam = skuId,
                safetyMultiplierParam = DEFAULT_RISK_MULTIPLIER.times(BigDecimal.TEN),
            )
            val existingSafetyStockConfigRecord2 = createSafetyStockConfigRecord(
                weekParam = "2024-W22",
                skuIdParam = skuId,
                safetyMultiplierParam = DEFAULT_RISK_MULTIPLIER.times(BigDecimal.TEN),
            )
            val existingSafetyStockConfigRecord3 = createSafetyStockConfigRecord(
                weekParam = "2024-W23",
                skuIdParam = skuId,
                safetyMultiplierParam = DEFAULT_RISK_MULTIPLIER.times(BigDecimal.TEN),
            )

            dsl.batchInsert(
                existingSQRConfigRecord1,
                existingSQRConfigRecord2,
                existingSQRConfigRecord3,
                existingSafetyStockConfigRecord1,
                existingSafetyStockConfigRecord2,
                existingSafetyStockConfigRecord3,
            ).execute()
            sqrConfigRepositoryImpl.upsertSQRConfig(sqrRequest.toSQRRequest())
            val insertedSQRConf = dsl.selectFrom(
                SUPPLY_QUANTITY_RECOMMENDATION_CONF,
            ).toList()

            val newSQRConf = insertedSQRConf.find { it.week == "2024-W20" }
            val existingSQRConf1 = insertedSQRConf.find { it.week == "2024-W21" }
            val existingSQRConf2 = insertedSQRConf.find { it.week == "2024-W22" }
            val existingSQRConf3 = insertedSQRConf.find { it.week == "2024-W23" }
            assertEquals(sqrRequest.recommendationEnabled, newSQRConf!!.recommendationEnabled)
            assertEquals(sqrRequest.multiWeekEnabled, newSQRConf.multiWeekEnabled)
            assertEquals(sqrRequest.recommendationEnabled, existingSQRConf1!!.recommendationEnabled)
            assertEquals(sqrRequest.multiWeekEnabled, existingSQRConf1.multiWeekEnabled)
            assertEquals(sqrRequest.recommendationEnabled, existingSQRConf2!!.recommendationEnabled)
            assertEquals(sqrRequest.multiWeekEnabled, existingSQRConf2.multiWeekEnabled)
            assertEquals(sqrRequest.recommendationEnabled, existingSQRConf3!!.recommendationEnabled)
            assertEquals(sqrRequest.multiWeekEnabled, existingSQRConf3.multiWeekEnabled)

            val insertedSafetyStockConf = dsl.selectFrom(
                SAFETY_STOCK_CONF,
            ).toList()

            val newSafetyStockConf = insertedSafetyStockConf.find { it.week == "2024-W20" }
            val existingSafetyStockConf1 = insertedSafetyStockConf.find { it.week == "2024-W21" }
            val existingSafetyStockConf2 = insertedSafetyStockConf.find { it.week == "2024-W22" }
            val existingSafetyStockConf3 = insertedSafetyStockConf.find { it.week == "2024-W23" }

            assertEquals(sqrRequest.safetyMultiplier, newSafetyStockConf!!.riskMultiplier)
            assertEquals(sqrRequest.safetyMultiplier, existingSafetyStockConf1!!.riskMultiplier)
            assertEquals(sqrRequest.safetyMultiplier, existingSafetyStockConf2!!.riskMultiplier)
            assertEquals(sqrRequest.safetyMultiplier, existingSafetyStockConf3!!.riskMultiplier)
        }
    }

    companion object {
        @Suppress("unused")
        @JvmStatic
        fun safetyMultiplier() =
            Stream.of(
                Arguments.of(BigDecimal(0.5)),
                Arguments.of(null),
            )
    }
}
