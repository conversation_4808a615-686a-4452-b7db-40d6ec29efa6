package com.hellofresh.cif.api.supplyQuantityRecommendation

import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.schema.tables.records.SupplyQuantityRecommendationRecord
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.SupplyQuantityRecommendation
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.SupplyQuantityRecommendationRepositoryImpl
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import io.mockk.mockk
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class SupplyQuantityRecommendationRepositoryTest : FunctionalTest() {
    private val dcConfigServiceMock = mockk<DcConfigService>(relaxed = true)
    private val supplyQuantityRecommendationRepository =
        SupplyQuantityRecommendationRepositoryImpl(dsl, dcConfigServiceMock)

    @Test
    fun `should fetch supply quantity recommendations`() {
        val skuSpec1 = UUID.randomUUID()
        val skuSpec2 = UUID.randomUUID()
        insertSkus(setOf(skuSpec1, skuSpec2))
        val sqr1 = createSQRRecord(skuIdParam = skuSpec1)
        val sqr2 = createSQRRecord(skuIdParam = skuSpec2)
        dsl.batchInsert(sqr1, sqr2).execute()
        runBlocking {
            val sqrs = supplyQuantityRecommendationRepository.fetchSupplyQuantityRecommendations(
                sqr1.dcCode,
                sqr1.week,
            )
            assertEquals(2, sqrs.size)
            assertSku(sqrs.find { it.skuId == sqr1.skuId }!!, sqr1)
            assertSku(sqrs.find { it.skuId == sqr2.skuId }!!, sqr2)
        }
    }

    @Test
    fun `should fetch empty list of supply quantity recommendations when dccode and week does not matches`() {
        val skuSpec1 = UUID.randomUUID()
        val skuSpec2 = UUID.randomUUID()
        insertSkus(setOf(skuSpec1, skuSpec2))
        val sqr1 = createSQRRecord(skuIdParam = skuSpec1)
        val sqr2 = createSQRRecord(skuIdParam = skuSpec2)
        dsl.batchInsert(sqr1, sqr2).execute()
        runBlocking {
            val sqrs = supplyQuantityRecommendationRepository.fetchSupplyQuantityRecommendations(
                "NON-EXISTING-DC",
                "NON-EXISTING-WEEK",
            )
            assertNotNull(sqrs)
            assertEquals(0, sqrs.size)
        }
    }

    private fun assertSku(actualSqr: SupplyQuantityRecommendation, expectedSqr: SupplyQuantityRecommendationRecord) {
        assertEquals(expectedSqr.dcCode, actualSqr.dcCode)
        assertEquals(expectedSqr.week, actualSqr.week)
        assertEquals(expectedSqr.uom.name, actualSqr.uom.name)
        assertEquals(expectedSqr.demand, actualSqr.demand)
        assertEquals(expectedSqr.sqr, actualSqr.supplyQuantityRecommendation)
        assertEquals(expectedSqr.recommendationEnabled, actualSqr.recommendationEnabled)
        assertEquals(expectedSqr.multiWeekEnabled, actualSqr.multiWeekEnabled)
        assertEquals(expectedSqr.inventoryRollover, actualSqr.inventoryRollover)
        assertEquals(expectedSqr.safetyStock, actualSqr.safetyStock)
    }
}
