package com.hellofresh.cif.api.demand

import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.api.schema.tables.records.DemandRecord
import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.ZERO
import java.time.DayOfWeek
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit.SECONDS
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class DemandRepositoryFunctionalTest : FunctionalTest() {
    @Test
    fun `should return null for the last Forecast Refresh DateTime when there is no demand`() {
        // given
        dsl.batchInsert(createDcConfigRecord("VE")).execute()

        // when
        val result =
            runBlocking { demandRepository.getLatestDemandUpdates(listOf("VE"), listOf(DcWeek("2023-W01"))) }

        // then
        assertEquals(0, result.size)
    }

    @Test
    fun `should return most recent timestamps for updated forecast for multiple DCs`() {
        // given
        val dcCodes = listOf("BV", "BX")
        val productionStart = MONDAY
        val dcConfigRecords = dcCodes.map { createDcConfigRecord(it, productionStart) }
        dsl.batchInsert(dcConfigRecords).execute()
        val expectedUpdatedAt = LocalDateTime.now().plusMinutes(1)
        val currentDcWeek = DcWeek(LocalDate.now(ZoneOffset.UTC), productionStart)
        dcCodes.forEach {
            val demandMax = createDemandRecord(expectedUpdatedAt, it)
            Companion.dsl.batchInsert(
                (1..3).map { v ->
                    createDemandRecord(LocalDateTime.now().minusDays(v.toLong()), dcCode = it)
                } + demandMax,
            ).execute()
        }

        // when
        val result =
            runBlocking { demandRepository.getLatestDemandUpdates(dcCodes, listOf(currentDcWeek)) }

        // then
        assertEquals(dcCodes.size, result.size)
        result.entries.forEach {
            it.value.forEach { v ->
                assertEquals(
                    expectedUpdatedAt.truncatedTo(SECONDS).atOffset(ZoneOffset.UTC),
                    v.latestDemandTime.truncatedTo(SECONDS),
                )
            }
        }
    }

    @Test
    fun `should return a max updated last forecast refresh date time for the DC`() {
        // given
        val expectedUpdatedAt = LocalDateTime.now().plusMinutes(1)
        val productionStart = WEDNESDAY
        val demandLatest = createDemandRecord(expectedUpdatedAt)

        val dcCode = "VE"
        dsl.batchInsert(createDcConfigRecord(dcCode, productionStart)).execute()
        dsl.batchInsert(
            (1..3).map {
                createDemandRecord(
                    LocalDateTime.now().minusDays(it.toLong()),
                )
            } + demandLatest,
        ).execute()

        // when
        val result =
            runBlocking {
                demandRepository.getLatestDemandUpdates(
                    listOf(dcCode),
                    listOf(DcWeek(LocalDate.now(ZoneOffset.UTC), productionStart)),
                )
            }

        // then
        assertEquals(1, result.size)
        val demandTimestamps = result[dcCode]!!
        assertEquals(1, demandTimestamps.size)
        assertEquals(
            expectedUpdatedAt.atOffset(ZoneOffset.UTC).truncatedTo(SECONDS),
            demandTimestamps.first().latestDemandTime.truncatedTo(SECONDS),
        )
    }

    @Test
    fun `should return demand per market`() {
        // given
        dsl.batchInsert(
            createDcConfigRecord("VE")
        ).execute()
        dsl.batchInsert(
            createCalculationRecord("2023-W01", UUID.randomUUID(), LocalDateTime.now(), LocalDateTime.now(), ONE)
        ).execute()

        // when
        val result =
            runBlocking { demandRepository.getDemandByMarket(listOf(DcWeek("2023-W01")), "DACH", null) }

        // then
        assertEquals(1, result.marketDemand.size)
    }

    @Test
    fun `should return only demand with older updated timestamp after the request timestamp`() {
        // given
        val timeStamp = LocalDateTime.now()
        val olderUUid = UUID.randomUUID()
        val newerUUid = UUID.randomUUID()

        dsl.batchInsert(
            createDcConfigRecord("VE")
        ).execute()
        dsl.batchInsert(
            createCalculationRecord(
                "2023-W01",
                newerUUid,
                timeStamp,
                timeStamp.plusDays(1),
                ONE,
            ),
        ).execute()
        dsl.batchInsert(
            createCalculationRecord(
                "2023-W01",
                olderUUid,
                createdAt = timeStamp,
                updatedAt = timeStamp,
                ONE,
            ),
        ).execute()

        // when
        val result =
            runBlocking { demandRepository.getDemandByMarket(listOf(DcWeek("2023-W01")), "DACH", timeStamp) }

        // then
        assertEquals(1, result.marketDemand.size)
        assertEquals(newerUUid, result.marketDemand[0].skuId)
    }

    @Test
    fun `should return only demand with createdAt after the request timestamp when updated At is null`() {
        // given
        val timeStamp = LocalDateTime.now()
        val nullUpdatedAtUUID = UUID.randomUUID()
        val olderUUid = UUID.randomUUID()
        val newerUUid = UUID.randomUUID()

        dsl.batchInsert(createDcConfigRecord("VE")).execute()
        dsl.batchInsert(
            createCalculationRecord(
                "2023-W01",
                nullUpdatedAtUUID,
                createdAt = timeStamp.plusDays(1),
                updatedAt = null,
                ONE
            ),
        ).execute()
        dsl.batchInsert(
            createCalculationRecord(
                "2023-W01",
                newerUUid,
                createdAt = timeStamp.minusDays(1),
                updatedAt = timeStamp.plusDays(1),
                ONE
            ),
        ).execute()
        dsl.batchInsert(
            createCalculationRecord(
                "2023-W01",
                olderUUid,
                createdAt = timeStamp,
                updatedAt = null,
                ONE
            ),
        ).execute()

        // when
        val result =
            runBlocking { demandRepository.getDemandByMarket(listOf(DcWeek("2023-W01")), "DACH", timeStamp) }

        // then
        assertEquals(2, result.marketDemand.size)
        assertEquals(true, result.marketDemand.map { it.skuId }.toList().contains(newerUUid))
        assertEquals(true, result.marketDemand.map { it.skuId }.toList().contains(nullUpdatedAtUUID))
    }

    @Test
    fun `deamnd by market should return nothing when demand is zero`() {
        // given
        dsl.batchInsert(
            createDcConfigRecord("VE")
        ).execute()
        dsl.batchInsert(
            createCalculationRecord("2023-W01", UUID.randomUUID(), LocalDateTime.now(), LocalDateTime.now(), ZERO)
        ).execute()

        // when
        val result =
            runBlocking { demandRepository.getDemandByMarket(listOf(DcWeek("2023-W01")), "DACH", null) }

        // then
        assertEquals(0, result.marketDemand.size)
    }
    private fun createDemandRecord(
        updatedAt: LocalDateTime,
        dcCode: String = "VE",
    ) = DemandRecord(
        UUID.randomUUID(),
        dcCode,
        LocalDate.now(),
        LocalDateTime.now(),
        LocalDateTime.now(),
        updatedAt,
        null,
        BigDecimal(100L),
        null,
        null,
        null,
        com.hellofresh.cif.api.schema.enums.Uom.UOM_UNIT
    )

    private fun createDcConfigRecord(dcCode: String, productionStart: DayOfWeek = MONDAY) = DcConfigRecord().apply {
        this.dcCode = dcCode
        market = "DACH"
        this.productionStart = productionStart.name
        cleardown = "FRIDAY"
        zoneId = "Europe/Berlin"
        enabled = true
        hasCleardown = true
        recordTimestamp_ = LocalDateTime.now()
        createdAt = LocalDateTime.now()
        updatedAt = LocalDateTime.now()
    }

    private fun createCalculationRecord(week: String, cskuId: UUID, createdAt: LocalDateTime?, updatedAt: LocalDateTime?, demandQty: BigDecimal) =
        CalculationRecord().apply {
            this.dcCode = "VE"
            this.productionWeek = week
            this.cskuId = cskuId
            this.date = LocalDate.now()
            this.demanded = demandQty
            this.createdAt = createdAt
            this.updatedAt = updatedAt
        }
}
