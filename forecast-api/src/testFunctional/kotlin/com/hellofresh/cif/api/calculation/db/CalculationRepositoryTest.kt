package com.hellofresh.cif.api.calculation.db

import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_INBOUND_SHORTAGE
import com.hellofresh.cif.api.calculation.AllPages
import com.hellofresh.cif.api.calculation.CalculationFunctionalTest
import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.InventoryRefreshType.LIVE
import com.hellofresh.cif.api.calculation.SortBy
import com.hellofresh.cif.api.calculation.fixtures.Default
import com.hellofresh.cif.api.calculation.fixtures.RandomFixture
import com.hellofresh.cif.api.calculation.fixtures.calculationRecord
import com.hellofresh.cif.api.calculation.fixtures.liveInventoryCalculationRecord
import com.hellofresh.cif.api.toSkuSpecification
import com.hellofresh.inventory.models.LocationType
import java.math.BigDecimal
import java.math.BigDecimal.TEN
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.junit.jupiter.api.Test

private const val NUM_OF_RECORDS = 1000

class CalculationRepositoryTest : CalculationFunctionalTest() {

    private val dcCodeVE = "VE"
    private val dcCodesVE = listOf(dcCodeVE)
    private val poRef = "PO1_001"
    private val calculationRequestAll = CalculationRequest(dcCodesVE, emptyList(), AllPages, consumptionDaysAhead = 0)

    @Test
    fun `should return empty results when there is no record`() {
        runBlocking {
            val result =
                prodCalculationsRepo.fetchPageableCalculations(calculationRequestAll.copy(weeks = listOf("2022-W01")))
            assertTrue(result.calculationPage.isEmpty())
            assertEquals(1, result.totalPages)
        }
    }

    @Test
    fun `should fetch all daily calculations without paginating`() {
        // given
        val week = "2022-W01"
        val calculationRecords = (1..NUM_OF_RECORDS)
            .map { index ->
                Default.calculationRecord {
                    cskuId = UUID.randomUUID()
                    dcCode = dcCodeVE
                    productionWeek = week
                    expectedInboundPo = poRef.format() + index
                    actualInboundPo = null
                }
            }

        dsl.batchInsert(calculationRecords).execute()
        insertSkuRecords(calculationRecords.map(::toSkuSpecification))

        // when
        val pageableCalculations = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(
                calculationRequestAll.copy(
                    weeks = listOf(week),
                    sortBy = SortBy.SKU_CODE,
                ),
            )
        }
        val dailyViews = pageableCalculations.calculationPage

        // then
        assertEquals(calculationRecords.size, dailyViews.size)
        assertEquals(1, pageableCalculations.totalPages)
    }

    @Test
    fun `should fetch all daily calculations with unusable inventory`() {
        // given
        val week = "2022-W01"
        val calculationRecords = (1..1)
            .map { index ->
                Default.calculationRecord {
                    cskuId = UUID.randomUUID()
                    dcCode = dcCodeVE
                    productionWeek = week
                    expectedInboundPo = poRef.format() + index
                    actualInboundPo = null
                    unusableInventory = JSONB.valueOf(
                        "[{\n" +
                            "  \"qty\": 50,\n" +
                            "  \"expiry_date\": \"2022-12-31\",\n" +
                            "  \"state\": {\n" +
                            "    \"original\": \"USABLE\",\n" +
                            "    \"state\": \"USABLE\"\n" +
                            "  },\n" +
                            "  \"location_type\": \"LOCATION_TYPE_QUARANTINE\"\n" +
                            "},\n" +
                            "    {\n" +
                            "        \"qty\": 150,\n" +
                            "        \"state\": {\n" +
                            "            \"value\": \"USABLE\",\n" +
                            "            \"original\": \"USABLE\"\n" +
                            "        },\n" +
                            "        \"expiry_date\": \"2024-07-08\",\n" +
                            "        \"location_type\": \"LOCATION_TYPE_WASTE\"\n" +
                            "    }]",
                    )
                }
            }

        dsl.batchInsert(calculationRecords).execute()
        insertSkuRecords(calculationRecords.map(::toSkuSpecification))

        // when
        val pageableCalculations = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(
                calculationRequestAll.copy(
                    weeks = listOf(week),
                    sortBy = SortBy.SKU_CODE,
                ),
            )
        }
        val dailyViews = pageableCalculations.calculationPage

        // then
        assertEquals(2, dailyViews[0].unusableStockDetails?.size)
        assertEquals(
            50.toBigDecimal(),
            dailyViews[0].unusableStockDetails?.firstOrNull {
                it.expiryDate.toString() == "2022-12-31"
            }?.qty,
        )
        assertEquals(
            LocationType.LOCATION_TYPE_QUARANTINE,
            dailyViews[0].unusableStockDetails?.firstOrNull { it.expiryDate.toString() == "2022-12-31" }?.locationType,
        )
        assertEquals(
            150.toBigDecimal(),
            dailyViews[0].unusableStockDetails?.firstOrNull {
                it.expiryDate.toString() == "2024-07-08"
            }?.qty,
        )
        assertEquals(
            LocationType.LOCATION_TYPE_WASTE,
            dailyViews[0].unusableStockDetails?.firstOrNull { it.expiryDate.toString() == "2024-07-08" }?.locationType,
        )
    }

    @Test
    fun `should not return skus which has inbound shortage for today and future`() {
        val today = LocalDate.now()
        val week = "2022-W01"
        val yesterdayRecord = Default.calculationRecord {
            cskuId = UUID.randomUUID()
            actualInbound = TEN
            expectedInbound = BigDecimal(50)
            date = today.minusDays(1)
            dcCode = dcCodeVE
            productionWeek = week
        }

        val todayRecord = Default.calculationRecord {
            cskuId = UUID.randomUUID()
            actualInbound = BigDecimal(100)
            expectedInbound = BigDecimal(500)
            date = today
            dcCode = dcCodeVE
            productionWeek = week
        }

        val tomorrowRecord = Default.calculationRecord {
            cskuId = UUID.randomUUID()
            actualInbound = BigDecimal(200)
            expectedInbound = BigDecimal(500)
            date = today.plusDays(1)
            dcCode = dcCodeVE
            productionWeek = week
        }

        dsl.batchInsert(
            yesterdayRecord,
            todayRecord,
            tomorrowRecord,

        ).execute()
        insertSkuRecords(
            toSkuSpecification(yesterdayRecord),
            toSkuSpecification(todayRecord),
            toSkuSpecification(tomorrowRecord),
        )
        refreshSkuView()

        val results = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(
                calculationRequestAll.copy(
                    dcCodes = dcCodesVE,
                    additionalFilters = setOf(WITH_INBOUND_SHORTAGE),
                    weeks = listOf(week),
                ),
            )
        }

        assertEquals(1, results.calculationPage.size)
        assertEquals(yesterdayRecord.cskuId, results.calculationPage.first().cskuId)
    }

    @Test
    fun `should return the safety stock quantity for a calculation`() {
        // given
        val safetyStock = BigDecimal(9)
        val safetyStockNeeds = BigDecimal(11)
        val calculation = newCalculationRecord().apply {
            this.safetystock = safetyStock
            this.safetystockNeeds = safetyStockNeeds
        }
        val skuSpecificationRecord = toSkuSpecification(calculation)
        dsl.batchInsert(calculation).execute()
        insertSkuRecords(skuSpecificationRecord)

        // when
        val results = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(
                calculationRequestAll.copy(weeks = listOf(calculation.productionWeek)),
            )
        }

        // then
        assertEquals(1, results.calculationPage.size)
        assertEquals(safetyStock, results.calculationPage.first().safetyStock)
        assertEquals(safetyStockNeeds, results.calculationPage.first().safetyStockNeeds)
    }

    @Test
    fun `should return staging stock quantity for a calculation and live inventory refresh type`() {
        // given
        val stagingStockQty = BigDecimal(10)
        val storageStockQty = BigDecimal(20)
        val liveInventoryCalculationRecord = Default.liveInventoryCalculationRecord {
            this.cskuId = UUID.randomUUID()
            this.date = LocalDate.now()
            this.dcCode = dcCodeVE
            this.productionWeek = "2022-W01"
            this.storageStock = storageStockQty
            this.stagingStock = stagingStockQty
        }
        val skuSpecificationRecord = toSkuSpecification(liveInventoryCalculationRecord)
        dsl.batchInsert(liveInventoryCalculationRecord).execute()
        insertSkuRecords(skuSpecificationRecord)

        // when
        val results = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(
                calculationRequestAll
                    .copy(
                        weeks = listOf(liveInventoryCalculationRecord.productionWeek),
                        inventoryRefreshType = LIVE,
                    ),
            )
        }

        // then
        assertEquals(1, results.calculationPage.size)
        assertEquals(storageStockQty, results.calculationPage.first().storageStock)
        assertEquals(stagingStockQty, results.calculationPage.first().stagingStock)
    }

    @Test
    fun `should fetch calculations with decimal numbers`() {
        // given
        val week = "2022-W01"
        val skuId = UUID.randomUUID()
        val date1 = LocalDate.now()
        val date2 = LocalDate.now().plusDays(1)
        val calculationRecords = (1..2)
            .map { index ->
                RandomFixture().calculationRecord {
                    cskuId = skuId
                    dcCode = dcCodeVE
                    productionWeek = week
                    date = if (index == 1) date1 else date2
                }
            }

        dsl.batchInsert(calculationRecords).execute()
        insertSkuRecords(toSkuSpecification(calculationRecords[1]))

        // when
        val pageableCalculations = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(
                calculationRequestAll.copy(
                    weeks = listOf(week),
                    sortBy = SortBy.SKU_CODE,
                ),
            )
        }
        val dailyViews = pageableCalculations.calculationPage

        // then
        assertEquals(calculationRecords.size, dailyViews.size)

        dailyViews.forEach { dailyView ->

            val record = calculationRecords.first { it.date == dailyView.date }

            assertEquals(record.openingStock, dailyView.openingStock)
            assertEquals(record.expired, dailyView.expired)
            assertEquals(record.expectedInbound, dailyView.expectedInbound)
            assertEquals(record.actualInbound, dailyView.actualInbound)
            assertEquals(record.demanded, dailyView.demanded)
            assertEquals(record.dailyNeeds, dailyView.dailyNeeds)
            assertEquals(record.closingStock, dailyView.closingStock)
            assertEquals(record.actualConsumption, dailyView.actualConsumption)
            assertEquals(record.safetystock, dailyView.safetyStock)
            assertEquals(record.safetystockNeeds, dailyView.safetyStockNeeds)
            assertEquals(record.netNeeds, dailyView.netNeeds)
            assertEquals(record.stockUpdate, dailyView.stockUpdate)
        }
    }

    private fun newCalculationRecord(
        skuId: UUID = UUID.randomUUID(),
        date: LocalDate = LocalDate.now(),
        week: String = "2022-W01"
    ) = Default.calculationRecord {
        this.cskuId = skuId
        this.date = date
        this.dcCode = dcCodeVE
        this.productionWeek = week
    }
}
