package com.hellofresh.cif.api.fileupload

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.api.schema.enums.FileType
import com.hellofresh.cif.api.schema.tables.records.FileUploadsRecord
import com.hellofresh.cif.db.metrics.withMetrics
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.ZoneOffset
import java.util.concurrent.Executors
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class FileUploadRepositoryImplTest : FunctionalTest() {

    private val datasource = getMigratedDataSource()
    private val dbConfig = DefaultConfiguration().apply {
        setSQLDialect(POSTGRES)
        setDataSource(datasource)
        setExecutor(Executors.newSingleThreadExecutor())
    }
    private val dsl = DSL.using(dbConfig).withMetrics(SimpleMeterRegistry())

    @BeforeEach
    fun cleanupDb() {
        dsl.deleteFrom(Tables.FILE_UPLOADS).execute()
    }

    @ParameterizedTest(name = "{index} => {1}")
    @MethodSource("provideInputsForFileUploads")
    fun `test get file uploads by provided market and file type`(existingFileUpload: FileUploadsRecord) {
        dsl.insertInto(Tables.FILE_UPLOADS).set(existingFileUpload).execute()

        runBlocking {
            val fileUploads =
                fileUploadRepository.fetchFileUploads(DACH_MARKET, existingFileUpload.fileType.literal).first()

            assertEquals(existingFileUpload.id, fileUploads.id)
            assertEquals(existingFileUpload.fileName, fileUploads.fileName)
            assertEquals(existingFileUpload.market, fileUploads.market)
            assertEquals(existingFileUpload.dcs.first(), fileUploads.dcCodes.first())
            assertEquals(existingFileUpload.authorName, fileUploads.authorName)
            assertEquals(existingFileUpload.authorEmail, fileUploads.authorEmail)
            assertEquals(
                existingFileUpload.createdAt.atOffset(ZoneOffset.UTC).toEpochSecond(),
                fileUploads.createdAt.toEpochSecond(),
            )
            assertEquals(existingFileUpload.status.name, fileUploads.status.name)
        }
    }

    @Test
    fun `test file uploads is empty by provided market and file type`() {
        val existingFileUpload = getFileUploadStockInventoryData(DACH_MARKET)
        dsl.insertInto(Tables.FILE_UPLOADS).set(existingFileUpload).execute()

        runBlocking {
            val fileUploads = fileUploadRepository.fetchFileUploads("TEST", FileType.STOCK_INVENTORY.literal)
            assertTrue(fileUploads.isEmpty())
        }
    }

    @Test
    fun `test file uploads is failed by unknown file type`() {
        val existingFileUpload = getFileUploadStockInventoryData(DACH_MARKET)
        dsl.insertInto(Tables.FILE_UPLOADS).set(existingFileUpload).execute()

        assertThrows(IllegalStateException::class.java) {
            runBlocking {
                fileUploadRepository.fetchFileUploads("TEST", "TEST")
            }
        }
    }

    companion object {
        @Suppress("unused")
        @JvmStatic
        fun provideInputsForFileUploads(): Stream<Arguments> = Stream.of(
            Arguments.of(
                getFileUploadStockInventoryData(DACH_MARKET),
                "should process the StockInventory file upload",
            ),
            Arguments.of(
                getFileUploadStockUpdateData(DACH_MARKET),
                "should process the StockUpdate file upload",
            ),
        )
    }
}
