package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.calculation.InventoryRefreshType.CLEARDOWN
import com.hellofresh.cif.api.calculation.InventoryRefreshType.LIVE
import com.hellofresh.cif.api.calculation.SortBy.SKU_CODE
import com.hellofresh.cif.api.calculation.SortBy.SKU_NAME
import com.hellofresh.cif.api.calculation.fixtures.RandomFixture
import com.hellofresh.cif.api.calculation.fixtures.calculationRecord
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.toSkuSpecification
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.Supplier
import com.hellofresh.cif.models.purchaseorder.TimeRange
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.DayOfWeek.FRIDAY
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource

@Suppress("LargeClass", "MagicNumber")
internal class CalculationsApiFunctionalTest : CalculationFunctionalTest() {
    private val dcCodeVE = "VE"
    private val dcCodesVE = listOf(dcCodeVE)
    private val dcWeek = "2022-W08"
    private val jwksURI = "https://test.com"

    @Test fun `number of pages and total Skus count are accurate and response is formatted as per docs`() {
        // Number of calculations to insert in the DB.
        val count = 12
        insertTestData(count, dcCodeVE, dcWeek, accConsumption = BigDecimal(100), netNeedsParam = BigDecimal(10))
        val dailyCalculationsResponse =
            getDailyCalculationsOkResponse(
                CalculationRequest(dcCodesVE, listOf(dcWeek), Page(page = 1, skuCount = 5), consumptionDaysAhead = 0),
            )

        assertEquals(3, dailyCalculationsResponse.totalPages)
        assertEquals(count, dailyCalculationsResponse.totalSkusCount)
        assertEquals(BigDecimal(100), dailyCalculationsResponse.calculations[0].actualConsumption)
        assertEquals(BigDecimal(10), dailyCalculationsResponse.calculations[0].netNeeds)
    }

    @Test fun `pagination is correct`() {
        val numberOfSkus = 12
        insertTestData(numberOfSkus, dcCodeVE, dcWeek, accConsumption = BigDecimal(100))
        val page = Page(page = 1, 5)
        val dailyCalculations1 =
            getDailyCalculationsOkResponse(
                CalculationRequest(dcCodesVE, listOf(dcWeek), page, consumptionDaysAhead = 0),
            )

        val page2 =
            getDailyCalculationsOkResponse(
                CalculationRequest(dcCodesVE, listOf(dcWeek), page.copy(page = 2), consumptionDaysAhead = 0),
            ).calculations

        val page3 =
            getDailyCalculationsOkResponse(
                CalculationRequest(dcCodesVE, listOf(dcWeek), page.copy(page = 3), consumptionDaysAhead = 0),
            ).calculations

        assertEquals(page.skuCount, dailyCalculations1.calculations.size, "Page 1 sku count incorrect")
        assertNull(dailyCalculations1.calculations.first().poDueIn)
        assertEquals(page.skuCount, page2.size, "Page 2 sku count incorrect")
        assertEquals(2, page3.size, "Page 3 sku count incorrect")
        assertEquals(numberOfSkus, dailyCalculations1.totalSkusCount)
        assertEquals(
            0,
            getDailyCalculationsOkResponse(
                CalculationRequest(dcCodesVE, listOf(dcWeek), page.copy(page = 4), consumptionDaysAhead = 0),
            )
                .calculations.size,
        )
    }

    @Test
    fun `should return one calculation page for 2 skus and 2 skus per page is requested`() {
        val dailyCalculationsCount = 7
        val skus = mutableListOf<Sku>()
        for (i in 0..1) {
            val sku = Sku("sku code$i", UUID.randomUUID())
            insertSkuCalculations(sku, dailyCalculationsCount, dcCodeVE, dcWeek)
            skus.add(sku)
        }
        val twoSkusPage = Page(page = 1, 2)

        val dailyCalculationsResponse =
            getDailyCalculationsOkResponse(
                CalculationRequest(dcCodesVE, listOf(dcWeek), twoSkusPage, consumptionDaysAhead = 0),
            )

        assertEquals(1, dailyCalculationsResponse.totalPages)
        assertEquals(2, dailyCalculationsResponse.totalSkusCount)
        assertEquals(dailyCalculationsCount * twoSkusPage.skuCount, dailyCalculationsResponse.calculations.size)
        for (i in 0..1) {
            dailyCalculationsResponse.calculations.safeSubList(
                i * dailyCalculationsCount,
                i * dailyCalculationsCount + dailyCalculationsCount,
            )
                .forEach { skus.map { it.skuCode }.contains(it.skuCode) }
        }
    }

    @Test
    fun `should return 3 pages for 7 skus when 3 skus per page is requested`() {
        val dailyCalculationsCount = 7
        val skus = mutableListOf<Sku>()
        for (i in 0..6) {
            val sku = Sku("sku code$i", UUID.randomUUID())
            insertSkuCalculations(sku, dailyCalculationsCount, dcCodeVE, dcWeek)
            skus.add(sku)
        }
        val threeSkusPage = Page(page = 1, 3)

        val calculationsPage =
            getDailyCalculationsOkResponse(
                CalculationRequest(dcCodesVE, listOf(dcWeek), threeSkusPage, consumptionDaysAhead = 0),
            )

        assertEquals(3, calculationsPage.totalPages)
        assertEquals(7, calculationsPage.totalSkusCount)
        assertEquals(dailyCalculationsCount * threeSkusPage.skuCount, calculationsPage.calculations.size)
        for (i in 0..2) {
            calculationsPage.calculations.safeSubList(
                i * dailyCalculationsCount,
                i * dailyCalculationsCount + dailyCalculationsCount,
            )
                .forEach { skus.map { it.skuCode }.contains(it.skuCode) }
        }
    }

    @Test fun `returns 400 Bad request when week is not given`() {
        runBlocking {
            getDailyCalculations(
                CalculationRequest(dcCodesVE, listOf(), Page(page = 1, 50), consumptionDaysAhead = 0),
            ).apply {
                assertEquals(HttpStatusCode.BadRequest, status)
                assertEquals("""{"reason":"Required Parameter: weeks is not specified"}""", bodyAsText())
            }
        }
    }

    @Test
    fun `unauthorized HTTP response when no jwt token is given for daily calculations`() {
        runBlocking {
            getDailyCalculations(
                CalculationRequest(dcCodesVE, listOf(), Page(page = 1, 50), consumptionDaysAhead = 0),
                jwtEnabled = false,
            ).apply {
                assertEquals(HttpStatusCode.Unauthorized, status)
            }
        }
    }

    @Test
    fun `unauthorized HTTP response when no jwt token is given for weekly calculations`() {
        runBlocking {
            getWeeklyCalculations(
                CalculationRequest(dcCodesVE, listOf(), Page(page = 1, 50), consumptionDaysAhead = 0),
                jwtEnabled = false,
            ).apply {
                assertEquals(HttpStatusCode.Unauthorized, status)
            }
        }
    }

    @Test fun `correctly filters on skuCode and skuCategory`() {
        val skuCount = 5
        val code = "some code"
        val category = "PTN"
        // We want to have more than 1 page and have some offset in the last page
        val count = 2 * skuCount + 2
        insertTestData(count, dcCodeVE, dcWeek, code = code, category = category, accConsumption = BigDecimal(100))

        val pageWithCode = getDailyCalculationsOkResponse(
            CalculationRequest(
                dcCodesVE,
                listOf(dcWeek),
                Page(page = 1, skuCount),
                listOf(code),
                listOf(),
                consumptionDaysAhead = 0,
            ),
        ).calculations

        assertEquals(1, pageWithCode.size)
        assertEquals(code, pageWithCode.first().skuCode)

        val pageWithCategory = getDailyCalculationsOkResponse(
            CalculationRequest(
                dcCodesVE,
                listOf(dcWeek),
                Page(page = 1, skuCount),
                listOf(),
                listOf(category),
                consumptionDaysAhead = 0,
            ),
        ).calculations

        assertEquals(1, pageWithCategory.size)
        assertEquals(category, pageWithCategory.first().skuCategories)
        assertEquals(BigDecimal(100), pageWithCategory[0].actualConsumption)
    }

    @Test fun `should get the calculations with actual consumption`() {
        val code = "some code"
        val category = "PTN"
        insertTestData(
            1,
            dcCodeVE,
            dcWeek,
            code = code,
            category = category,
            accConsumption = BigDecimal(100),
            netNeedsParam = BigDecimal(10),
        )

        val calculationWithActualConsumption = getDailyCalculationsOkResponse(
            CalculationRequest(
                dcCodesVE,
                listOf(dcWeek),
                Page(page = 1, 1),
                listOf(code),
                listOf(),
                consumptionDaysAhead = 0,
            ),
        ).calculations

        assertEquals(1, calculationWithActualConsumption.size)
        assertEquals(BigDecimal(100), calculationWithActualConsumption[0].actualConsumption)
        assertEquals(BigDecimal(10), calculationWithActualConsumption[0].netNeeds)
    }

    @Test fun `should get the calculations with purchase order due in`() {
        val code = "some code"
        val category = "PTN"
        insertTestData(
            1,
            dcCodeVE,
            dcWeek,
            code = code,
            category = category,
            accConsumption = BigDecimal(100),
            poDueIn = 10,
            netNeedsParam = BigDecimal(10),
        )

        val calculationWithPoDueIn = getDailyCalculationsOkResponse(
            CalculationRequest(
                dcCodesVE,
                listOf(dcWeek),
                Page(page = 1, 1),
                listOf(code),
                listOf(),
                consumptionDaysAhead = 0,
            ),
        ).calculations

        assertEquals(1, calculationWithPoDueIn.size)
        assertEquals(10, calculationWithPoDueIn[0].poDueIn)
        assertEquals(BigDecimal(10), calculationWithPoDueIn[0].netNeeds)
    }

    @Test fun `should get the calculations with closingStock`() {
        val code = "some code"
        val closingStock = BigDecimal(123)
        insertTestData(1, dcCodeVE, dcWeek, code = code, closeStock = closingStock)

        val calculationWithClosingStock = getDailyCalculationsOkResponse(
            CalculationRequest(
                dcCodesVE,
                listOf(dcWeek),
                Page(page = 1, 1),
                listOf(code),
                listOf(),
                consumptionDaysAhead = 0,
            ),
        ).calculations

        assertEquals(closingStock, calculationWithClosingStock.first().closingStock)
    }

    @Test fun `get calculations using location-in-box filters`() {
        val locationInBox = "Assembly-Cool Pouch"
        val dailyCalculationsCount = 1
        val expectedSku = Sku(UUID.randomUUID().toString(), UUID.randomUUID())
        val sku2 = Sku(UUID.randomUUID().toString(), UUID.randomUUID())
        val sku3 = Sku(UUID.randomUUID().toString(), UUID.randomUUID())
        val skuToLocationInBox = mapOf(
            expectedSku to locationInBox,
            sku2 to "Meal Kit",
            sku3 to "Pack Chilled",
        )
        skuToLocationInBox.forEach {
            insertSkuCalculations(it.key, dailyCalculationsCount, dcCodeVE, dcWeek, it.value)
        }

        val skuFetchedUsingLocationInBox =
            getDailyCalculationsOkResponse(
                CalculationRequest(
                    dcCodesVE,
                    listOf(dcWeek),
                    Page(page = 1, 5),
                    listOf(),
                    consumptionDaysAhead = 0,
                    locationInBox = listOf(locationInBox),
                ),
            ).calculations

        assertEquals(1, skuFetchedUsingLocationInBox.size)
        val actualSku = skuFetchedUsingLocationInBox[0]
        assertEquals(expectedSku.skuId, actualSku.skuId)
        assertEquals(expectedSku.skuCode, actualSku.skuCode)
        assertEquals("sku name ${expectedSku.skuId}", actualSku.skuName)
    }

    @Test fun `get calculations using multiple location-in-box selected in filters`() {
        val assemblyCoolPouch = "Assembly-Cool Pouch"
        val mealKit = "Meal Kit"
        val dailyCalculationsCount = 1
        val sku1 = Sku(UUID.randomUUID().toString(), UUID.randomUUID())
        val sku2 = Sku(UUID.randomUUID().toString(), UUID.randomUUID())
        val sku3 = Sku(UUID.randomUUID().toString(), UUID.randomUUID())
        val skuToLocationInBox = mapOf(
            sku1 to assemblyCoolPouch,
            sku2 to mealKit,
            sku3 to "Pack Chilled",
        )
        skuToLocationInBox.forEach {
            insertSkuCalculations(it.key, dailyCalculationsCount, dcCodeVE, dcWeek, it.value)
        }

        val skuFetchedUsingLocationInBox =
            getDailyCalculationsOkResponse(
                CalculationRequest(
                    dcCodesVE,
                    listOf(dcWeek),
                    Page(page = 1, 5),
                    listOf(),
                    consumptionDaysAhead = 0,
                    locationInBox = listOf(assemblyCoolPouch, mealKit),
                ),
            ).calculations

        assertEquals(2, skuFetchedUsingLocationInBox.size)
        val results = skuFetchedUsingLocationInBox.map { it.skuName }.toList()
        assertTrue(
            skuToLocationInBox.map { it.key }
                .filter { it.skuCode != sku3.skuCode }
                .map { sku -> "sku name ${sku.skuId}" }
                .containsAll(results.orEmpty()),
        )
    }

    @ParameterizedTest
    @CsvSource(
        "CLEARDOWN,true",
        "CLEARDOWN,false",
        "LIVE,true",
        "LIVE,false",
    )
    fun `get calculations for sku with closing stock less than the given number`(
        inventoryRefreshType: InventoryRefreshType,
        preProd: Boolean
    ) {
        val expectedSku = firstCulinarySku()
        val expectedSkuEqualClosingStock = secondCulinarySku()
        val someOtherSku = thirdCulinarySku()
        val closingStockParam = BigDecimal.TEN.negate()

        insertSkuRecords(
            expectedSku.toSkuSpecificationRecord(),
            expectedSkuEqualClosingStock.toSkuSpecificationRecord(),
            someOtherSku.toSkuSpecificationRecord(),
        )

        (0..5).map {
            insertCalculationRecord(
                inventoryRefreshType,
                preProd,
                expectedSku,
                dcWeek,
                date = LocalDate.now(ZoneOffset.UTC).plusDays(it.toLong()),
                // for 1 day it's lower than the amount
                closingStock = if (it == 3) closingStockParam - BigDecimal.TEN else closingStockParam + BigDecimal(20),
            )

            insertCalculationRecord(
                inventoryRefreshType,
                preProd,
                expectedSkuEqualClosingStock,
                dcWeek,
                LocalDate.now(ZoneOffset.UTC).plusDays(it.toLong()),

                // for 1 day it's equal than the amount
                closingStock = if (it == 3) closingStockParam else closingStockParam + BigDecimal(20),
            )

            insertCalculationRecord(
                inventoryRefreshType,
                preProd,
                someOtherSku,
                dcWeek,
                date = LocalDate.now(ZoneOffset.UTC).plusDays(it.toLong()),

                // always greater than the param
                closingStock = closingStockParam + BigDecimal(20),
            )
        }

        val actualCalculations =
            getDailyCalculationsOkResponse(
                CalculationRequest(
                    dcCodesVE,
                    listOf(dcWeek),
                    Page(page = 1, 5),
                    listOf(),
                    consumptionDaysAhead = if (preProd) 1 else 0,
                    inventoryRefreshType = inventoryRefreshType,
                    closingStockLessThanOrEqual = closingStockParam.toLong(),
                ),
            ).calculations

        assertEquals(12, actualCalculations.size)
        val actualSkus = actualCalculations.map { it.skuId }.toSet()
        assertEquals(2, actualSkus.size)
        assertEquals(setOf(expectedSku.id, expectedSkuEqualClosingStock.id), actualSkus.toSet())
    }

    @Test
    fun `get only calculations for skus with net needs when has net needs filter is applied`() {
        val expectedSku = firstCulinarySku()
        val expectedSkuEqualClosingStock = secondCulinarySku()
        val someOtherSku = thirdCulinarySku()
        val closingStockParam = BigDecimal.TEN.negate()
        val dcWeek2 = "2022-W09"
        insertSkuRecords(
            expectedSku.toSkuSpecificationRecord(),
            expectedSkuEqualClosingStock.toSkuSpecificationRecord(),
            someOtherSku.toSkuSpecificationRecord(),
        )

        (0..2).map {
            insertCalculationRecord(
                CLEARDOWN,
                true,
                expectedSku,
                if (it % 2 == 0) dcWeek else dcWeek2,
                date = LocalDate.now(ZoneOffset.UTC).plusDays(it.toLong()),
                netNeeds = if (it % 2 == 0) BigDecimal(100) else BigDecimal.ZERO,
                closingStock = closingStockParam - BigDecimal.TEN,
            )
        }

        val actualCalculations =
            getDailyCalculationsOkResponse(
                CalculationRequest(
                    dcCodesVE,
                    listOf(dcWeek, dcWeek2),
                    Page(page = 1, 5),
                    listOf(),
                    consumptionDaysAhead = 1,
                    inventoryRefreshType = CLEARDOWN,
                    additionalFilters = setOf(AdditionalFilter.WITH_NET_NEEDS_ONLY),
                ),
            ).calculations

        assertEquals(2, actualCalculations.size)
    }

    @Test fun `get calculations when location-in-box filter is not selected`() {
        val dailyCalculationsCount = 1
        val locationInBoxList = listOf("Assembly-Cool Pouch", "Meal Kit", "Pack Chilled", "")
        val skuToLocationInBox = locationInBoxList.associateBy {
            Sku(UUID.randomUUID().toString(), UUID.randomUUID())
        }

        skuToLocationInBox.forEach {
            insertSkuCalculations(it.key, dailyCalculationsCount, dcCodeVE, dcWeek, it.value)
        }

        val skuFetchedUsingLocationInBox =
            getDailyCalculationsOkResponse(
                CalculationRequest(
                    dcCodesVE,
                    listOf(dcWeek),
                    Page(page = 1, 5),
                    listOf(),
                    consumptionDaysAhead = 0,
                ),
            ).calculations

        assertEquals(4, skuFetchedUsingLocationInBox.size)
        val results = skuFetchedUsingLocationInBox.map { it.skuName }.toList()
        assertTrue(
            skuToLocationInBox.map { it.key }
                .map { sku -> "sku name ${sku.skuId}" }
                .containsAll(results.orEmpty()),
        )
    }

    @Test fun `should return a single sku per page if calculations for a single sku was requested`() {
        // given
        val today = LocalDate.now()
        val startDayOfWeek = FRIDAY
        val currentWeek = DcWeek(today, startDayOfWeek)
        val weekStartDate = today.with(TemporalAdjusters.previousOrSame(startDayOfWeek))

        val record1 = RandomFixture().calculationRecord {
            dcCode = dcCode
            productionWeek = currentWeek.value
        }

        val record2 = RandomFixture().calculationRecord {
            dcCode = dcCode
            productionWeek = currentWeek.value
        }
        insertSkuRecords(toSkuSpecification(record1), toSkuSpecification(record2))

        // when
        // insert 3 days calculations
        listOf(0, 2, 3).forEach { day ->
            listOf(record1, record2).forEach {
                it.date = weekStartDate.plusDays(day.toLong())
            }
            dsl.batchInsert(record1, record2).execute()
        }

        // fetch whole week of calculations
        val page1SingleSkuRequest = CalculationRequest(
            dcCodes = dcCodesVE,
            weeks = listOf(currentWeek.value),
            pageRequest = Page(page = 1, 1),
            skuCategories = listOf(),
            consumptionDaysAhead = 0,
        )
        val responsePage1 = getDailyCalculationsOkResponse(page1SingleSkuRequest).calculations
        val page2SingleSquRequest = page1SingleSkuRequest.copy(pageRequest = Page(page = 2, 1))
        val responsePage2 = getDailyCalculationsOkResponse(page2SingleSquRequest).calculations

        // then
        listOf(responsePage1, responsePage2).forEach {
            assertEquals(1, it.map { v -> v.skuId }.toSet().size)
            assertEquals(3, it.size)
        }
        assertNotEquals(responsePage1.first().skuId, responsePage2.first().skuId)
    }

    @Test
    fun `should return a single sku per page if calculations for a single sku was requested when we have repeated names for skus`() {
        // given
        val today = LocalDate.now()
        val startDayOfWeek = FRIDAY
        val currentWeek = DcWeek(today, startDayOfWeek)

        val record1 = RandomFixture().calculationRecord {
            dcCode = dcCode
            productionWeek = currentWeek.value
        }
        val record2 = RandomFixture().calculationRecord {
            dcCode = dcCode
            productionWeek = currentWeek.value
        }
        val skuSpecificationRecord1 = toSkuSpecification(record1)
        val skuSpecificationRecord2 = toSkuSpecification(record2).apply { this.name = skuSpecificationRecord1.name }

        insertSkuRecords(toSkuSpecification(record1), skuSpecificationRecord2)
        dsl.batchInsert(record1, record2).execute()

        // when
        // fetch calculations
        val page1SingleSkuRequest = CalculationRequest(
            dcCodes = dcCodesVE,
            weeks = listOf(currentWeek.value),
            pageRequest = Page(page = 1, 1),
            skuCategories = listOf(),
            consumptionDaysAhead = 0,
            sortBy = SKU_NAME,
        )
        val responsePage1 = getDailyCalculationsOkResponse(page1SingleSkuRequest)
        val page2SingleSquRequest = page1SingleSkuRequest.copy(pageRequest = Page(page = 2, 1))
        val responsePage2 = getDailyCalculationsOkResponse(page2SingleSquRequest)

        // then
        listOf(responsePage1, responsePage2).forEach {
            assertEquals(1, it.calculations.size)
        }
        assertEquals(2, responsePage1.totalSkusCount)
        assertEquals(2, responsePage1.totalPages)
    }

    @Test
    fun `should use ISO date formatting in the API response`() {
        val dateStr = "2022-01-01"
        val record = RandomFixture()
            .calculationRecord { date = LocalDate.parse(dateStr) }
            .also {
                insertSkuRecords(toSkuSpecification(it))
                dsl.executeInsert(it)
            }

        runBlocking {
            val request = CalculationRequest(
                dcCodes = listOf(record.dcCode),
                weeks = listOf(record.productionWeek),
                pageRequest = Page(page = 1, 1),
                skuCategories = listOf(),
                consumptionDaysAhead = 0,
            )
            val response = getDailyCalculations(request)
            assertEquals(HttpStatusCode.OK, response.status)
            assertTrue(response.bodyAsText().contains("\"date\":\"${dateStr}\""))
        }
    }

    @ParameterizedTest
    @EnumSource(SortBy::class)
    fun `calculations are sorted by default by sku at risk and then selected sortBy`(sortBy: SortBy) {
        // given
        val givenWeek = DcWeek("2022-W01")
        val day = givenWeek.getLastDateInDcWeek(FRIDAY, ZoneId.of("UTC"))
        val randomSkuFixture = { sortByParam: SortBy, value: String ->
            when (sortByParam) {
                SKU_CODE -> CulinarySkuFixture(UUID.randomUUID(), value, UUID.randomUUID().toString())
                SKU_NAME -> CulinarySkuFixture(UUID.randomUUID(), UUID.randomUUID().toString(), value)
            }
        }
        val skusWithShortage = ('C'..'D').map { randomSkuFixture(sortBy, "$it") }
            .mapIndexed { index, sku ->
                sku to
                    emptyCalculationRecord(givenWeek.value, day, sku).apply {
                        this.dailyNeeds = index.toBigDecimal() + BigDecimal.ONE
                        this.expectedInbound = index.toBigDecimal() + BigDecimal.ONE
                        this.actualInbound = BigDecimal.ZERO
                        this.date = day.plusDays(index.toLong())
                    }
            }.shuffled().toMap()

        val skusWithoutShortage = ('A'..'B').map { randomSkuFixture(sortBy, "$it") }
            .mapIndexed { index, sku ->
                sku to emptyCalculationRecord(givenWeek.value, day, sku).apply {
                    this.dailyNeeds = BigDecimal.ZERO
                    this.expectedInbound = BigDecimal.ZERO
                    this.actualInbound = BigDecimal.ZERO
                    this.date = day.plusDays(index.toLong())
                }
            }.shuffled().toMap()

        insertSkuRecords(
            skusWithShortage.keys.map { it.toSkuSpecificationRecord() } +
                skusWithoutShortage.keys.map { it.toSkuSpecificationRecord() },
        )

        dsl.batchInsert(
            skusWithShortage.values +
                skusWithoutShortage.values,
        ).execute()

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek.value),
            pageRequest = Page(1, 10),
            consumptionDaysAhead = 0,
            sortBy = sortBy,
        )

        // when
        val response = getDailyCalculationsOkResponse(req)

        // then
        assertEquals(
            listOf("C", "D", "A", "B"),
            response.calculations.map
                {
                    when (sortBy) {
                        SKU_NAME -> it.skuName
                        SKU_CODE -> it.skuCode
                    }
                },
        )
    }

    @Test
    fun `should not mark a sku with future shortage as at risk`() {
        val givenWeek = "2022-W01"
        val yesterday = LocalDate.now().minusDays(1)
        val today = LocalDate.now()
        val tomorrow = LocalDate.now().plusDays(1)

        // Inbound shortage yesterday
        val firstCulinarySku = firstCulinarySku()
        val calculationWithInboundShortageYesterday = emptyCalculationRecord(givenWeek, yesterday, firstCulinarySku)
            .apply {
                this.actualInbound = BigDecimal(50)
                this.expectedInbound = BigDecimal(100)
            }

        // Inbound shortage Today
        val secondCulinarySku = secondCulinarySku()
        val calculationWithInboundShortageToday = emptyCalculationRecord(givenWeek, today, secondCulinarySku)
            .apply {
                this.actualInbound = BigDecimal(7789)
                this.expectedInbound = BigDecimal(9000)
            }

        // Inbound shortage Future
        val thirdCulinarySku = thirdCulinarySku()
        val calculationWithInboundShortageTomorrow = emptyCalculationRecord(givenWeek, tomorrow, thirdCulinarySku)
            .apply {
                this.actualInbound = BigDecimal(300)
                this.expectedInbound = BigDecimal(600)
            }

        dsl.batchInsert(
            calculationWithInboundShortageYesterday,
            calculationWithInboundShortageToday,
            calculationWithInboundShortageTomorrow,

        ).execute()
        insertSkuRecords(
            firstCulinarySku.toSkuSpecificationRecord(),
            secondCulinarySku.toSkuSpecificationRecord(),
            thirdCulinarySku.toSkuSpecificationRecord(),
        )

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 10),
            consumptionDaysAhead = 0,
        )

        // when & then
        val response = getDailyCalculationsOkResponse(req)

        assertEquals(3, response.calculations.size)
        assertEquals(1, response.totalSkusAtRiskCount)
        assertEquals(3, response.totalSkusCount)
    }

    @Test
    fun `should return total sku at risk count in daily calculations response matching given request`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.parse("2022-01-01")
        val firstCulinarySku = firstCulinarySku()
        val calculation = emptyCalculationRecord(givenWeek, day1, firstCulinarySku)

        val secondCulinarySku = secondCulinarySku()
        val calculationWithShortage = emptyCalculationRecord(
            givenWeek,
            day1,
            secondCulinarySku,
        ).apply { this.dailyNeeds = BigDecimal.ONE }

        val thirdCulinarySku = thirdCulinarySku()
        val calculationWithInboundShortage = emptyCalculationRecord(givenWeek, day1, thirdCulinarySku)
            .apply {
                this.actualInbound = BigDecimal(50)
                this.expectedInbound = BigDecimal(100)
            }
        val calculationNotAtRiskForOtherDayForSameSku =
            emptyCalculationRecord(givenWeek, day1.plusDays(1), thirdCulinarySku)

        val fourthCulinarySku = CulinarySkuFixture(UUID.randomUUID(), code = "PRO-00-00000-4", name = "Fourth CSKU")
        val calculationWithShortageWeekNoRequested = emptyCalculationRecord("2022-W50", day1, fourthCulinarySku)
            .apply {
                this.dailyNeeds = BigDecimal.ONE
            }

        dsl.batchInsert(
            listOf(
                calculation,
                calculationWithShortage,
                calculationWithInboundShortage,
                calculationNotAtRiskForOtherDayForSameSku,
                calculationWithShortageWeekNoRequested,
            ),
        ).execute()
        insertSkuRecords(
            firstCulinarySku.toSkuSpecificationRecord(),
            secondCulinarySku.toSkuSpecificationRecord(),
            thirdCulinarySku.toSkuSpecificationRecord(),
            fourthCulinarySku.toSkuSpecificationRecord(),
        )

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 10),
            consumptionDaysAhead = 0,
        )

        // when & then
        val response = getDailyCalculationsOkResponse(req)

        assertEquals(4, response.calculations.size)
        assertEquals(2, response.totalSkusAtRiskCount)
        assertEquals(3, response.totalSkusCount)
        assertTrue { response.calculations.any { it.skuId == secondCulinarySku.id } }
        assertTrue { response.calculations.any { it.skuId == thirdCulinarySku.id } }
    }

    @ParameterizedTest
    @CsvSource(
        "CLEARDOWN,true",
        "CLEARDOWN,false",
        "LIVE,true",
        "LIVE,false",
    )
    fun `should calculations matching the po_due_min and po_due_max limits`(
        inventoryRefreshType: InventoryRefreshType,
        preProd: Boolean
    ) {
        // given
        val givenWeekMatching = "2022-W01"
        val givenWeekNotMatching = "2022-W02"
        val givenWeekNotSelected = "2022-W03"

        val day = LocalDate.parse("2022-01-01")
        val firstCulinarySku = firstCulinarySku()
        val secondCulinarySku = secondCulinarySku()
        insertSkuRecords(
            firstCulinarySku.toSkuSpecificationRecord(),
            secondCulinarySku.toSkuSpecificationRecord(),
        )

        (0..6).map {
            // This week all matching 1st sku
            insertCalculationRecord(
                inventoryRefreshType,
                preProd,
                firstCulinarySku,
                givenWeekMatching,
                day.plusDays(it.toLong()),
                maxPurchaseOrderDueIn = it,
            )

            // This week no match for 2nd sku
            insertCalculationRecord(
                inventoryRefreshType,
                preProd,
                secondCulinarySku,
                givenWeekNotMatching,
                day.plusDays(it.toLong() + 7),
                maxPurchaseOrderDueIn = null,
            )

            // This week no match 1st sku
            insertCalculationRecord(
                inventoryRefreshType,
                preProd,
                firstCulinarySku,
                givenWeekNotMatching,
                day.plusDays(it.toLong() + 7),
                maxPurchaseOrderDueIn = null,
            )

            // This week matches for po_due_in but not selected in the request
            insertCalculationRecord(
                inventoryRefreshType,
                preProd,
                firstCulinarySku,
                givenWeekNotSelected,
                day.plusDays(it.toLong() + 14),
                maxPurchaseOrderDueIn = it,
            )
        }

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeekMatching, givenWeekNotMatching),
            pageRequest = Page(1, 10),
            consumptionDaysAhead = if (preProd) 1 else 0,
            inventoryRefreshType = inventoryRefreshType,
            poDueInMin = 3,
            poDueInMax = 7,
        )

        // when & then
        val response = getDailyCalculationsOkResponse(req)

        // returns 1sku for both the weeks, 2nd is not matched
        assertEquals(setOf(firstCulinarySku.id), response.calculations.map { it.skuId }.toSet())
        assertEquals(setOf(givenWeekMatching, givenWeekNotMatching), response.calculations.map { it.week }.toSet())
    }

    @Test
    fun `should return safety stock quantities in daily calculations`() {
        // given
        val givenWeek = "2022-W01"
        val today = LocalDate.now()
        val firstCulinarySku = firstCulinarySku()
        val calculation = emptyCalculationRecord(givenWeek, today, firstCulinarySku).apply {
            safetystock = BigDecimal(11)
            safetystockNeeds = BigDecimal(12)
        }
        dsl.batchInsert(calculation).execute()
        insertSkuRecords(firstCulinarySku.toSkuSpecificationRecord())

        // when
        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 1),
            consumptionDaysAhead = 0,
        )
        val response = getDailyCalculationsOkResponse(req)

        // then
        assertEquals(1, response.calculations.size)
        assertEquals(calculation.safetystock, response.calculations.first().safetyStock)
        assertEquals(calculation.safetystockNeeds, response.calculations.first().safetyStockNeeds)
    }

    @Test
    fun `should return storage and staging stock in daily calculations for live inventory refresh type`() {
        // given
        val givenWeek = "2022-W01"
        val today = LocalDate.now()
        val stagingStockQty = BigDecimal(10)
        val storageStockQty = BigDecimal(20)
        val firstCulinarySku = firstCulinarySku()
        val calculation =
            emptyLiveInventoryCalculationRecord(givenWeek, today, firstCulinarySku, storageStockQty, stagingStockQty)

        dsl.batchInsert(calculation).execute()
        insertSkuRecords(firstCulinarySku.toSkuSpecificationRecord())

        // when
        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 1),
            consumptionDaysAhead = 0,
            inventoryRefreshType = LIVE,
        )
        val response = getDailyCalculationsOkResponse(req)

        // then
        assertEquals(1, response.calculations.size)
        assertEquals(stagingStockQty, response.calculations.first().stagingStock)
        assertEquals(storageStockQty, response.calculations.first().storageStock)
    }

    @Test
    fun `should return calculations for selected suppliers`() {
        val sku = Sku("PRO-1111", UUID.randomUUID())
        insertSkuCalculations(sku, 1, FIXTURE_DC, dcWeek)
        val supplierId = UUID.randomUUID()
        val po = PurchaseOrder(
            "po",
            "po-ref",
            UUID.randomUUID(),
            FIXTURE_DC,
            TimeRange(ZonedDateTime.now(), ZonedDateTime.now().plusMinutes(1)),
            Supplier(supplierId, ""),
            listOf(
                PurchaseOrderSku(
                    sku.skuId,
                    SkuQuantity.fromLong(99),
                    deliveries = listOf(
                        DeliveryInfo("ID1", ZonedDateTime.now(), DeliveryInfoStatus.CLOSED, SkuQuantity.fromLong(99)),
                    ),
                ),
            ),
            poStatus = APPROVED,
        )
        insertPurchaseOrderAndSupplier(po)
        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(dcWeek),
            pageRequest = Page(1, 1),
            consumptionDaysAhead = 0,
            supplierIds = listOf(supplierId),
        )

        // when
        val response = getDailyCalculationsOkResponse(req)

        // then
        assertEquals(1, response.calculations.size)

        // when
        val responseSupplierIdUnknown = getDailyCalculationsOkResponse(req.copy(supplierIds = listOf(UUID(0, 0))))
        // then
        assertEquals(0, responseSupplierIdUnknown.calculations.size)
    }

    @Test
    fun `should return actual and expected POs from calculation`() {
        val givenWeek = "2022-W01"
        val day = LocalDate.parse("2022-01-01")
        val sku = firstCulinarySku()
        val expectedPo = setOf("11", "22")
        val actualPo = setOf("33", "44", "11")
        val calculation = emptyCalculationRecord(givenWeek, day, sku).apply {
            this.dcCode = dcCodeVE
            this.expectedInboundPo = expectedPo.joinToString(separator = ",")
            this.actualInboundPo = actualPo.joinToString(separator = ",")
        }

        dsl.batchInsert(calculation).execute()
        insertSkuRecords(sku.toSkuSpecificationRecord())

        val req = CalculationRequest(
            dcCodes = dcCodesVE,
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 1),
            consumptionDaysAhead = 0,
        )

        val calculations = getDailyCalculationsOkResponse(req).calculations
        assertEquals(1, calculations.size)
        assertEquals(expectedPo + actualPo, calculations.first().pos?.toSet())
    }

    @Test fun `should return bad request poDueInLessThanOrEqual is out of range`() {
        val params = Parameters.build {
            set("dcCode", FIXTURE_DC)
            set("weeks", "2022-W01")
            append("poDueInLessThanOrEqual", "-8")
            set("sortBy", "skuCode")
        }.formUrlEncode()
        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials("", "", "", "", jwksURI), false)
                    calculationRoutingModule(
                        CalculationsService(
                            SimpleMeterRegistry(),
                            dcConfigService,
                            usableInventoryEvaluator,
                            calculationsPendingStockUpdateService,
                            prodCalculationsRepo,
                            preProdCalculationsRepo,
                            statsigFeatureFlagClient,
                        ),
                        calculationResponseMapper,
                        Duration.parse("PT1S"),
                    )()
                }
                client.get("/calculation/dailyView?$params") {
                    this.addAuthHeader(UUID.randomUUID().toString(), UUID.randomUUID().toString(), jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.BadRequest, status)
                    assertEquals(
                        """{"reason":"poDueInLessThanOrEqual value should be greater than or equal to -7"}""",
                        bodyAsText(),
                    )
                }
            }
        }
    }

    @Test fun `should return bad request poDueInGreaterThanOrEqual is out of range`() {
        val params = Parameters.build {
            set("dcCode", FIXTURE_DC)
            set("weeks", "2022-W01")
            append("poDueInGreaterThanOrEqual", "11")
            set("sortBy", "skuCode")
        }.formUrlEncode()
        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials("", "", "", "", jwksURI), false)
                    calculationRoutingModule(
                        CalculationsService(
                            SimpleMeterRegistry(),
                            dcConfigService,
                            usableInventoryEvaluator,
                            calculationsPendingStockUpdateService,
                            prodCalculationsRepo,
                            preProdCalculationsRepo,
                            statsigFeatureFlagClient,
                        ),
                        calculationResponseMapper,
                        Duration.parse("PT1S"),
                    )()
                }
                client.get("/calculation/dailyView?$params") {
                    this.addAuthHeader(UUID.randomUUID().toString(), UUID.randomUUID().toString(), jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.BadRequest, status)
                    assertEquals(
                        """{"reason":"poDueInGreaterThanOrEqual value should be less than or equal to 10"}""",
                        bodyAsText(),
                    )
                }
            }
        }
    }

    @SuppressWarnings("LongParameterList")
    private fun insertCalculationRecord(
        inventoryRefreshType: InventoryRefreshType,
        preProd: Boolean,
        sku: CulinarySkuFixture,
        week: String,
        date: LocalDate,
        closingStock: BigDecimal = BigDecimal.ZERO,
        maxPurchaseOrderDueIn: Int? = null,
        netNeeds: BigDecimal = BigDecimal.ZERO,
    ) {
        val calculation = when (inventoryRefreshType) {
            CLEARDOWN ->
                if (preProd) {
                    emptyPreProdCalculationRecord(week, date, sku).apply {
                        this.closingStock = closingStock
                        this.maxPurchaseOrderDueIn = maxPurchaseOrderDueIn
                        this.netNeeds = netNeeds
                    }
                } else {
                    emptyCalculationRecord(week, date, sku).apply {
                        this.closingStock = closingStock
                        this.maxPurchaseOrderDueIn = maxPurchaseOrderDueIn
                        this.netNeeds = netNeeds
                    }
                }

            LIVE ->
                if (preProd) {
                    emptyLivePreProdInventoryCalculationRecord(week, date, sku).apply {
                        this.closingStock = closingStock
                        this.maxPurchaseOrderDueIn = maxPurchaseOrderDueIn
                        this.netNeeds = netNeeds
                    }
                } else {
                    emptyLiveInventoryCalculationRecord(week, date, sku).apply {
                        this.closingStock = closingStock
                        this.maxPurchaseOrderDueIn = maxPurchaseOrderDueIn
                        this.netNeeds = netNeeds
                    }
                }
        }
        dsl.batchInsert(calculation).execute()
    }
}
