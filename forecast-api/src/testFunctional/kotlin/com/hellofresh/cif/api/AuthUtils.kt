package com.hellofresh.cif.api

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTCreator
import com.auth0.jwt.algorithms.Algorithm
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.header
import io.ktor.http.HttpHeaders
import io.ktor.http.auth.AuthScheme
import io.ktor.http.auth.HttpAuthHeader
import java.util.UUID

object AuthUtils {

    private fun buildJwtToken(jwtSecret: String, jwtBuilderConf: JWTCreator.Builder.() -> Unit): String =
        JWT.create().withClaim("sub", UUID.randomUUID().toString())
            .also(jwtBuilderConf)
            .sign(Algorithm.HMAC256(jwtSecret))

    fun HttpRequestBuilder.addAuthHeader(authorEmail: String?, authorName: String?, jwtSecret: String) {
        this.header(
            HttpHeaders.Authorization,
            HttpAuthHeader.Single(
                AuthScheme.Bearer,
                buildJwtToken(jwtSecret) {
                    authorEmail?.also { this.withClaim("email", it) }
                    authorName?.also { this.withClaim("metadata", mapOf("name" to authorName)) }
                },
            ),
        )
    }
}
