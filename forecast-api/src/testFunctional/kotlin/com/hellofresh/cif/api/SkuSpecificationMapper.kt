package com.hellofresh.cif.api

import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.LiveInventoryCalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord

fun toSkuSpecification(calculationRecord: CalculationRecord) =
    SkuSpecificationRecord()
        .apply {
            id = calculationRecord.cskuId
            code = calculationRecord.cskuId.toString()
            name = calculationRecord.cskuId.toString()
            category = calculationRecord.cskuId.toString().take(3)
            coolingType = ""
            packaging = ""
            acceptableCodeLife = 0
            market = ""
        }

fun toSkuSpecification(liveInventoryCalculationRecord: LiveInventoryCalculationRecord) =
    SkuSpecificationRecord()
        .apply {
            id = liveInventoryCalculationRecord.cskuId
            code = liveInventoryCalculationRecord.cskuId.toString()
            name = liveInventoryCalculationRecord.cskuId.toString()
            category = liveInventoryCalculationRecord.cskuId.toString().take(3)
            coolingType = ""
            packaging = ""
            acceptableCodeLife = 0
            market = ""
        }
