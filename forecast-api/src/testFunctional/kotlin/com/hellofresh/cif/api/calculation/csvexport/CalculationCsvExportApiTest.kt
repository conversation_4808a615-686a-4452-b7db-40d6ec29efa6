package com.hellofresh.cif.api.calculation.csvexport

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.calculation.AdditionalFilter
import com.hellofresh.cif.api.calculation.AllPages
import com.hellofresh.cif.api.calculation.CalculationFunctionalTest
import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.CalculationsService
import com.hellofresh.cif.api.calculation.InventoryRefreshType
import com.hellofresh.cif.api.calculation.Page
import com.hellofresh.cif.api.calculation.SortBy
import com.hellofresh.cif.api.calculation.csvexport.CalculationCsvExportService.Companion.toCsvExportParameters
import com.hellofresh.cif.api.calculation.generated.model.FileExportResponse
import com.hellofresh.cif.api.fileexport.FileExportService
import com.hellofresh.cif.api.fileexport.fileExportModule
import com.hellofresh.cif.api.fileexport.mapToFileExportStatus
import com.hellofresh.cif.api.fileexport.repository.FileExportRequest
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.api.schema.enums.ExportStatus
import com.hellofresh.cif.s3.FileObject
import com.hellofresh.cif.s3.S3FileService
import io.ktor.client.request.accept
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.Parameters
import io.ktor.http.ParametersBuilder
import io.ktor.http.formUrlEncode
import io.ktor.http.isSuccess
import io.ktor.server.testing.testApplication
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import java.net.URI
import java.time.Instant
import java.util.UUID
import kotlin.reflect.KClass
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.testcontainers.shaded.org.awaitility.Awaitility

class CalculationCsvExportApiTest : CalculationFunctionalTest() {

    private val calculationCsvGeneratorMock = mockk<CalculationCsvGenerator>()
    private val s3FileService = mockk<S3FileService>()

    private val calculationsService = CalculationsService(
        SimpleMeterRegistry(),
        dcConfigService,
        usableInventoryEvaluator,
        calculationsPendingStockUpdateService,
        prodCalculationsRepo,
        preProdCalculationsRepo,
        statsigFeatureFlagClient,
    )

    @ParameterizedTest
    @EnumSource(CalculationViewType::class)
    fun `calculation csv export is requested`(calculationViewType: CalculationViewType) {
        val calculationRequest = CalculationRequest(
            dcCodes = listOf("dc1", "dc2"),
            weeks = listOf("2024-W34", "2024-W35"),
            pageRequest = Page(page = 1, skuCount = 50),
            skuCodes = listOf("skuCodes"),
            skuCategories = listOf("skuCategories"),
            additionalFilters = setOf(AdditionalFilter.WITH_INBOUND_SHORTAGE, AdditionalFilter.WITH_CONSUMPTION_ONLY),
            consumptionDaysAhead = 1,
            inventoryRefreshType = InventoryRefreshType.CLEARDOWN,
            sortBy = SortBy.SKU_NAME,
            locationInBox = listOf("locationInBox"),
            supplierIds = listOf(UUID.randomUUID(), UUID.randomUUID()),
            poDueInMax = 2,
            poDueInMin = 4,
            closingStockLessThanOrEqual = 21342,
        )

        val fileUrl = "http://fileUrl"

        coEvery {
            calculationCsvGeneratorMock.generateCsvFile(
                calculationViewType,
                calculationRequest.copy(pageRequest = AllPages),
                any<FileExportRequest>(),
            )
        } coAnswers {
            fileExportRequestRepository.updateFileExportRequest(
                thirdArg<FileExportRequest>().copy(status = ExportStatus.Completed, fileUrl = fileUrl),
            )!!
        }

        val fileExportResponse =
            get(
                "/export/calculation/" +
                    when (calculationViewType) {
                        CalculationViewType.DAILY -> "dailyView"
                        CalculationViewType.WEEKLY -> "weeklyView"
                    },
                FileExportResponse::class,
                calculationCsvGeneratorMock,
            ) { addCalculationParams(calculationRequest) }!!

        assertEquals(FileExportResponse.Status.PENDING, fileExportResponse.status)
        assertNotNull(fileExportResponse.parameters)

        Awaitility.await()
            .atMost(10.seconds.toJavaDuration())
            .pollInterval(500.milliseconds.toJavaDuration())
            .until {
                val updatedFileExportResponse =
                    get(
                        "/export/${fileExportResponse.requestId}",
                        FileExportResponse::class,
                        calculationCsvGeneratorMock,
                    )
                updatedFileExportResponse != null &&
                    ExportStatus.Completed.mapToFileExportStatus() == updatedFileExportResponse.status &&
                    fileUrl == updatedFileExportResponse.fileUrl
            }

        val fileRecord = dsl.fetchSingle(Tables.FILE_EXPORT_REQUEST)

        assertEquals(
            calculationRequest.toCsvExportParameters(),
            objectMapper.readValue<CsvExportParameters>(fileRecord.parameters.data()),
        )
    }

    @Test
    fun `calculation csv export is generated`() {
        val calculationRequest = CalculationRequest(
            dcCodes = listOf("dc1"),
            weeks = listOf("2024-W34"),
            pageRequest = Page(page = 1, skuCount = 1),
            consumptionDaysAhead = 0,
            inventoryRefreshType = InventoryRefreshType.CLEARDOWN,
        )

        val sku1 = Sku("sku1", UUID.randomUUID())
        insertSkuCalculations(
            sku = sku1,
            calcCount = 7,
            dcCode = calculationRequest.dcCodes.first(),
            week = calculationRequest.weeks.first(),
        )
        val sku2 = Sku("sku2", UUID.randomUUID())
        insertSkuCalculations(
            sku = sku2,
            calcCount = 7,
            dcCode = calculationRequest.dcCodes.first(),
            week = calculationRequest.weeks.first(),
        )

        val fileUrl = URI("http://fileUrl").toURL()

        val expectedContent =
            runBlocking {
                calculationsCsvConverter.convertDailyToCsv(
                    calculationsService.getDailyCalculations(
                        calculationRequest.copy(pageRequest = AllPages),
                    ).calculationPage,
                    emptyList(),
                )
            }
        val keySlot = slot<String>()
        every {
            s3FileService.putObject(
                any(), capture(keySlot),
                expectedContent,
                any(),
            )
        } answers {
            FileObject(secondArg(), Instant.now())
        }

        every {
            s3FileService.getPresignedGetUrl(
                any(),
                match {
                    it == keySlot.captured
                },
                match {
                    it.isPositive()
                },
            )
        } returns fileUrl

        val fileExportResponse =
            get(
                "/export/calculation/dailyView",
                FileExportResponse::class,
            ) { addCalculationParams(calculationRequest) }!!

        assertEquals(FileExportResponse.Status.PENDING, fileExportResponse.status)
        assertNotNull(fileExportResponse.parameters)

        Awaitility.await()
            .atMost(10.seconds.toJavaDuration())
            .pollInterval(500.milliseconds.toJavaDuration())
            .until {
                val updatedFileExportResponse =
                    get(
                        "/export/${fileExportResponse.requestId}",
                        FileExportResponse::class,
                        calculationCsvGeneratorMock,
                    )
                updatedFileExportResponse != null &&
                    ExportStatus.Completed.mapToFileExportStatus() == updatedFileExportResponse.status &&
                    fileUrl.toString() == updatedFileExportResponse.fileUrl
            }

        val fileRecord = dsl.fetchSingle(Tables.FILE_EXPORT_REQUEST)

        assertEquals(
            calculationRequest.toCsvExportParameters(),
            objectMapper.readValue<CsvExportParameters>(fileRecord.parameters.data()),
        )
    }

    private fun <T : Any> get(
        path: String,
        kClass: KClass<T>,
        calculationCsvGenerator: CalculationCsvGenerator? = null,
        builder: ParametersBuilder.() -> Unit = {}
    ): T? {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials("testSecret", "test", "", "", "https://test.com"), false)
                fileExportModule(FileExportService(fileExportRequestRepository), 1.minutes)()
                calculationCsvExportModule(
                    CalculationCsvExportService(
                        FileExportService(fileExportRequestRepository),
                        calculationCsvGenerator ?: CalculationCsvGeneratorImpl(
                            FileExportService(fileExportRequestRepository),
                            s3FileService,
                            calculationsService,
                            PurchaseOrderCSVService(purchaseOrderRepository, dcConfigService),
                            calculationsCsvConverter,
                            SimpleMeterRegistry(),
                            ""
                        ),
                        1,
                    ),
                    1.minutes,
                )()
            }
            val params = Parameters.build(builder).formUrlEncode()

            response = client.get("$path?$params") {
                accept(ContentType.Application.Json)
            }
        }
        return if (response.status.isSuccess()) {
            val bodyAsText = runBlocking { response.bodyAsText() }
            objectMapper.readValue(bodyAsText, kClass.java)
        } else {
            null
        }
    }
}
