package com.hellofresh.cif.api.sku

import com.hellofresh.cif.api.calculation.generated.model.UomEnum
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.models.SkuUOM

object SkuQuantityMapper {

    fun mapSkuUOMToUomEnum(uom: SkuUOM): UomEnum =
        when (uom) {
            SkuUOM.UOM_UNIT -> UomEnum.UNIT
            SkuUOM.UOM_KG -> UomEnum.KG
            SkuUOM.UOM_LBS -> UomEnum.LBS
            SkuUOM.UOM_GAL -> UomEnum.GAL
            SkuUOM.UOM_LITRE -> UomEnum.LITRE
            SkuUOM.UOM_OZ -> UomEnum.OZ
            SkuUOM.UOM_UNRECOGNIZED, SkuUOM.UOM_UNSPECIFIED -> UomEnum.UNRECOGNIZED
        }

    fun mapUomEnumToSkuUOM(uom: UomEnum): SkuUOM =
        when (uom) {
            UomEnum.UNIT -> SkuUOM.UOM_UNIT
            UomEnum.KG -> SkuUOM.UOM_KG
            UomEnum.LBS -> SkuUOM.UOM_LBS
            UomEnum.GAL -> SkuUOM.UOM_GAL
            UomEnum.LITRE -> SkuUOM.UOM_LITRE
            UomEnum.OZ -> SkuUOM.UOM_OZ
            UomEnum.UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
        }

    fun mapToSkuUom(uom: Uom) =
        when (uom) {
            Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
            Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
            Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
            Uom.UOM_KG -> SkuUOM.UOM_KG
            Uom.UOM_LBS -> SkuUOM.UOM_LBS
            Uom.UOM_GAL -> SkuUOM.UOM_GAL
            Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
            Uom.UOM_OZ -> SkuUOM.UOM_OZ
        }

    fun mapToDbUom(skuUOM: SkuUOM) =
        when (skuUOM) {
            SkuUOM.UOM_UNSPECIFIED -> Uom.UOM_UNSPECIFIED
            SkuUOM.UOM_UNRECOGNIZED -> Uom.UOM_UNRECOGNIZED
            SkuUOM.UOM_UNIT -> Uom.UOM_UNIT
            SkuUOM.UOM_KG -> Uom.UOM_KG
            SkuUOM.UOM_LBS -> Uom.UOM_LBS
            SkuUOM.UOM_GAL -> Uom.UOM_GAL
            SkuUOM.UOM_LITRE -> Uom.UOM_LITRE
            SkuUOM.UOM_OZ -> Uom.UOM_OZ
        }
}
