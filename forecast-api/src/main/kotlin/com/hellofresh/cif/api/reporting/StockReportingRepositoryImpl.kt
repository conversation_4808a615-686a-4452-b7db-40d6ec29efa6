package com.hellofresh.cif.api.reporting

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.api.schema.Tables.CALCULATION
import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationViewRecord
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.DateRange
import com.hellofresh.inventory.models.variance.DailyInventoryVarianceData
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.DSLContext

class StockReportingRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
) : StockReportingRepository {
    override suspend fun stockVarianceFromCleardown(dcCode: String, cleardownDate: LocalDate): List<WeekCalculations> =
        metricsDSLContext.withTagName("stock-variance-from-cleardown")
            .transactionResultAsync { contextConfig ->
                val dslContext = contextConfig.dsl()
                val skusWithOpeningStockMismatch =
                    getSkusWithOpeningStockMismatchCleardown(dslContext, dcCode, cleardownDate)

                if (skusWithOpeningStockMismatch.isNotEmpty()) {
                    val fromDate = cleardownDate.minusWeeks(1)
                    val dateRange = DateRange(fromDate, cleardownDate)
                    getCleardownCalculationsSelectedWeek(dslContext, dcCode, skusWithOpeningStockMismatch, dateRange)
                } else {
                    emptyList()
                }
            }.await()

    private fun getCleardownCalculationsSelectedWeek(
        dslContext: DSLContext,
        dcCode: String,
        skusWithOpeningStockMismatch: List<UUID>,
        dateRange: DateRange
    ) = dslContext.selectFrom(
        CALCULATION.join(SKU_SPECIFICATION_VIEW).on(CALCULATION.CSKU_ID.eq(SKU_SPECIFICATION_VIEW.ID)),
    ).where(
        CALCULATION.CSKU_ID.`in`(skusWithOpeningStockMismatch)
            .and(CALCULATION.DC_CODE.eq(dcCode))
            .and(CALCULATION.DATE.between(dateRange.fromDate, dateRange.toDate)),
    ).fetch()
        .groupBy { it.into(CALCULATION.CSKU_ID).value1() }
        .map { kv ->
            val sortedCalculations = kv.value.sortedBy { v -> v.into(CALCULATION.DATE).value1() }
                .map {
                    it.into(SKU_SPECIFICATION_VIEW) to it.into(CALCULATION)
                }
            WeekCalculations(
                toStockCalculation(sortedCalculations.last()),
                sortedCalculations.dropLast(1).map(::toStockCalculation),
            )
        }

    private fun toStockCalculation(pair: Pair<SkuSpecificationViewRecord, CalculationRecord>) =
        StockCalculation(
            sku = pair.first,
            calculationRecord = pair.second,
        )

    private fun getSkusWithOpeningStockMismatchCleardown(
        dslContext: DSLContext,
        dcCode: String,
        currentCleardownDate: LocalDate,
    ): List<UUID> {
        val dayBeforeCurrentCleardown = currentCleardownDate.minusDays(1)
        return dslContext.select()
            .from(CALCULATION)
            .join(SKU_SPECIFICATION_VIEW).on(CALCULATION.CSKU_ID.eq(SKU_SPECIFICATION_VIEW.ID))
            .where(
                CALCULATION.DC_CODE.eq(dcCode)
                    .and(CALCULATION.DATE.between(dayBeforeCurrentCleardown, currentCleardownDate)),
            )
            .fetch()
            .groupBy { it.into(CALCULATION.CSKU_ID) }
            // skip skus with no prior week calculations
            .filter { it.value.size > 1 }
            .filter { kv ->
                val openingStockCurrentCleardown = kv.value.find { v ->
                    v[CALCULATION.DATE] == currentCleardownDate
                }?.get(CALCULATION.OPENING_STOCK)
                val closingStockDayBeforeCleardown = kv.value.find { v ->
                    v[CALCULATION.DATE] == dayBeforeCurrentCleardown
                }?.get(CALCULATION.CLOSING_STOCK)
                openingStockCurrentCleardown != closingStockDayBeforeCleardown
            }.map { it.key.value1()!! }
    }

    override suspend fun liveStockVariance(dcCode: String, dcWeek: String): List<StockLiveVarianceSku> =
        metricsDSLContext.withTagName("inventory-variance-live")
            .select(
                SKU_SPECIFICATION_VIEW.ID,
                SKU_SPECIFICATION_VIEW.CODE,
                SKU_SPECIFICATION_VIEW.NAME,
                SKU_SPECIFICATION_VIEW.CATEGORY,
                Tables.INVENTORY_VARIANCE.CLEARDOWN_VARIANCE,
                Tables.INVENTORY_VARIANCE.LIVE_VARIANCE,
                Tables.INVENTORY_VARIANCE.VALUE,
            )
            .from(Tables.INVENTORY_VARIANCE)
            .join(SKU_SPECIFICATION_VIEW).on(
                Tables.INVENTORY_VARIANCE.SKU_ID.eq(SKU_SPECIFICATION_VIEW.ID),
            )
            .where(
                Tables.INVENTORY_VARIANCE.DC_CODE.eq(dcCode)
                    .and(Tables.INVENTORY_VARIANCE.PRODUCTION_WEEK.eq(dcWeek)),
            ).fetchAsync()
            .thenApply { result ->
                result.map {
                    StockLiveVarianceSku(
                        skuId = it[SKU_SPECIFICATION_VIEW.ID],
                        skuCode = it[SKU_SPECIFICATION_VIEW.CODE],
                        skuName = it[SKU_SPECIFICATION_VIEW.NAME],
                        skuCategories = it[SKU_SPECIFICATION_VIEW.CATEGORY],
                        cleardownVariance = it[Tables.INVENTORY_VARIANCE.CLEARDOWN_VARIANCE].toLong(),
                        liveVariance = it[Tables.INVENTORY_VARIANCE.LIVE_VARIANCE].toLong(),
                        dailyInventoryVarianceData = prepareStockVarianceReportData(
                            it[Tables.INVENTORY_VARIANCE.VALUE].toString()
                        ),
                    )
                }
            }.await()

    private fun prepareStockVarianceReportData(
        value: String
    ) = objectMapper.readValue<List<DailyInventoryVarianceData>>(value)

    companion object {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}

data class WeekCalculations(
    val latestCleardown: StockCalculation,
    val previousWeek: List<StockCalculation>
)

data class StockCalculation(
    val sku: SkuSpecificationViewRecord,
    val calculationRecord: CalculationRecord
)
