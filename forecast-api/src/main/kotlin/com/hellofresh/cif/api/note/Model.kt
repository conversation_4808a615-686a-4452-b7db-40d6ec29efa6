package com.hellofresh.cif.api.note

import com.hellofresh.cif.api.calculation.AdditionalFilter
import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.time.OffsetDateTime
import java.util.UUID

data class Note(
    val id: UUID,
    val skuId: UUID,
    val authorEmail: String,
    val authorName: String?,
    val dcCodes: Set<String>,
    val weeks: Set<DcWeek>,
    val text: String,
    val createdAt: OffsetDateTime,
    val atRisk: List<AtRisk> = listOf(),
    val version: Int,
    val isDeleted: Boolean,
    val isEdited: Boolean
)

data class AtRisk(val dc: String, val week: DcWeek)
data class CreateNoteRequest(
    val skuId: UUID,
    val authorEmail: String,
    val authorName: String?,
    val dcCodes: Set<String>,
    val dcWeeks: Set<DcWeek>,
    val text: String
)

data class UpdateNoteRequest(
    val noteId: UUID,
    val authorEmail: String,
    val authorName: String?,
    val dcWeeks: Set<DcWeek>,
    val text: String
)

data class FindNotesRequest(
    val dcCodes: Set<String>,
    val dcWeeks: Set<DcWeek>,
    val skuIds: Set<UUID> = emptySet(),
    val skuCategories: Set<String> = emptySet(),
    val additionalFilters: Set<AdditionalFilter> = emptySet(),
    val locationInBox: Set<String> = emptySet(),
)
