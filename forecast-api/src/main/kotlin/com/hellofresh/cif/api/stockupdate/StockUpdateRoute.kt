package com.hellofresh.cif.api.stockupdate

import com.hellofresh.cif.api.calculation.CalculationResponseMapper
import com.hellofresh.cif.api.calculation.generated.model.CalculationExperimentV2RequestInner
import com.hellofresh.cif.api.calculation.generated.model.CurrentSkuStockUpdatesResponse
import com.hellofresh.cif.api.calculation.generated.model.StockSimulationRequest
import com.hellofresh.cif.api.calculation.generated.model.StockSimulationResponse
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateRequest
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateV2Item
import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.getAllOrDefault
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getLoggedInUserInfo
import com.hellofresh.cif.api.getOrDefault
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.RequestValidationException
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.api.stockupdate.model.StockUpdateSimulationData
import com.hellofresh.cif.api.stockupdate.simulation.StockUpdateSimulationResponseMapper
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.authenticate
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

private const val DC_CODE_PARAM = "dcCode"
private const val SKU_ID_PARAM = "skuId"
private const val WEEK_PARAM = "week"
private const val WEEKS = "weeks"

@SuppressWarnings("LongMethod")
fun Routing.stockUpdates(
    stockUpdateApiService: StockUpdateApiService,
    statsigFeatureFlagClient: StatsigFeatureFlagClient,
    usableInventoryEvaluator: UsableInventoryEvaluator,
    timeoutInMillis: Long,
) =
    authenticate {
        route("/stock-updates") {
            get("/{$DC_CODE_PARAM}/{$WEEK_PARAM}") {
                withTimeout(timeoutInMillis) {
                    kotlin.runCatching {
                        val dcCode = call.parameters.getOrThrow(DC_CODE_PARAM).trim()
                        val week = call.parameters.getOrThrow(WEEK_PARAM).trim()
                        kotlin.runCatching {
                            withTimeout(timeoutInMillis) {
                                StockUpdateMapper.mapToAllStockUpdatesResponse(
                                    stockUpdateApiService.getAllStockUpdates(dcCode, week),
                                )
                            }
                        }.onSuccess { result ->
                            call.respond(HttpStatusCode.OK, result)
                        }.onFailure {
                            call.handleResponseError(it.cause?.cause ?: it)
                        }
                    }.onFailure {
                        call.handleResponseError(it.cause?.cause ?: it)
                    }
                }
            }
            get("/current/{$DC_CODE_PARAM}/{$SKU_ID_PARAM}") {
                withTimeout(timeoutInMillis) {
                    kotlin.runCatching {
                        val dcCode = call.parameters.getOrThrow(DC_CODE_PARAM).trim()
                        val skuId = UUID.fromString(call.parameters.getOrThrow(SKU_ID_PARAM).trim())
                        val calculatorMode = parseConsumptionDaysAheadCalculatorMode(call.parameters)
                        withTimeout(timeoutInMillis) {
                            stockUpdateApiService.getStockUpdates(dcCode, skuId, calculatorMode)
                        }
                    }.onSuccess { result ->
                        call.respond(HttpStatusCode.OK, result)
                    }.onFailure {
                        call.handleResponseError(it.cause?.cause ?: it)
                    }
                }
            }

            post("/{$DC_CODE_PARAM}/{$SKU_ID_PARAM}") {
                withTimeout(timeoutInMillis) {
                    kotlin.runCatching {
                        stockUpdateApiService.upsertStockUpdate(
                            call.receive<StockUpdateRequest>(),
                            UUID.fromString(call.parameters.getOrThrow(SKU_ID_PARAM)),
                            call.parameters.getOrThrow(DC_CODE_PARAM),
                            call.getLoggedInUserInfo(),
                        )
                    }.onFailure {
                        call.handleResponseError(it.cause?.cause ?: it)
                    }.onSuccess {
                        call.respond(HttpStatusCode.Created)
                    }
                }
            }

            post("/simulation/{$DC_CODE_PARAM}/{$SKU_ID_PARAM}") {
                withTimeout(timeoutInMillis) {
                    kotlin.runCatching {
                        stockUpdateApiService.simulateStockUpdates(
                            call.parameters.getOrThrow(DC_CODE_PARAM),
                            UUID.fromString(call.parameters.getOrThrow(SKU_ID_PARAM)),
                            parseConsumptionDaysAheadCalculatorMode(call.parameters),
                            call.receive<StockSimulationRequest>().stockUpdates.associateBy(
                                { it.date },
                            ) { it.stockUpdateQuantity.toBigDecimal() },
                        )
                    }.onFailure {
                        call.handleResponseError(it.cause?.cause ?: it)
                    }.onSuccess {
                        call.respond(HttpStatusCode.OK, StockSimulationResponse(stockUpdates = it))
                    }
                }
            }

            route("/v2") {
                get("/current/{$DC_CODE_PARAM}/{$SKU_ID_PARAM}") {
                    withTimeout(timeoutInMillis) {
                        kotlin.runCatching {
                            val dcCode = call.parameters.getOrThrow(DC_CODE_PARAM).trim()
                            val skuId = UUID.fromString(call.parameters.getOrThrow(SKU_ID_PARAM).trim())
                            dcCode to skuId
                        }.onSuccess { (dcCode, skuId) ->
                            kotlin.runCatching {
                                withTimeout(timeoutInMillis) {
                                    val stockUpdates = stockUpdateApiService.getCurrentStockUpdate(dcCode, skuId)
                                    CurrentSkuStockUpdatesResponse(
                                        stockUpdates = stockUpdates.map { (date, item) ->
                                            StockUpdateV2Item(
                                                date = date,
                                                quantity = item?.quantity?.getValue(),
                                                version = item?.version,
                                            )
                                        },
                                    )
                                }
                            }.onSuccess { result ->
                                call.respond(HttpStatusCode.OK, result)
                            }.onFailure {
                                call.handleResponseError(it.cause?.cause ?: it)
                            }
                        }.onFailure {
                            call.handleResponseError(it.cause?.cause ?: it)
                        }
                    }
                }

                post("/{$DC_CODE_PARAM}/{$SKU_ID_PARAM}") {
                    withTimeout(timeoutInMillis) {
                        kotlin.runCatching {
                            stockUpdateApiService.upsertStockUpdateAndSimulate(
                                call.receive<StockUpdateRequest>(),
                                UUID.fromString(call.parameters.getOrThrow(SKU_ID_PARAM)),
                                call.parameters.getOrThrow(DC_CODE_PARAM),
                                call.parameters.getAllOrThrow(WEEKS).toSet(),
                                parseConsumptionDaysAheadCalculatorMode(call.parameters),
                                call.getLoggedInUserInfo(),
                                statsigFeatureFlagClient,
                                usableInventoryEvaluator,
                            )
                        }.onFailure {
                            call.handleResponseError(it.cause?.cause ?: it)
                        }.onSuccess { stockUpdateV2Response ->
                            call.respond(
                                HttpStatusCode.Created,
                                stockUpdateV2Response,
                            )
                        }
                    }
                }

                post("/simulation/{$DC_CODE_PARAM}/{$SKU_ID_PARAM}") {
                    handleStockUpdateSimulationRequest(
                        this.call,
                        timeoutInMillis,
                        stockUpdateApiService,
                        statsigFeatureFlagClient,
                        usableInventoryEvaluator,
                    )
                }
            }
        }
    }

fun stockUpdateModule(
    stockUpdateApiService: StockUpdateApiService,
    statsigFeatureFlagClient: StatsigFeatureFlagClient,
    usableInventoryEvaluator: UsableInventoryEvaluator,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        stockUpdates(
            stockUpdateApiService,
            statsigFeatureFlagClient,
            usableInventoryEvaluator,
            timeout.inWholeMilliseconds,
        ).also {
            configureSerialization(it)
        }
    }
}

private suspend fun handleStockUpdateSimulationRequest(
    call: ApplicationCall,
    timeoutInMillis: Long,
    stockUpdateApiService: StockUpdateApiService,
    statsigFeatureFlagClient: StatsigFeatureFlagClient,
    usableInventoryEvaluator: UsableInventoryEvaluator,
) {
    withTimeout(timeoutInMillis) {
        runCatching {
            val stockUpdateSimulationData = toStockUpdateSimulationData(call)
            val stockUpdateSimulationResults = stockUpdateApiService.performStockUpdateSimulation(
                stockUpdateSimulationData,
            )
            StockUpdateSimulationResponseMapper(
                CalculationResponseMapper(statsigFeatureFlagClient = statsigFeatureFlagClient),
            )
                .mapToStockUpdateSimulationResponse(
                    stockUpdateSimulationData.dcCode,
                    stockUpdateSimulationResults,
                    usableInventoryEvaluator,
                )
        }.onSuccess {
            call.respond(HttpStatusCode.OK, it)
        }.onFailure {
            call.handleResponseError(it)
        }
    }
}

private suspend fun toStockUpdateSimulationData(applicationCall: ApplicationCall) = StockUpdateSimulationData(
    dcCode = getDcCode(applicationCall),
    skuId = extractSkuId(applicationCall),
    weeks = applicationCall.parameters.getAllOrDefault(WEEKS).toSet(),
    calculatorMode = parseConsumptionDaysAheadCalculatorMode(applicationCall.parameters),
    stockUpdates = applicationCall.receive<Array<CalculationExperimentV2RequestInner>>()
        .associate { it.date to it.stockUpdate.toBigDecimal() },
)

private fun getDcCode(applicationCall: ApplicationCall): String {
    val dcCodes = applicationCall.parameters.getAllOrThrow("dcCode").toSet()
    require(dcCodes.count() == 1) {
        "Multiple DCs are not supported"
    }
    return dcCodes.first()
}

private fun extractSkuId(applicationCall: ApplicationCall) =
    UUID.fromString(applicationCall.parameters.getOrThrow("skuId").trim())

private fun parseConsumptionDaysAheadCalculatorMode(parameters: Parameters): CalculatorMode {
    val consumptionDaysAhead = parameters.getOrDefault("consumptionDaysAhead", "0").toInt()
    return when {
        consumptionDaysAhead == 0 -> CalculatorMode.PRODUCTION
        consumptionDaysAhead == 1 -> CalculatorMode.PRE_PRODUCTION
        consumptionDaysAhead < 0 -> throw RequestValidationException(
            "Please enter value greater than zero",
            HttpStatusCode.BadRequest,
        )

        else -> throw RequestValidationException(
            "Calculations for $consumptionDaysAhead days ahead is not yet implemented",
            HttpStatusCode.NotImplemented,
        )
    }
}
