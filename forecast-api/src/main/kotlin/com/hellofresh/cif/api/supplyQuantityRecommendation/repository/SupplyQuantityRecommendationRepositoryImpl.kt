package com.hellofresh.cif.api.supplyQuantityRecommendation.repository

import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.api.schema.Tables.SUPPLY_QUANTITY_RECOMMENDATION
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.enums.Uom.UOM_GAL
import com.hellofresh.cif.api.schema.enums.Uom.UOM_KG
import com.hellofresh.cif.api.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.api.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.api.schema.enums.Uom.UOM_OZ
import com.hellofresh.cif.api.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.api.schema.enums.Uom.UOM_UNRECOGNIZED
import com.hellofresh.cif.api.schema.enums.Uom.UOM_UNSPECIFIED
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuUOM
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlinx.coroutines.future.await

class SupplyQuantityRecommendationRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService,
) : SupplyQuantityRecommendationRepository {
    override suspend fun fetchSupplyQuantityRecommendations(
        dcCode: String,
        week: String
    ): List<SupplyQuantityRecommendation> =
        metricsDSLContext.withTagName("fetch-supply-quantity-recommendations")
            .select(
                SUPPLY_QUANTITY_RECOMMENDATION.DC_CODE,
                SUPPLY_QUANTITY_RECOMMENDATION.WEEK,
                SUPPLY_QUANTITY_RECOMMENDATION.SKU_ID,
                SUPPLY_QUANTITY_RECOMMENDATION.UOM,
                SUPPLY_QUANTITY_RECOMMENDATION.SQR,
                SUPPLY_QUANTITY_RECOMMENDATION.INVENTORY_ROLLOVER,
                SUPPLY_QUANTITY_RECOMMENDATION.DEMAND,
                SUPPLY_QUANTITY_RECOMMENDATION.RECOMMENDATION_ENABLED,
                SUPPLY_QUANTITY_RECOMMENDATION.MULTI_WEEK_ENABLED,
                SUPPLY_QUANTITY_RECOMMENDATION.SAFETY_STOCK,
                SUPPLY_QUANTITY_RECOMMENDATION.UPDATED_AT,
                SKU_SPECIFICATION_VIEW.ID,
                SKU_SPECIFICATION_VIEW.CODE,
                SKU_SPECIFICATION_VIEW.NAME,
                SKU_SPECIFICATION_VIEW.CATEGORY,
                SKU_SPECIFICATION_VIEW.UOM,
            )
            .from(SUPPLY_QUANTITY_RECOMMENDATION)
            .join(SKU_SPECIFICATION_VIEW).on(
                SUPPLY_QUANTITY_RECOMMENDATION.SKU_ID.eq(SKU_SPECIFICATION_VIEW.ID),
            ).where(
                SUPPLY_QUANTITY_RECOMMENDATION.DC_CODE.eq(dcCode).and(
                    SUPPLY_QUANTITY_RECOMMENDATION.WEEK.eq(week),
                ),
            )
            .fetchAsync()
            .thenApply { result ->
                val zoneId = dcConfigService.dcConfigurations[dcCode]?.zoneId ?: ZoneOffset.UTC
                result.map { record ->
                    SupplyQuantityRecommendation(
                        skuId = record.get(SKU_SPECIFICATION_VIEW.ID),
                        skuCode = record.get(SKU_SPECIFICATION_VIEW.CODE),
                        skuName = record.get(SKU_SPECIFICATION_VIEW.NAME),
                        skuCategory = record.get(SKU_SPECIFICATION_VIEW.CATEGORY),
                        dcCode = record.get(SUPPLY_QUANTITY_RECOMMENDATION.DC_CODE),
                        week = record.get(SUPPLY_QUANTITY_RECOMMENDATION.WEEK),
                        uom = mapToSkuUom(record.get(SUPPLY_QUANTITY_RECOMMENDATION.UOM)),
                        supplyQuantityRecommendation = record.get(SUPPLY_QUANTITY_RECOMMENDATION.SQR),
                        inventoryRollover = record.get(SUPPLY_QUANTITY_RECOMMENDATION.INVENTORY_ROLLOVER),
                        demand = record.get(SUPPLY_QUANTITY_RECOMMENDATION.DEMAND),
                        recommendationEnabled = record.get(SUPPLY_QUANTITY_RECOMMENDATION.RECOMMENDATION_ENABLED),
                        multiWeekEnabled = record.get(SUPPLY_QUANTITY_RECOMMENDATION.MULTI_WEEK_ENABLED),
                        safetyStock = record.get(SUPPLY_QUANTITY_RECOMMENDATION.SAFETY_STOCK),
                        updatedAt = record.get(SUPPLY_QUANTITY_RECOMMENDATION.UPDATED_AT)?.atZoneSameInstant(zoneId)?.toOffsetDateTime()
                            ?: OffsetDateTime.now(zoneId),
                    )
                }
            }.await()

    private fun mapToSkuUom(uom: Uom) =
        when (uom) {
            UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
            UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
            UOM_UNIT -> SkuUOM.UOM_UNIT
            UOM_KG -> SkuUOM.UOM_KG
            UOM_LBS -> SkuUOM.UOM_LBS
            UOM_GAL -> SkuUOM.UOM_GAL
            UOM_LITRE -> SkuUOM.UOM_LITRE
            UOM_OZ -> SkuUOM.UOM_OZ
        }
}

data class SupplyQuantityRecommendation(
    val skuId: UUID,
    val skuCode: String,
    val skuName: String,
    val skuCategory: String,
    val dcCode: String,
    val week: String,
    val uom: SkuUOM,
    val supplyQuantityRecommendation: BigDecimal,
    val inventoryRollover: BigDecimal,
    val safetyStock: BigDecimal?,
    val demand: BigDecimal,
    val recommendationEnabled: Boolean,
    val multiWeekEnabled: Boolean,
    val updatedAt: OffsetDateTime,
) {
    companion object
}
