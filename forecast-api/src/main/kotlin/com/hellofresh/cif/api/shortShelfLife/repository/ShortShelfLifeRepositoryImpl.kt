package com.hellofresh.cif.api.shortShelfLife.repository

import com.hellofresh.cif.api.calculation.generated.model.UomEnum
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLife
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeConfig
import com.hellofresh.cif.api.shortShelfLife.model.SkuSpecification
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateApiRepositoryImpl
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateDto
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.DSLContext
import org.jooq.Record

class ShortShelfLifeRepositoryImpl(
    private val readWriteMetricsDSLContext: MetricsDSLContext,
    private val readonlyMetricsDSLContext: MetricsDSLContext,
) : ShortShelfLifeRepository {
    override suspend fun getShortShelfLife(
        dcCode: DcCode,
        dates: List<LocalDate>,
    ): List<ShortShelfLife> {
        val result = with(Tables.SQR_SHORT_SHELF_LIFE) {
            readonlyMetricsDSLContext
                .withTagName("fetch-async-short-shelf-life")
                .select(
                    Tables.SKU_SPECIFICATION_VIEW.ID,
                    Tables.SKU_SPECIFICATION_VIEW.NAME,
                    Tables.SKU_SPECIFICATION_VIEW.CODE,
                    Tables.SKU_SPECIFICATION_VIEW.CATEGORY,
                    SKU_ID, DC_CODE, DATE, OPENING_SOCK, UNUSABLE_STOCK, CONSUMPTION, STOCK_UPDATE,
                    BUFFER_ADDITIONAL, BUFFER_PERCENTAGE, SQR, UOM, TOUCHLESS_ORDERING_ENABLED,
                )
                .from(this)
                .join(Tables.SKU_SPECIFICATION_VIEW)
                .on(SKU_ID.eq(Tables.SKU_SPECIFICATION_VIEW.ID))
                .where(
                    DC_CODE
                        .eq(dcCode)
                        .and(DATE.`in`(dates)),
                )
                .fetchAsync()
                .thenApply { records ->
                    records.map { record -> mapToShortShelfLife(record) }
                }
                .await()
        }

        return result
    }

    override suspend fun upsertShortShelfLifeConfig(
        configs: List<ShortShelfLifeConfig>,
        stockUpdates: List<StockUpdateDto>,
    ) =
        readWriteMetricsDSLContext
            .withTagName("short-shelf-life-config-upsert")
            .transactionResultAsync { tx ->
                val txDsl = readonlyMetricsDSLContext.withMeteredConfiguration(tx)
                // upsert ssl
                if (configs.isNotEmpty()) {
                    insertShortShelfLifeConfigs(txDsl, configs)
                }

                // upsert stock update
                if (stockUpdates.isNotEmpty()) {
                    insertStockUpdates(txDsl, stockUpdates)
                } else {
                    emptyList()
                }
            }.await()

    override suspend fun getSkuSpecificationById(skuId: UUID): SkuSpecification =
        with(Tables.SKU_SPECIFICATION_VIEW) {
            readonlyMetricsDSLContext
                .withTagName("fetch-sku-specification-by-id")
                .select(
                    ID,
                    NAME,
                    CODE,
                    CATEGORY,
                    UOM,
                )
                .from(this)
                .where(ID.eq(skuId))
                .fetchAsync()
                .thenApply { records ->
                    val record = records.firstOrNull() ?: throw IllegalArgumentException("Sku not found")
                    SkuSpecification(
                        skuId = record[ID],
                        name = record[NAME],
                        skuCode = record[CODE],
                        category = record[CATEGORY],
                        uom = mapUomToUomEnum(record[UOM].toString()),
                    )
                }
                .await()
        }

    private fun insertShortShelfLifeConfigs(dsl: DSLContext, configs: List<ShortShelfLifeConfig>) {
        with(Tables.SQR_SHORT_SHELF_LIFE_CONF) {
            val queries = configs.map { config ->
                dsl.insertInto(this)
                    .columns(DATE, SKU_ID, DC_CODE, BUFFER_ADDITIONAL, BUFFER_PERCENTAGE, TOUCHLESS_ORDERING_ENABLED)
                    .values(
                        config.date,
                        config.skuId,
                        config.dcCode,
                        config.bufferAdditional,
                        config.bufferPercentage,
                        config.touchlessOrderingEnabled,
                    )
                    .onConflict(DATE, SKU_ID, DC_CODE)
                    .doUpdate()
                    .set(BUFFER_ADDITIONAL, config.bufferAdditional)
                    .set(BUFFER_PERCENTAGE, config.bufferPercentage)
                    .set(TOUCHLESS_ORDERING_ENABLED, config.touchlessOrderingEnabled)
            }
            dsl
                .batch(queries)
                .execute()
        }
    }

    private fun insertStockUpdates(dsl: MetricsDSLContext, stockUpdates: List<StockUpdateDto>) =
        StockUpdateApiRepositoryImpl.upsertStockUpdate(dsl, stockUpdates)

    private fun mapToShortShelfLife(record: Record): ShortShelfLife =
        with(Tables.SQR_SHORT_SHELF_LIFE) {
            ShortShelfLife(
                skuId = record[Tables.SKU_SPECIFICATION_VIEW.ID],
                skuName = record[Tables.SKU_SPECIFICATION_VIEW.NAME],
                skuCode = record[Tables.SKU_SPECIFICATION_VIEW.CODE],
                skuCategory = record[Tables.SKU_SPECIFICATION_VIEW.CATEGORY],
                dcCode = record[DC_CODE],
                dcWeek = "",
                date = record[DATE],
                openingStock = record[OPENING_SOCK],
                unusableStock = record[UNUSABLE_STOCK],
                consumption = record[CONSUMPTION],
                stockUpdates = record[STOCK_UPDATE],
                bufferPercentage = record[BUFFER_PERCENTAGE],
                bufferAdditional = record[BUFFER_ADDITIONAL],
                sqrQuantity = record[SQR],
                uom = mapUomToUomEnum(record[UOM].toString()),
                touchlessOrderingEnabled = record[TOUCHLESS_ORDERING_ENABLED],
            )
        }
}

private fun mapUomToUomEnum(uom: String): UomEnum =
    when (uom) {
        "UOM_UNSPECIFIED" -> UomEnum.UNRECOGNIZED
        "UOM_UNIT" -> UomEnum.UNIT
        "UOM_KG" -> UomEnum.KG
        "UOM_LBS" -> UomEnum.LBS
        "UOM_GAL" -> UomEnum.GAL
        "UOM_LITRE" -> UomEnum.LITRE
        "UOM_OZ" -> UomEnum.OZ
        "UOM_UNRECOGNIZED" -> UomEnum.UNRECOGNIZED
        else -> {
            UomEnum.UNIT
        }
    }
