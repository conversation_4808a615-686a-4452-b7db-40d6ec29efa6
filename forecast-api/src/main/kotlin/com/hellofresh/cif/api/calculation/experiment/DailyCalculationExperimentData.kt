package com.hellofresh.cif.api.calculation.experiment

import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

data class DailyCalculationExperimentData(
    val dcCode: String,
    val skuId: UUID,
    val weeks: Set<String>,
    val calculatorMode: CalculatorMode,
    val experiments: Map<LocalDate, BigDecimal>
) {
    fun calculationExperiments() = experiments.mapKeys { CalculationKey(skuId, dcCode, it.key) }
}
