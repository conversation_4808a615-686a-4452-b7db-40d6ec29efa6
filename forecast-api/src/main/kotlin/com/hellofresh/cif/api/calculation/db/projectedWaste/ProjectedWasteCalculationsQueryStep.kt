package com.hellofresh.cif.api.calculation.db.projectedWaste

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.api.calculation.AdditionalFilter
import com.hellofresh.cif.api.calculation.AllPages
import com.hellofresh.cif.api.calculation.CalculationsPage
import com.hellofresh.cif.api.calculation.InventoryRefreshType.CLEARDOWN
import com.hellofresh.cif.api.calculation.Page
import com.hellofresh.cif.api.calculation.PageRequest
import com.hellofresh.cif.api.calculation.SortBy
import com.hellofresh.cif.api.calculation.SortBy.SKU_CODE
import com.hellofresh.cif.api.calculation.SortBy.SKU_NAME
import com.hellofresh.cif.api.calculation.db.CSKU_ID
import com.hellofresh.cif.api.calculation.db.CalculationsConditions
import com.hellofresh.cif.api.calculation.db.CalculationsQueryStep.CalculationInventoryRefreshMode
import com.hellofresh.cif.api.schema.Tables.CALCULATION
import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import java.util.concurrent.CompletionStage
import kotlin.math.ceil
import kotlinx.coroutines.future.await
import org.jetbrains.annotations.VisibleForTesting
import org.jooq.JSON
import org.jooq.JSONArrayAggOrderByStep
import org.jooq.OrderField
import org.jooq.Record
import org.jooq.Result
import org.jooq.impl.DSL
import org.jooq.impl.DSL.count
import org.jooq.impl.DSL.select
import org.jooq.impl.SQLDataType

@Suppress("TooManyFunctions")
class ProjectedWasteCalculationsQueryStep(
    private val dsl: MetricsDSLContext,
    dcCodes: List<String>,
    weeks: List<String>,
    sortBy: SortBy
) {

    private val fetchProjectedWasteCalculation = "fetch-projected-waste-calculations"
    private val fetchProjectedWasteCalculations = "fetch-all-projected-waste-calculations"
    private val fetchProjectedWasteSkuCount = "fetch-projected-waste-sku-count"

    private val cskuIdColumn = CALCULATION.field(CSKU_ID, SQLDataType.UUID)!!

    private val groupBySkuColumn = listOf(
        SKU_SPECIFICATION_VIEW.ID,
        when (sortBy) {
            SKU_NAME -> SKU_SPECIFICATION_VIEW.NAME
            SKU_CODE -> SKU_SPECIFICATION_VIEW.CODE
        },
    )

    private val sortFields: MutableList<OrderField<*>> = mutableListOf(
        when (sortBy) {
            SKU_NAME -> SKU_SPECIFICATION_VIEW.NAME.sortDefault()
            SKU_CODE -> SKU_SPECIFICATION_VIEW.CODE.sortDefault()
        },
    )

    private val skuCalculationStatement = select(groupBySkuColumn + skuCalculationsField())
        .from(CALCULATION)
        .join(SKU_SPECIFICATION_VIEW).on(SKU_SPECIFICATION_VIEW.ID.eq(cskuIdColumn))

    private val calculationConditionsStep =
        CalculationsConditions(dcCodes, weeks, CalculationInventoryRefreshMode(true, CLEARDOWN))
    private fun skuCalculationsField(): JSONArrayAggOrderByStep<JSON> {
        val fields = setOf(
            CALCULATION.DC_CODE,
            CALCULATION.CSKU_ID,
            CALCULATION.UNUSABLE_INVENTORY,
            CALCULATION.UOM,
        )
        val skuSpecFields = setOf(
            SKU_SPECIFICATION_VIEW.NAME,
            SKU_SPECIFICATION_VIEW.CODE,
            SKU_SPECIFICATION_VIEW.CATEGORY,
            SKU_SPECIFICATION_VIEW.ACCEPTABLE_CODE_LIFE,
        )
        return DSL.jsonArrayAgg(
            DSL.jsonObject(
                fields
                    .map { DSL.key(it.name).value(it) } +
                    skuSpecFields.map { DSL.key(it.name).value(it) }
            ),
        )
    }
    fun withSkus(codes: List<String>): ProjectedWasteCalculationsQueryStep = this.apply {
        calculationConditionsStep.withSkus(codes)
    }
    fun withSkuCategories(categories: List<String>): ProjectedWasteCalculationsQueryStep = this.apply {
        calculationConditionsStep.withSkuCategories(categories)
    }
    fun withAdditionalFilters(additionalFilters: Set<AdditionalFilter>) = this.apply {
        calculationConditionsStep.withAdditionalFilters(additionalFilters)
    }
    suspend fun fetchPageableCalculations(page: PageRequest): CalculationsPage<ProjectedWasteCalculationRecord> =
        when (page) {
            AllPages -> fetchAll() to fetchPageMetadata()
            is Page -> fetch(page) to fetchPageMetadata(page)
        }.let { (calculationPageStage, pageMetadataStage) ->
            val pageMetadata = pageMetadataStage.await()
            CalculationsPage(
                calculationPage = calculationPageStage.await(),
                totalPages = pageMetadata.totalPages,
                totalSkuAtRiskCount = 0,
                totalSkuCount = pageMetadata.skusCount.totalSkus,
            )
        }
    private fun fetch(page: Page): CompletionStage<List<ProjectedWasteCalculationRecord>> =
        dsl.withTagName(fetchProjectedWasteCalculation)
            .fetchAsync(
                skuCalculationStatement
                    .where(calculationConditionsStep.conditions)
                    .groupBy(groupBySkuColumn)
                    .orderBy(sortFields)
                    .offset(page.offset)
                    .limit(page.skuCount),
            ).thenApply(::deserializeJson)

    private fun fetchAll() =
        dsl.withTagName(fetchProjectedWasteCalculations)
            .fetchAsync(
                skuCalculationStatement
                    .where(calculationConditionsStep.conditions)
                    .groupBy(groupBySkuColumn)
                    .orderBy(groupBySkuColumn),
            ).thenApply(::deserializeJson)

    private fun deserializeJson(results: Result<Record>) = results.flatMap { r ->
        val calculationsArrayJsonStr = r[skuCalculationsField()].toString()
        deserializeCalculation(calculationsArrayJsonStr).asList()
    }
    private fun fetchPageMetadata() = fetchSkusCount().thenApply { PageMetadata(it, 1) }

    private fun fetchPageMetadata(page: Page) =
        fetchSkusCount().thenApply { PageMetadata(it, ceil(it.totalSkus.toDouble() / page.skuCount).toInt()) }

    private fun fetchSkusCount() =
        run {
            val nestedSkuSelect = select(SKU_SPECIFICATION_VIEW.CODE)
                .from(CALCULATION)
                .join(SKU_SPECIFICATION_VIEW).on(SKU_SPECIFICATION_VIEW.ID.eq(cskuIdColumn))
                .where(calculationConditionsStep.conditions)
                .groupBy(SKU_SPECIFICATION_VIEW.CODE)
            dsl.withTagName(fetchProjectedWasteSkuCount)
                .select(count())
                .from(nestedSkuSelect)
                .fetchAsync()
                .thenApply { r ->
                    val skuCountList: List<Int> = r.map { it.value1() }
                    SkusCount(totalSkus = skuCountList.sum())
                }
        }

    private data class PageMetadata(val skusCount: SkusCount, val totalPages: Int)
    private data class SkusCount(val totalSkus: Int)

    companion object {
        private val objectMapper: ObjectMapper = jacksonObjectMapper()
            .findAndRegisterModules()
            .setPropertyNamingStrategy(SnakeCaseStrategy())

        @VisibleForTesting
        fun deserializeCalculation(calculationsArrayJsonStr: String): Array<ProjectedWasteCalculationRecord> =
            objectMapper.readValue(calculationsArrayJsonStr, Array<ProjectedWasteCalculationRecord>::class.java)
    }
}
