package com.hellofresh.cif.api.configuration

import com.hellofresh.cif.api.error.mapToErrorResponse
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.config.CountryMarketMapping
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.plugins.NotFoundException
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.routing
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

fun Routing.config(
    configService: ConfigService,
    timeoutInMillis: Long
) = authenticate {
    get("/config/{country}") {
        runCatching {
            val country = call.parameters.getOrThrow("country").trim().uppercase()
            CountryMarketMapping.countryMarketMap[country] ?: throw NotFoundException("Country $country not found")
        }.onFailure { exception ->
            if (exception is NotFoundException) {
                call.respond(HttpStatusCode.NotFound, mapToErrorResponse(exception))
            } else {
                call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
            }
        }.onSuccess { market ->
            runCatching {
                withTimeout(timeoutInMillis) {
                    configService.fetchByMarket(market)
                }.mapValues { toConfigurationResponse(it.value) }
            }
                .onSuccess { result ->
                    val status = if (result.isEmpty()) HttpStatusCode.NotFound else HttpStatusCode.OK
                    call.respond(status, result)
                }
                .onFailure { call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(it)) }
        }
    }
}

fun configRoutingModule(
    configService: ConfigService,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        config(configService, timeout.inWholeMilliseconds).also {
            configureSerialization(it)
        }
    }
}
