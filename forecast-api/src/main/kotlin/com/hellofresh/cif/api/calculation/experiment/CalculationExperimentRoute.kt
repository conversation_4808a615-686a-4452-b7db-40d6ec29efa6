package com.hellofresh.cif.api.calculation.experiment

import com.hellofresh.cif.api.calculation.generated.model.CalculationExperimentRequestInner
import com.hellofresh.cif.api.calculation.generated.model.CalculationWeeklyExperimentsRequestInner
import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.getAllOrDefault
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getOrDefault
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.RequestValidationException
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.authenticate
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

private const val WEEKS = "weeks"
fun Routing.calculationExperiment(
    calculationExperimentService: CalculationExperimentService,
    timeoutInMillis: Long
) = authenticate {
    route("/calculation/experiment") {
        post("/{dcCode}/{skuId}") {
            handleDailyCalculationExperimentRequest(
                this.call,
                timeoutInMillis,
                calculationExperimentService,
            )
        }

        post("/{skuId}") {
            handleDailyCalculationExperimentRequest(
                this.call,
                timeoutInMillis,
                calculationExperimentService,
            )
        }

        post("/{skuId}/weeklyView") {
            withTimeout(timeoutInMillis) {
                runCatching {
                    val weeklyCalculationExperimentData = toWeeklyCalculationExperimentData(call)
                    calculationExperimentService.getWeeklyCalculationExperiment(weeklyCalculationExperimentData)
                }.onSuccess {
                    call.respond(HttpStatusCode.OK, it)
                }.onFailure {
                    call.handleResponseError(it)
                }
            }
        }
    }
}

fun calculationExperimentModule(
    calculationExperimentService: CalculationExperimentService,
    timeout: Duration
): Application.() -> Unit = {
    routing {
        calculationExperiment(
            calculationExperimentService,
            timeout.inWholeMilliseconds,
        ).also {
            configureSerialization(it)
        }
    }
}

private suspend fun handleDailyCalculationExperimentRequest(
    call: ApplicationCall,
    timeoutInMillis: Long,
    dailyCalculationExperimentService: CalculationExperimentService,
) {
    withTimeout(timeoutInMillis) {
        runCatching {
            val calculationExperimentData = toCalculationExperimentData(call)
            val calculationExperimentResults = dailyCalculationExperimentService.getCalculationExperiment(
                calculationExperimentData,
            )
            CalculationExperimentResponseMapper.mapToCalculationExperimentResponse(
                calculationExperimentResults,
            )
        }.onSuccess {
            call.respond(HttpStatusCode.OK, it)
        }.onFailure {
            call.handleResponseError(it)
        }
    }
}

private suspend fun toCalculationExperimentData(applicationCall: ApplicationCall) = DailyCalculationExperimentData(
    dcCode = getDcCode(applicationCall),
    skuId = extractSkuId(applicationCall),
    weeks = applicationCall.parameters.getAllOrDefault(WEEKS).toSet(),
    calculatorMode = parseConsumptionDaysAheadCalculatorMode(applicationCall.parameters),
    experiments = applicationCall.receive<Array<CalculationExperimentRequestInner>>()
        .associate { it.date to it.experiment.toBigDecimal() },
)

private suspend fun toWeeklyCalculationExperimentData(applicationCall: ApplicationCall) = WeeklyCalculationExperimentData(
    dcCode = getDcCode(applicationCall),
    skuId = extractSkuId(applicationCall),
    weeks = applicationCall.parameters.getAllOrDefault(WEEKS).toSet(),
    calculatorMode = parseConsumptionDaysAheadCalculatorMode(applicationCall.parameters),
    experiments = applicationCall.receive<Array<CalculationWeeklyExperimentsRequestInner>>()
        .associate { DcWeek(it.week) to it.experiment.toBigDecimal() },
)

private fun parseConsumptionDaysAheadCalculatorMode(parameters: Parameters): CalculatorMode {
    val consumptionDaysAhead = parameters.getOrDefault("consumptionDaysAhead", "0").toInt()
    return when {
        consumptionDaysAhead == 0 -> CalculatorMode.PRODUCTION
        consumptionDaysAhead == 1 -> CalculatorMode.PRE_PRODUCTION
        consumptionDaysAhead < 0 -> throw RequestValidationException(
            "Please enter value greater than zero",
            HttpStatusCode.BadRequest,
        )

        else -> throw RequestValidationException(
            "Calculations for $consumptionDaysAhead days ahead is not yet implemented",
            HttpStatusCode.NotImplemented,
        )
    }
}

private fun getDcCode(applicationCall: ApplicationCall): String {
    val dcCodes = applicationCall.parameters.getAllOrThrow("dcCode").toSet()
    require(dcCodes.count() == 1) {
        "Multiple DCs are not supported"
    }
    return dcCodes.first()
}

private fun extractSkuId(applicationCall: ApplicationCall) =
    UUID.fromString(applicationCall.parameters.getOrThrow("skuId").trim())
