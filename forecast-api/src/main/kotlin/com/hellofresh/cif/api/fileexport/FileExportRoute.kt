package com.hellofresh.cif.api.fileexport

import com.hellofresh.cif.api.calculation.generated.model.ErrorResponse
import com.hellofresh.cif.api.calculation.generated.model.FileExportResponse
import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.fileexport.repository.FileExportRequest
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.api.schema.enums.ExportStatus
import com.hellofresh.cif.api.schema.enums.ExportStatus.Completed
import com.hellofresh.cif.api.schema.enums.ExportStatus.Failed
import com.hellofresh.cif.api.schema.enums.ExportStatus.Pending
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout
import org.jooq.tools.json.JSONValue

fun Routing.fileExport(
    csvExportService: FileExportService,
    timeoutInMillis: Long,
) =
    authenticate {
        route("/export") {
            get("/{requestId}") {
                withTimeout(timeoutInMillis) {
                    kotlin.runCatching {
                        val requestId = UUID.fromString(call.parameters.getOrThrow("requestId").trim())
                        kotlin.runCatching {
                            csvExportService.getFileExportRequest(requestId)
                        }.onSuccess { result ->
                            result?.let {
                                call.respond(HttpStatusCode.OK, mapToFileExportResponse(result))
                            } ?: call.respond(HttpStatusCode.NotFound, ErrorResponse("File Export request not found: $requestId"))
                        }.onFailure {
                            call.handleResponseError(it.cause?.cause ?: it)
                        }
                    }.onFailure {
                        call.handleResponseError(it.cause?.cause ?: it)
                    }
                }
            }
        }
    }

fun mapToFileExportResponse(fileExportRequest: FileExportRequest): FileExportResponse =
    FileExportResponse(
        requestId = fileExportRequest.requestId,
        parameters = JSONValue.escape(fileExportRequest.jsonParameters),
        status = fileExportRequest.status.mapToFileExportStatus(),
        fileUrl = fileExportRequest.fileUrl,
    )

fun ExportStatus.mapToFileExportStatus() =
    when (this) {
        Pending -> FileExportResponse.Status.PENDING
        Failed -> FileExportResponse.Status.FAILED
        Completed -> FileExportResponse.Status.COMPLETED
    }

fun fileExportModule(
    csvExportService: FileExportService,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        fileExport(
            csvExportService,
            timeout.inWholeMilliseconds,
        ).also {
            configureSerialization(it)
        }
    }
}
