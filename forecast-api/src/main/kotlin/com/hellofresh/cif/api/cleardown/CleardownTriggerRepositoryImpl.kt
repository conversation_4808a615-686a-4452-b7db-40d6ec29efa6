package com.hellofresh.cif.api.cleardown

import com.hellofresh.cif.api.schema.tables.records.InventoryCleardownTriggerRecord
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import io.ktor.server.plugins.NotFoundException
import java.time.OffsetDateTime
import java.util.UUID
import kotlinx.coroutines.future.await

class CleardownTriggerRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService,
) : CleardownTriggerRepository {

    private val saveInventoryCleardownTrigger = "save-inventory-cleardown-trigger"

    override suspend fun saveCleardownTrigger(
        dcCodes: List<String>,
        authorEmail: String,
        authorName: String?,
        snapshotId: UUID?,
    ) {
        val cleardownTriggerRecords = prepareCleardownTriggerRecords(
            dcCodes,
            authorEmail,
            authorName,
            snapshotId,
        )
        metricsDSLContext.withTagName(saveInventoryCleardownTrigger)
            .batchInsert(cleardownTriggerRecords)
            .executeAsync()
            .await()
    }

    private fun prepareCleardownTriggerRecords(
        dcCodes: List<String>,
        authorEmail: String,
        authorName: String?,
        snapshotId: UUID?,
    ) = dcCodes.map { dcCode ->
        val dc = dcConfigService.dcConfigurations[dcCode]
            ?: throw NotFoundException("DcCode $dcCode not found")
        val now = OffsetDateTime.now(dc.zoneId)
        dcCode to now
    }.map { (dcCode, now) ->
        InventoryCleardownTriggerRecord().apply {
            this.id = UUID.randomUUID()
            this.dcCode = dcCode
            this.timestamp = now
            this.authorEmail = authorEmail
            this.authorName = authorName
            this.inventorySnapshotId = snapshotId
        }
    }
}
