package com.hellofresh.cif.api.note

import com.hellofresh.cif.api.calculation.AdditionalFilter
import com.hellofresh.cif.api.calculation.InventoryRefreshType.CLEARDOWN
import com.hellofresh.cif.api.calculation.db.CalculationsAdditionalFilterConditions
import com.hellofresh.cif.api.calculation.db.CalculationsQueryStep.CalculationInventoryRefreshMode
import com.hellofresh.cif.api.schema.Tables.CALCULATION
import com.hellofresh.cif.api.schema.Tables.NOTE
import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.api.schema.tables.Note
import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.util.UUID
import org.jooq.Table
import org.jooq.impl.DSL
import org.jooq.impl.DSL.any
import org.jooq.impl.DSL.arrayOverlap

class NoteFilterConditions(private val noteGetTable: Table<*>) {

    fun buildNoteFilterConditions(findNotesRequest: FindNotesRequest) =
        withDcs(findNotesRequest.dcCodes) +
            withProductionWeeks(findNotesRequest.dcWeeks) +
            withSkus(findNotesRequest.skuIds) +
            withSkuCategories(findNotesRequest.skuCategories) +
            withLocationInBox(findNotesRequest.locationInBox) +
            withAdditionalFilters(findNotesRequest.additionalFilters)

    private fun withDcs(dcCodes: Set<String>) =
        listOf(
            arrayOverlap(
                noteGetTable.field(Note.NOTE.DC_CODES),
                DSL.cast(DSL.array(dcCodes.toTypedArray()), Note.NOTE.DC_CODES.dataType),
            ),
        )

    private fun withProductionWeeks(dcWeeks: Set<DcWeek>) =
        listOf(
            arrayOverlap(
                noteGetTable.field(Note.NOTE.PRODUCTION_WEEKS),
                DSL.cast(DSL.array(dcWeeks.map { it.value }.toTypedArray()), Note.NOTE.PRODUCTION_WEEKS.dataType),
            ),
        )

    private fun withSkus(skuIds: Set<UUID>) =
        if (skuIds.isNotEmpty()) {
            listOf(noteGetTable.field(NOTE.SKU_ID)!!.`in`(skuIds))
        } else {
            emptyList()
        }

    private fun withSkuCategories(categories: Set<String>) =
        if (categories.isNotEmpty()) {
            listOf(noteGetTable.field(SKU_SPECIFICATION_VIEW.CATEGORY)!!.`in`(categories))
        } else {
            emptyList()
        }

    private fun withLocationInBox(locationInBox: Set<String>) =
        if (locationInBox.isNotEmpty()) {
            listOf(noteGetTable.field(SKU_SPECIFICATION_VIEW.PACKAGING)!!.`in`(locationInBox))
        } else {
            emptyList()
        }

    private fun withAdditionalFilters(filters: Set<AdditionalFilter>) =
        CalculationsAdditionalFilterConditions(
            noteGetTable.field(NOTE.SKU_ID)!!,
            CalculationInventoryRefreshMode(true, CLEARDOWN),
        ) { skuId, dcCode, week ->
            skuId.eq(noteGetTable.field(NOTE.SKU_ID))
                .and(dcCode.eq(any(noteGetTable.field(NOTE.DC_CODES))))
                .and(week.eq(any(noteGetTable.field(NOTE.PRODUCTION_WEEKS))))
        }.withAdditionalFilters(filters, CALCULATION)
}
