package com.hellofresh.cif.api.calculation.db

import com.hellofresh.cif.api.calculation.CalculationFilterRequest
import com.hellofresh.cif.api.calculation.CalculationFiltersParams
import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.CalculationsPage
import com.hellofresh.cif.api.calculation.db.CalculationsQueryStep.CalculationInventoryRefreshMode
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.enums.Uom.UOM_GAL
import com.hellofresh.cif.api.schema.enums.Uom.UOM_KG
import com.hellofresh.cif.api.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.api.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.api.schema.enums.Uom.UOM_OZ
import com.hellofresh.cif.api.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.api.schema.enums.Uom.UOM_UNRECOGNIZED
import com.hellofresh.cif.api.schema.enums.Uom.UOM_UNSPECIFIED
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.SkuUOM

class CalculationsRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
    private val isProdCalculation: Boolean = true,
) : CalculationRepository {

    override suspend fun fetchPageableCalculations(calculationRequest: CalculationRequest): CalculationsPage<CalculationRecord> {
        val query = CalculationsQueryStep(
            metricsDSLContext,
            calculationRequest.dcCodes,
            calculationRequest.weeks,
            CalculationInventoryRefreshMode(isProdCalculation, calculationRequest.inventoryRefreshType),
            calculationRequest.sortBy,
        ).withFilterConditions(calculationRequest)

        return query.fetchPageableCalculations(calculationRequest.pageRequest)
    }

    override suspend fun fetchCalculationFilters(calculationFilterRequest: CalculationFilterRequest) =
        CalculationsFilterQueryStep(
            metricsDSLContext,
            calculationFilterRequest.dcCodes,
            calculationFilterRequest.weeks,
            CalculationInventoryRefreshMode(isProdCalculation, calculationFilterRequest.inventoryRefreshType),
        ).withFilterConditions(calculationFilterRequest)
            .fetch()

    private fun <T : CalculationConditionsStep> T.withFilterConditions(
        calculationFiltersParams: CalculationFiltersParams
    ) = apply {
        conditionsStep
            .withSkus(calculationFiltersParams.skuCodes)
            .withSkuCategories(calculationFiltersParams.skuCategories)
            .withLocationInBox(calculationFiltersParams.locationInBox)
            .withSupplierIds(calculationFiltersParams.supplierIds)
            .withActiveSupplierNames(calculationFiltersParams.activeSupplierNames)
            .withPoDueInGreaterThan(calculationFiltersParams.poDueInMin)
            .withPoDueInLessThan(calculationFiltersParams.poDueInMax)
            .withClosingStockLessThanOrEqual(calculationFiltersParams.closingStockLessThanOrEqual)
            .withAdditionalFilters(calculationFiltersParams.additionalFilters)
    }

    companion object
}

fun Uom.toSkuUom() =
    when (this) {
        UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
        UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
        UOM_UNIT -> SkuUOM.UOM_UNIT
        UOM_KG -> SkuUOM.UOM_KG
        UOM_LBS -> SkuUOM.UOM_LBS
        UOM_GAL -> SkuUOM.UOM_GAL
        UOM_LITRE -> SkuUOM.UOM_LITRE
        UOM_OZ -> SkuUOM.UOM_OZ
    }
