package com.hellofresh.cif.api.configuration

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID

const val TEN_WEEKS = 10L

@JsonIgnoreProperties(ignoreUnknown = true)
data class DcConfiguration(
    val dcCode: String,
    val market: String,
    val productionStartDay: DayOfWeek,
    val clearDownDay: DayOfWeek,
    val zoneId: ZoneId,
    val lastCleardownTime: OffsetDateTime?,
    val hasCleardown: Boolean,
) {
    init {
        require(!(hasCleardown && lastCleardownTime == null)) {
            "cleardown time should exist if the dc cleardown is enabled : $dcCode"
        }
    }

    private val distributionCenterConfiguration = DistributionCenterConfiguration(
        dcCode = dcCode,
        market = market,
        productionStart = productionStartDay,
        cleardown = clearDownDay,
        zoneId = zoneId,
        wmsType = WmsSystem.UNRECOGNIZED
    )
    val startDate: LocalDate = distributionCenterConfiguration.getProductionStartDate(LocalDate.now(zoneId))
    val lastCleardown: LocalDate = distributionCenterConfiguration.getLatestCleardown()
    val currentWeek = getCurrentDcWeek(distributionCenterConfiguration.productionStart).toString()
    val minWeek = getMinDcWeek(distributionCenterConfiguration.productionStart).toString()

    companion object {
        fun getCurrentDcWeek(productionStartWeekday: DayOfWeek) = DcWeek(
            LocalDate.now(ZoneOffset.UTC),
            productionStartWeekday,
        )

        fun getMinDcWeek(productionStartWeekday: DayOfWeek) = DcWeek(
            LocalDate.now(ZoneOffset.UTC).minusWeeks(TEN_WEEKS),
            productionStartWeekday,
        )
    }
}

data class Sku(
    val skuCode: String,
    val skuId: UUID,
    val skuName: String,
)

data class Supplier(
    val id: UUID,
    val name: String
)
