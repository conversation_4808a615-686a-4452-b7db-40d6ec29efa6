package com.hellofresh.cif.api.note

import com.hellofresh.cif.api.calculation.db.CalculationsQueryStep
import com.hellofresh.cif.api.schema.Tables.CALCULATION
import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.api.schema.tables.Note.NOTE
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.Record
import org.jooq.impl.DSL.any
import org.jooq.impl.DSL.inline
import org.jooq.impl.DSL.multiset
import org.jooq.impl.DSL.select
import org.jooq.impl.DSL.selectDistinct
import org.jooq.impl.DSL.selectFrom

class NoteRepositoryImpl(
    private val masterMetricsDSLContext: MetricsDSLContext,
    private val readonlyMetricsDSLContext: MetricsDSLContext,
) : NoteRepository {

    private val createNote = "create-note"
    private val updateNote = "update-note"
    private val deleteNote = "delete-note"
    private val getNote = "get-notes"

    private val calculationWeeksAtRiskTable =
        selectDistinct(CALCULATION.DC_CODE, CALCULATION.PRODUCTION_WEEK, CALCULATION.CSKU_ID)
            .from(CALCULATION)
            .where(CalculationsQueryStep.skuAtRiskCondition(CALCULATION))
            .asTable("calculationWeeksAtRisk")

    private val atRiskCskuIDField = calculationWeeksAtRiskTable.field(CALCULATION.CSKU_ID)!!
    private val atRiskDcCodeField = calculationWeeksAtRiskTable.field(CALCULATION.DC_CODE)!!
    private val atRiskProductionWeekField = calculationWeeksAtRiskTable.field(CALCULATION.PRODUCTION_WEEK)!!

    private val lastVersionNoteTable =
        with(
            select()
                .distinctOn(NOTE.ID)
                .from(NOTE)
                .orderBy(NOTE.ID, NOTE.VERSION.desc())
                .asTable("lastVersionWithDeleted"),
        ) {
            selectFrom(this)
                .where(this.field(NOTE.IS_DELETED)!!.isFalse)
                .asTable("lastVersionNotes")
        }

    private val multiSetWeeksAtRisk = multiset(
        selectFrom(calculationWeeksAtRiskTable)
            .where(atRiskCskuIDField.eq(lastVersionNoteTable.field(NOTE.SKU_ID)))
            .and(atRiskDcCodeField.eq(any(lastVersionNoteTable.field(NOTE.DC_CODES))))
            .and(atRiskProductionWeekField.eq(any(lastVersionNoteTable.field(NOTE.PRODUCTION_WEEKS)))),
    ).`as`("weeksAtRisk").convertFrom { result -> result.map { AtRisk(it.value1(), DcWeek(it.value2())) } }

    private val skuSpecIdName = "skuSpecId"
    private val skuSpecificationTable = select(
        SKU_SPECIFICATION_VIEW.ID.`as`(skuSpecIdName),
        SKU_SPECIFICATION_VIEW.CATEGORY,
        SKU_SPECIFICATION_VIEW.PACKAGING
    )
        .from(SKU_SPECIFICATION_VIEW).asTable("skuSpec")
    private val skuSpecId = skuSpecificationTable.field(skuSpecIdName, SKU_SPECIFICATION_VIEW.ID.dataType)!!

    private val noteWithRiskViewTable =
        select(
            lastVersionNoteTable.field(NOTE.ID),
            lastVersionNoteTable.field(NOTE.SKU_ID),
            lastVersionNoteTable.field(NOTE.AUTHOR_EMAIL),
            lastVersionNoteTable.field(NOTE.AUTHOR_NAME),
            lastVersionNoteTable.field(NOTE.DC_CODES),
            lastVersionNoteTable.field(NOTE.PRODUCTION_WEEKS),
            lastVersionNoteTable.field(NOTE.NOTE_TEXT),
            lastVersionNoteTable.field(NOTE.CREATED_AT),
            lastVersionNoteTable.field(NOTE.IS_DELETED),
            lastVersionNoteTable.field(NOTE.VERSION),
            skuSpecId,
            skuSpecificationTable.field(SKU_SPECIFICATION_VIEW.CATEGORY),
            skuSpecificationTable.field(SKU_SPECIFICATION_VIEW.PACKAGING),
            multiSetWeeksAtRisk,
        ).from(
            lastVersionNoteTable
                .leftJoin(skuSpecificationTable)
                .on(skuSpecId.eq(lastVersionNoteTable.field(NOTE.SKU_ID))),
        )
            .asTable("noteView")

    private val noteGetFilterConditions = NoteFilterConditions(noteWithRiskViewTable)

    override suspend fun createNote(
        skuId: UUID,
        authorEmail: String,
        authorName: String?,
        dcCodes: Set<String>,
        dcWeeks: Set<DcWeek>,
        text: String
    ) =
        masterMetricsDSLContext.withTagName(createNote)
            .insertInto(NOTE).columns(
                NOTE.ID,
                NOTE.SKU_ID,
                NOTE.AUTHOR_EMAIL,
                NOTE.AUTHOR_NAME,
                NOTE.DC_CODES,
                NOTE.PRODUCTION_WEEKS,
                NOTE.NOTE_TEXT,
            ).values(
                UUID.randomUUID(),
                skuId,
                authorEmail,
                authorName,
                dcCodes.toTypedArray(),
                dcWeeks.map { it.value }.toTypedArray(),
                text,
            ).returning()
            .fetchAsync()
            .thenApply {
                getNoteFromRecord(it[0])
            }.await()

    override suspend fun updateNote(
        id: UUID,
        authorName: String?,
        authorEmail: String,
        dcWeeks: Set<DcWeek>,
        text: String
    ) =
        masterMetricsDSLContext.withTagName(updateNote)
            .insertInto(NOTE)
            .columns(
                NOTE.ID,
                NOTE.VERSION,
                NOTE.SKU_ID,
                NOTE.DC_CODES,
                NOTE.PRODUCTION_WEEKS,
                NOTE.NOTE_TEXT,
                NOTE.AUTHOR_NAME,
                NOTE.AUTHOR_EMAIL,
                NOTE.IS_DELETED,
            )
            .select(
                select(
                    lastVersionNoteTable.field(NOTE.ID),
                    lastVersionNoteTable.field(NOTE.VERSION)?.plus(1),
                    lastVersionNoteTable.field(NOTE.SKU_ID),
                    lastVersionNoteTable.field(NOTE.DC_CODES),
                    inline(dcWeeks.map { it.value }.toTypedArray()),
                    inline(text),
                    inline(authorName),
                    inline(authorEmail),
                    inline(false),
                ).from(lastVersionNoteTable)
                    .where(lastVersionNoteTable.field(NOTE.ID)?.eq(id)),
            ).returning()
            .fetchAsync()
            .thenApply {
                require(it.size == 1) { "Expected to update 1 record but found instead ${it.size}" }
                getNoteFromRecord(it[0])
            }.await()

    override suspend fun deleteNote(
        id: UUID,
        authorName: String?,
        authorEmail: String
    ) = run {
        val numberOfRecordsDeleted = masterMetricsDSLContext.withTagName(deleteNote)
            .insertInto(NOTE)
            .columns(
                NOTE.ID,
                NOTE.VERSION,
                NOTE.SKU_ID,
                NOTE.DC_CODES,
                NOTE.PRODUCTION_WEEKS,
                NOTE.NOTE_TEXT,
                NOTE.AUTHOR_NAME,
                NOTE.AUTHOR_EMAIL,
                NOTE.IS_DELETED,
            )
            .select(
                select(
                    lastVersionNoteTable.field(NOTE.ID),
                    lastVersionNoteTable.field(NOTE.VERSION)?.plus(1),
                    lastVersionNoteTable.field(NOTE.SKU_ID),
                    lastVersionNoteTable.field(NOTE.DC_CODES),
                    lastVersionNoteTable.field(NOTE.PRODUCTION_WEEKS),
                    lastVersionNoteTable.field(NOTE.NOTE_TEXT),
                    inline(authorName),
                    inline(authorEmail),
                    inline(true),
                ).from(lastVersionNoteTable)
                    .where(lastVersionNoteTable.field(NOTE.ID)?.eq(id)),
            ).executeAsync().await()
        require(numberOfRecordsDeleted == 1) { "Expected to update 1 record but found instead 0" }
        numberOfRecordsDeleted
    }

    override suspend fun getNotes(findNotesRequest: FindNotesRequest): List<Note> =
        readonlyMetricsDSLContext.withTagName(getNote)
            .selectFrom(noteWithRiskViewTable)
            .where(noteGetFilterConditions.buildNoteFilterConditions(findNotesRequest))
            .orderBy(noteWithRiskViewTable.field(NOTE.CREATED_AT)?.desc())
            .fetchAsync()
            .thenApply {
                it.map { r ->
                    val weeksAtRisk = r.value14()
                    getNoteFromRecord(r, weeksAtRisk)
                }
            }.await()

    private fun getNoteFromRecord(record: Record, weeksAtRisk: List<AtRisk> = listOf()): Note {
        val noteRecord = record.into(NOTE)
        val version = noteRecord.version
        return Note(
            noteRecord.id,
            noteRecord.skuId,
            noteRecord.authorEmail,
            noteRecord.authorName,
            noteRecord.dcCodes.toSet(),
            noteRecord.productionWeeks.map { productionWeek -> DcWeek(productionWeek) }.toSet(),
            noteRecord.noteText,
            noteRecord.createdAt,
            weeksAtRisk,
            version,
            noteRecord.isDeleted,
            version > 0,
        )
    }
}
