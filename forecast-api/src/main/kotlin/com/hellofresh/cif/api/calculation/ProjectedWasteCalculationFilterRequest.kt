package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.SortBy.SKU_NAME
import com.hellofresh.cif.api.getAllOrDefault
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getOrDefault
import io.ktor.http.Parameters

const val EXPIRING_IN_MIN_START = 0
const val EXPIRING_IN_MIN_END = 11

const val EXPIRING_IN_MAX_START = 1
const val EXPIRING_IN_MAX_END = 12

interface ProjectedWasteCalculationFiltersParams {
    val dcCodes: List<String>
    val weeks: List<String>
    val skuCodes: List<String>
    val sortBy: SortBy
    val skuCategories: List<String>
    val additionalFilters: Set<AdditionalFilter>
    val expiringInMin: Int?
    val expiringInMax: Int?
}

data class ProjectedWasteCalculationFilterRequest(
    override val dcCodes: List<String>,
    override val weeks: List<String> = emptyList(),
    override val skuCodes: List<String> = emptyList(),
    val pageRequest: PageRequest,
    override val sortBy: SortBy = SKU_NAME,
    override val skuCategories: List<String> = emptyList(),
    override val additionalFilters: Set<AdditionalFilter> = emptySet(),
    override val expiringInMin: Int? = null,
    override val expiringInMax: Int? = null,
) : ProjectedWasteCalculationFiltersParams {
    companion object {
        fun from(parameters: Parameters) = ProjectedWasteCalculationFilterRequest(
            dcCodes = parameters.getAllOrThrow("dcCode"),
            skuCodes = parameters.getAllOrDefault("skuCode"),
            pageRequest = Page(
                page = parameters.getOrDefault("page", "1").toInt(),
                skuCount = parameters.getOrDefault("skuCount", "50").toInt(),
            ),
            skuCategories = parameters.getAllOrDefault("skuCategory"),
            additionalFilters = parameters.getAllOrDefault("additionalFilters")
                .map { AdditionalFilter.valueOf(it.trim().uppercase()) }
                .toSet(),
            expiringInMin = parameters["expiringInGreaterThanOrEqual"]?.toInt()?.validateRange(
                "expiringInGreaterThanOrEqual",
                EXPIRING_IN_MIN_START,
                EXPIRING_IN_MIN_END,
            ),
            expiringInMax = parameters["expiringInLessThanOrEqual"]?.toInt()?.validateRange(
                "expiringInLessThanOrEqual",
                EXPIRING_IN_MAX_START,
                EXPIRING_IN_MAX_END,
            ),
        )
    }
}
