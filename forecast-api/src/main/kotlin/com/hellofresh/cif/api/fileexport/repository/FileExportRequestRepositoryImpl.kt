package com.hellofresh.cif.api.fileexport.repository

import com.hellofresh.cif.api.schema.enums.ExportStatus
import com.hellofresh.cif.api.schema.tables.FileExportRequest.FILE_EXPORT_REQUEST
import com.hellofresh.cif.api.schema.tables.records.FileExportRequestRecord
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import java.time.LocalDateTime
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.JSONB

class FileExportRequestRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
) : FileExportRequestRepository {

    override suspend fun fetchFileExportRequest(requestId: UUID): FileExportRequest? =
        metricsDSLContext.withTagName("fetch-file-export-request")
            .selectFrom(FILE_EXPORT_REQUEST)
            .where(FILE_EXPORT_REQUEST.REQUEST_ID.eq(requestId))
            .fetchAsync()
            .thenApply { result ->
                result.map { record ->
                    toFileExportRequest(record)
                }.firstOrNull()
            }.await()

    override suspend fun saveFileExportRequest(jsonParameters: String?): FileExportRequest =
        metricsDSLContext.withTagName("create-file-export-request")
            .insertInto(FILE_EXPORT_REQUEST)
            .set(
                FileExportRequestRecord().apply {
                    requestId = UUID.randomUUID()
                    parameters = JSONB.jsonbOrNull(jsonParameters)
                },
            )
            .returning()
            .fetchAsync()
            .thenApply { records ->
                records.firstOrNull()?.let { toFileExportRequest(it) }
            }.await()

    override suspend fun updateFileExportRequest(
        fileExportRequest: FileExportRequest
    ): FileExportRequest? =
        metricsDSLContext.withTagName("update-file-export-request")
            .update(FILE_EXPORT_REQUEST)
            .set(toFileExportRequestRecord(fileExportRequest))
            .where(FILE_EXPORT_REQUEST.REQUEST_ID.eq(fileExportRequest.requestId))
            .returning()
            .fetchAsync()
            .thenApply {
                toFileExportRequest(it.first())
            }.await()

    private fun toFileExportRequestRecord(fileExportRequest: FileExportRequest) =
        FileExportRequestRecord().apply {
            requestId = fileExportRequest.requestId
            parameters = JSONB.jsonbOrNull(fileExportRequest.jsonParameters)
            status = fileExportRequest.status
            fileUrl = fileExportRequest.fileUrl
        }

    private fun toFileExportRequest(record: FileExportRequestRecord): FileExportRequest =
        FileExportRequest(
            requestId = record.requestId,
            jsonParameters = record.parameters.data(),
            status = record.status,
            fileUrl = record.fileUrl,
            createdAt = record.createdAt,
            updatedAt = record.updatedAt,
        )
}

data class FileExportRequest(
    val requestId: UUID,
    val jsonParameters: String?,
    val status: ExportStatus,
    val fileUrl: String?,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime
)
