package com.hellofresh.cif.api.fileupload

import com.hellofresh.cif.api.error.mapToErrorResponse
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.routing
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

fun Routing.fileUpload(
    fileUploadService: FileUploadService,
    timeoutInMillis: Long,
) =
    authenticate {
        get("/file-uploads") {
            handleGetFileUploads(call, fileUploadService, timeoutInMillis)
        }
    }

private suspend fun handleGetFileUploads(
    call: ApplicationCall,
    fileUploadService: FileUploadService,
    timeoutInMillis: Long
) {
    withTimeout(timeoutInMillis) {
        runCatching {
            val market = call.parameters.getOrThrow("market")
            val fileType = call.parameters.getOrThrow("fileType")
            runCatching {
                fileUploadService.getFileUploads(market, fileType)
            }.onSuccess { result ->
                result.let { call.respond(HttpStatusCode.OK, result) }
            }.onFailure { exception ->
                call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(exception))
            }
        }.onFailure { exception ->
            call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
        }
    }
}

fun fileUploadModule(
    fileUploadService: FileUploadService,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        fileUpload(
            fileUploadService,
            timeout.inWholeMilliseconds,
        ).also {
            configureSerialization(it)
        }
    }
}
