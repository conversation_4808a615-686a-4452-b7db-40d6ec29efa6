package com.hellofresh.cif.api.supplyQuantityRecommendation.repository

import com.hellofresh.cif.api.schema.Tables.SAFETY_STOCK_CONF
import com.hellofresh.cif.api.schema.Tables.SUPPLY_QUANTITY_RECOMMENDATION_CONF
import com.hellofresh.cif.api.supplyQuantityRecommendation.SQRRequest
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.DSLContext
import org.jooq.impl.DSL

class SQRConfigRepositoryImpl(private val readWriteDslContext: MetricsDSLContext) : SQRConfigRepository {
    private val upsertSupplyQuantityRecommendation = "upsert-supply-quantity-recommendation"
    override suspend fun upsertSQRConfig(sqrRequest: SQRRequest) {
        logger.info("Saving/Updating supply quantity recommendation: $sqrRequest")
        readWriteDslContext.withTagName(upsertSupplyQuantityRecommendation).transactionAsync { tx ->
            val txDsl = tx.dsl()
            upsertSQRConf(txDsl, sqrRequest)
            upsertSafetyStockConf(txDsl, sqrRequest)
        }.await()
    }
    private fun upsertSafetyStockConf(
        txDsl: DSLContext,
        supplyQuantityRecommendationRequest: SQRRequest
    ) {
        txDsl.insertInto(SAFETY_STOCK_CONF)
            .columns(
                SAFETY_STOCK_CONF.DC_CODE,
                SAFETY_STOCK_CONF.WEEK,
                SAFETY_STOCK_CONF.SKU_ID,
                SAFETY_STOCK_CONF.RISK_MULTIPLIER,
                SAFETY_STOCK_CONF.SKU_RISK_RATING,
            ).values(
                supplyQuantityRecommendationRequest.dcCode,
                supplyQuantityRecommendationRequest.week,
                supplyQuantityRecommendationRequest.skuId,
                supplyQuantityRecommendationRequest.safetyMultiplier,
                supplyQuantityRecommendationRequest.skuRiskRating,
            ).onDuplicateKeyUpdate()
            .set(
                SAFETY_STOCK_CONF.RISK_MULTIPLIER,
                DSL.excluded(SAFETY_STOCK_CONF.RISK_MULTIPLIER),
            )
            .set(
                SAFETY_STOCK_CONF.SKU_RISK_RATING,
                DSL.excluded(SAFETY_STOCK_CONF.SKU_RISK_RATING),
            )
            .execute()

        txDsl.update(SAFETY_STOCK_CONF)
            .set(
                SAFETY_STOCK_CONF.RISK_MULTIPLIER,
                supplyQuantityRecommendationRequest.safetyMultiplier,
            )
            .set(
                SAFETY_STOCK_CONF.SKU_RISK_RATING,
                supplyQuantityRecommendationRequest.skuRiskRating,
            )
            .where(
                SAFETY_STOCK_CONF.DC_CODE.eq(supplyQuantityRecommendationRequest.dcCode)
                    .and(
                        SAFETY_STOCK_CONF.WEEK.greaterThan(
                            supplyQuantityRecommendationRequest.week,
                        ),
                    )
                    .and(SAFETY_STOCK_CONF.SKU_ID.eq(supplyQuantityRecommendationRequest.skuId)),
            ).execute()
    }

    private fun upsertSQRConf(
        txDsl: DSLContext,
        sqrRequest: SQRRequest
    ) {
        txDsl.insertInto(SUPPLY_QUANTITY_RECOMMENDATION_CONF)
            .columns(
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.DC_CODE,
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.WEEK,
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.SKU_ID,
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.RECOMMENDATION_ENABLED,
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.MULTI_WEEK_ENABLED,
            ).values(
                sqrRequest.dcCode,
                sqrRequest.week,
                sqrRequest.skuId,
                sqrRequest.recommendationEnabled,
                sqrRequest.multiWeekEnabled,
            ).onDuplicateKeyUpdate()
            .set(
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.RECOMMENDATION_ENABLED,
                DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION_CONF.RECOMMENDATION_ENABLED),
            )
            .set(
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.MULTI_WEEK_ENABLED,
                DSL.excluded(SUPPLY_QUANTITY_RECOMMENDATION_CONF.MULTI_WEEK_ENABLED),
            )
            .execute()

        txDsl.update(SUPPLY_QUANTITY_RECOMMENDATION_CONF)
            .set(
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.RECOMMENDATION_ENABLED,
                sqrRequest.recommendationEnabled,
            ).set(
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.MULTI_WEEK_ENABLED,
                sqrRequest.multiWeekEnabled
            )
            .where(
                SUPPLY_QUANTITY_RECOMMENDATION_CONF.DC_CODE.eq(sqrRequest.dcCode)
                    .and(
                        SUPPLY_QUANTITY_RECOMMENDATION_CONF.WEEK.greaterThan(
                            sqrRequest.week,
                        ),
                    )
                    .and(SUPPLY_QUANTITY_RECOMMENDATION_CONF.SKU_ID.eq(sqrRequest.skuId)),
            ).execute()
    }

    companion object : Logging
}
