package com.hellofresh.cif.api.shortShelfLife.model

import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.models.SkuQuantity
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

data class ShortShelfLifeConfig(
    val skuId: UUID,
    val dcCode: DcCode,
    val date: LocalDate,
    val bufferAdditional: BigDecimal,
    val bufferPercentage: BigDecimal,
    val touchlessOrderingEnabled: Boolean,
)

data class ShortShelfLifeConfigWithStockUpdate(
    val week: String,
    val bufferAdditional: BigDecimal,
    val bufferPercentage: BigDecimal,
    val stockUpdates: SkuQuantity?,
    val version: Int?,
    val touchlessOrderingEnabled: Boolean,
)

data class ShortShelfLifeConfigKey(
    val dcCode: DcCode,
    val skuId: UUID,
    val date: LocalDate,
)
