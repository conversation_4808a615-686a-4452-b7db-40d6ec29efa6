package com.hellofresh.cif.api.calculation.stockupdate

import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.calculator.models.DayCalculationResult

object DayCalculationResultMapper {

    fun toCalculationRecord(
        dayCalculationResult: DayCalculationResult,
        calculationRecord: CalculationRecord
    ) = with(dayCalculationResult) {
        calculationRecord.copy(
            expired = unusable.getValue(),
            openingStock = openingStock.getValue(),
            storageStock = storageStock.getValue(),
            stagingStock = stagingStock.getValue(),
            stockUpdate = stockUpdate?.getValue(),
            demanded = demanded.getValue(),
            present = present.getValue(),
            closingStock = closingStock.getValue(),
            actualInbound = actualInbound.getValue(),
            expectedInbound = expectedInbound.getValue(),
            actualInboundPo = actualInboundPurchaseOrders,
            expectedInboundPo = expectedInboundPurchaseOrders,
            dailyNeeds = dailyNeeds.getValue(),
            actualConsumption = actualConsumption.getValue(),
            safetyStock = safetyStock?.getValue(),
            safetyStockNeeds = safetyStockNeeds?.getValue(),
            poDueIn = maxPurchaseOrderDueIn?.toLong(),
            netNeeds = netNeeds.getValue(),
            unusableStockDetails = unusableInventory
                ?.map { calculationInventory ->
                    ForecastInventory(
                        calculationInventory.qty.getValue(),
                        calculationInventory.expiryDate,
                        calculationInventory.locationType,
                    )
                },
        )
    }
}
