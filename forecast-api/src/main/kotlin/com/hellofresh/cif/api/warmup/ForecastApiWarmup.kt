package com.hellofresh.cif.api.warmup

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.api.HTTP_PORT
import com.hellofresh.cif.api.calculation.generated.model.CountryDcConfigurationResponse
import com.hellofresh.cif.api.calculation.generated.model.WeeklyCalculationsResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.jwtToken
import com.hellofresh.cif.api.po.PURCHASE_ORDER_PATH
import com.hellofresh.cif.checks.Warmup
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.lib.recordSuspended
import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.request.accept
import io.ktor.client.request.bearerAuth
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType.Application
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Timer
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging

private const val WARMUP_COUNTRY = "ES"
private const val WARMUP_MAX_DCS_IN_COUNTRY = 2
private const val WARMUP_MAX_SKUS = 5
private const val WARMUP_WEEKS_IN_FUTURE = 4L

private const val DC_CODE_PARAM = "dcCode"
private const val WEEKS_PARAM = "weeks"
private const val SKU_ID_PARAM = "skuId"
private const val HTTP_CLIENT_REQUEST_TIMEOUT = 30000L

private const val EMAIL_ADDRESS = "<EMAIL>"

class ForecastApiWarmup(
    private var jwtCredentials: JwtCredentials,
    meterRegistry: MeterRegistry,
    private val httpClient: HttpClient = buildHttpClient()
) : Warmup {

    private val warmUpDuration: Timer = Timer.builder("warmup")
        .description("Record the elapsed time of warmup")
        .register(meterRegistry)

    override suspend fun run() {
        recordSuspended(warmUpDuration) {
            val dcs = getDcs()
            val weeklyCalculations = getWeeklyCalculations(dcs.take(WARMUP_MAX_DCS_IN_COUNTRY).toSet())

            weeklyCalculations.calculations
                .groupBy({ it.skuId }) { Pair(it.dcCode, DcWeek(it.week)) }
                .entries.take(WARMUP_MAX_SKUS)
                .forEach { (skuId, dcCodesAndWeeks) ->
                    val dcCodes = dcCodesAndWeeks.map { it.first }.toSet()
                    val dcWeeks = dcCodesAndWeeks.map { it.second }.toSet()
                    callPurchaseOrders(skuId, dcCodes, dcWeeks)
                    callNotes(dcCodes, dcWeeks, skuId)
                }
        }
    }

    private suspend fun getDcs(): Set<String> =
        get("/config/$WARMUP_COUNTRY")
            .let {
                objectMapper.readValue(
                    it.bodyAsText(),
                    object : TypeReference<Map<String, CountryDcConfigurationResponse>>() {}
                )
            }.keys

    private suspend fun getWeeklyCalculations(dcs: Set<String>): WeeklyCalculationsResponse {
        val params = Parameters.build {
            dcs.forEach { append(DC_CODE_PARAM, it) }
            (0L..WARMUP_WEEKS_IN_FUTURE).map {
                append(
                    WEEKS_PARAM,
                    DcWeek(
                        LocalDate.now(UTC).plusWeeks(it),
                        MONDAY,
                    ).toString(),
                )
            }
        }.formUrlEncode()

        return get("/calculation/weeklyView", params)
            .let {
                objectMapper.readValue(it.bodyAsText(), WeeklyCalculationsResponse::class.java)
            }
    }

    private suspend fun callNotes(dcCodes: Set<String>, dcWeeks: Set<DcWeek>, skuId: UUID) {
        val params = Parameters.build {
            append(SKU_ID_PARAM, skuId.toString())
            dcCodes.forEach { append(DC_CODE_PARAM, it) }
            dcWeeks.forEach { append(WEEKS_PARAM, it.toString()) }
        }.formUrlEncode()

        get("/notes", params)
    }

    private suspend fun callPurchaseOrders(skuId: UUID, dcCodes: Set<String>, dcWeeks: Set<DcWeek>) {
        val params = Parameters.build {
            append(SKU_ID_PARAM, skuId.toString())
            dcCodes.forEach { append(DC_CODE_PARAM, it) }
            dcWeeks.forEach { append(WEEKS_PARAM, it.toString()) }
        }.formUrlEncode()

        get(PURCHASE_ORDER_PATH, params)
    }

    private suspend fun get(path: String, params: String? = null): HttpResponse {
        val pathWithParam = path + (params?.let { "?$it" } ?: "")
        return httpClient.get(pathWithParam) {
            bearerAuth(forecastApiWarmupJwtToken())
        }
    }

    private fun forecastApiWarmupJwtToken() =
        jwtToken(
            secret = jwtCredentials.secret,
            issuer = "ForecastApi Warmup",
            userName = "forecast warmup",
            userEmail = EMAIL_ADDRESS,
        )

    companion object : Logging {

        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

        private fun buildHttpClient() = HttpClient(CIO) {
            expectSuccess = true
            install(HttpTimeout) {
                requestTimeoutMillis = HTTP_CLIENT_REQUEST_TIMEOUT
            }
            defaultRequest {
                accept(Application.Json)
                host = "localhost"
                port = HTTP_PORT
            }
        }
    }
}
