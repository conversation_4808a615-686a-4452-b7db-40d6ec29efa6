package com.hellofresh.cif.api.calculation.db

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.calculation.db.CalculationsQueryStep.CalculationInventoryRefreshMode
import com.hellofresh.cif.api.calculation.db.CalculationsQueryStep.Companion.resolveCalculationTable
import com.hellofresh.cif.api.configuration.Supplier
import com.hellofresh.cif.api.schema.Tables.PO_CALCULATIONS_VIEW
import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.api.schema.Tables.SUPPLIER_DETAILS_VIEW
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.lib.kafka.serde.objectMapper
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.JSONB
import org.jooq.Record7
import org.jooq.Result
import org.jooq.impl.DSL
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.select
import org.jooq.impl.SQLDataType

class CalculationsFilterQueryStep(
    private val dsl: MetricsDSLContext,
    dcCodes: List<String>,
    weeks: List<String>,
    calculationInventoryRefreshMode: CalculationInventoryRefreshMode,
) : CalculationConditionsStep {

    private val fetchCalculationFilters = "fetch-calculation-filters"

    private val calculationsTable = resolveCalculationTable(calculationInventoryRefreshMode)

    private val cskuIdColumn = calculationsTable.field(CSKU_ID, SQLDataType.UUID)!!
    private val dcCodeColumn = calculationsTable.field(DC_CODE, SQLDataType.CLOB.nullable(false))!!
    private val dateColumn = calculationsTable.field(DATE, SQLDataType.LOCALDATE.nullable(false))!!

    override val conditionsStep = CalculationsConditions(dcCodes, weeks, calculationInventoryRefreshMode)

    private val suppliersViewField = DSL.field(
        "jsonb_array_elements({0})",
        SQLDataType.JSONB,
        PO_CALCULATIONS_VIEW.SUPPLIER_NAMES,
    ).`as`("suppliers")
    private val poSuppliersView = select(
        PO_CALCULATIONS_VIEW.DC_CODE,
        PO_CALCULATIONS_VIEW.DATE,
        PO_CALCULATIONS_VIEW.SKU_ID,
        suppliersViewField,
    ).from(PO_CALCULATIONS_VIEW).asTable("poSuppliersView")
    private val poViewSkuIdColumn = poSuppliersView.field(PO_CALCULATIONS_VIEW.SKU_ID)!!
    private val poViewDcCodeColumn = poSuppliersView.field(PO_CALCULATIONS_VIEW.DC_CODE)!!
    private val poViewDateColumn = poSuppliersView.field(PO_CALCULATIONS_VIEW.DATE)!!

    private val jsonbSuppliersAgg = DSL.jsonbArrayAgg(suppliersViewField)
    private val jsonbActiveSuppliersAgg = DSL.jsonbArrayAgg(
        field("jsonb_build_object('name', {0})", SQLDataType.JSONB, SUPPLIER_DETAILS_VIEW.SUPPLIER_NAME),
    )

    private fun statement() = select(
        SKU_SPECIFICATION_VIEW.ID,
        SKU_SPECIFICATION_VIEW.CODE,
        SKU_SPECIFICATION_VIEW.NAME,
        SKU_SPECIFICATION_VIEW.CATEGORY,
        SKU_SPECIFICATION_VIEW.PACKAGING,
        jsonbSuppliersAgg.filterWhere(suppliersViewField.isNotNull),
        jsonbActiveSuppliersAgg.filterWhere(SUPPLIER_DETAILS_VIEW.SUPPLIER_NAME.isNotNull),
    ).from(calculationsTable)
        .join(SKU_SPECIFICATION_VIEW).on(cskuIdColumn.eq(SKU_SPECIFICATION_VIEW.ID))
        .leftJoin(poSuppliersView)
        .on(poViewDcCodeColumn.eq(dcCodeColumn), poViewDateColumn.eq(dateColumn), poViewSkuIdColumn.eq(cskuIdColumn))
        .leftJoin(SUPPLIER_DETAILS_VIEW)
        .on(SUPPLIER_DETAILS_VIEW.SKU_ID.eq(cskuIdColumn),)
        .where(conditionsStep.conditions)
        .groupBy(
            SKU_SPECIFICATION_VIEW.ID,
            SKU_SPECIFICATION_VIEW.CODE,
            SKU_SPECIFICATION_VIEW.NAME,
            SKU_SPECIFICATION_VIEW.CATEGORY,
            SKU_SPECIFICATION_VIEW.PACKAGING,
        )

    suspend fun fetch(): CalculationFiltersData =
        dsl.withTagName(fetchCalculationFilters)
            .fetchAsync(statement())
            .thenApply {
                toCalculationFiltersData(it)
            }.await()

    private fun toCalculationFiltersData(results: Result<Record7<UUID, String, String, String, String, JSONB, JSONB>>) =
        CalculationFiltersData(
            results.map {
                Sku(
                    it[SKU_SPECIFICATION_VIEW.ID],
                    it[SKU_SPECIFICATION_VIEW.CODE],
                    it[SKU_SPECIFICATION_VIEW.NAME],
                )
            }.toSet(),
            results.map { it[SKU_SPECIFICATION_VIEW.CATEGORY] }.toSet(),
            results.map { it[SKU_SPECIFICATION_VIEW.PACKAGING] }.toSet(),
            results.flatMap {
                it[jsonbSuppliersAgg]?.let { jsonB ->
                    objectMapper.readValue<Set<Supplier>?>(jsonB.data())
                } ?: emptySet()
            }.toSet(),
            activeSuppliers = results.flatMap {
                it[jsonbActiveSuppliersAgg]?.let { jsonB ->
                    objectMapper.readValue<Set<ActiveSupplier>?>(jsonB.data())
                } ?: emptySet()
            }.toSet(),
        )
}

data class CalculationFiltersData(
    val skuSet: Set<Sku>,
    val ingredientCategories: Set<String>,
    val locationInBox: Set<String>,
    val suppliers: Set<Supplier>,
    val activeSuppliers: Set<ActiveSupplier>,
)

data class Sku(
    val skuId: UUID,
    val skuCode: String,
    val skuName: String,
)

data class Supplier(
    val id: UUID,
    val name: String
)

data class ActiveSupplier(
    val name: String
)
