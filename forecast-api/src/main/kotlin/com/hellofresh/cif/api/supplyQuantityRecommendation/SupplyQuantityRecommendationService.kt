package com.hellofresh.cif.api.supplyQuantityRecommendation

import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SQRData
import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SQRState
import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SQRState.PENDING
import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SQRState.PROCESSED
import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SkuDetail
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.StockUpdatesReadRepository
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.SupplyQuantityRecommendation
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.SupplyQuantityRecommendationRepository
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.sumOf
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.SafetyStockConfiguration
import com.hellofresh.cif.safetystock.repository.SafetyStockConfigurationRepository
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationConfigRepository
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging

class SupplyQuantityRecommendationService(
    private val supplyQuantityRecommendationRepository: SupplyQuantityRecommendationRepository,
    private val stockUpdatesReadRepository: StockUpdatesReadRepository,
    private val supplyQuantityRecommendationConfigRepository: SupplyQuantityRecommendationConfigRepository,
    private val safetyStockRepository: SafetyStockRepository,
    private val safetyStockConfigurationRepository: SafetyStockConfigurationRepository,
    private val dcConfigService: DcConfigService
) {

    suspend fun getSupplyQuantityRecommendations(dcCode: String, week: String): SQRData {
        logger.info("get supply quantity recommendation using dcCode = $dcCode and week = $week")
        val distributionCenter = requireNotNull(
            dcConfigService.dcConfigurations[dcCode],
        ) { "Dc Configuration not found for $dcCode" }

        val sqRecommendations = supplyQuantityRecommendationRepository.fetchSupplyQuantityRecommendations(dcCode, week)
        val stockUpdatesMap = getStockUpdates(distributionCenter, week)
        val sqrConfigurations = supplyQuantityRecommendationConfigRepository.fetchSupplyQuantityRecommendationConfigurations(
            week,
            distributionCenter,
        )
        val safetyStockBySku = safetyStockRepository.fetchSafetyStock(
            week,
            distributionCenter.dcCode,
        ).associateBy { it.skuId }
        val safetyStockConfigurations = safetyStockConfigurationRepository.fetchSafetyStockConfigurations(
            week,
            distributionCenter,
        )

        val sqrSkus = sqRecommendations.map { sqr ->
            val stockUpdateQuantity = stockUpdatesMap[StockUpdateKey(dcCode, week, sqr.skuId)]
            val productionWeek = ProductionWeek(week, distributionCenter.productionStart, distributionCenter.zoneId)
            val sqrConfiguration = sqrConfigurations.getConfiguration(dcCode, sqr.skuId, productionWeek)
            val safetyStock = safetyStockBySku[sqr.skuId]
            val safetyStockConfiguration = safetyStockConfigurations.getConfiguration(dcCode, sqr.skuId, productionWeek)

            SkuDetail(
                skuId = sqr.skuId,
                skuCode = sqr.skuCode,
                skuName = sqr.skuName,
                skuCategory = sqr.skuCategory,
                uom = sqr.uom,
                supplyQuantityRecommendation = sqr.supplyQuantityRecommendation,
                inventoryRollover = sqr.inventoryRollover,
                demand = sqr.demand,
                stockUpdates = stockUpdateQuantity?.getValue(),
                safetyStock = sqr.safetyStock,
                safetyStockRiskMultiplier = safetyStockConfiguration.riskMultiplier,
                recommendationEnabled = sqrConfiguration.recommendationEnabled,
                multiWeekEnabled = sqrConfiguration.multiWeekEnabled,
                state = calculateState(safetyStock, safetyStockConfiguration, sqr),
                updatedAt = sqr.updatedAt,
                skuRiskRating = safetyStockConfiguration.skuRiskRating,
                bufferPercentage = safetyStock?.configuration?.bufferPercentage
            )
        }

        return SQRData(
            dcCode = dcCode,
            week = week,
            skus = sqrSkus,
        )
    }

    private fun calculateState(
        safetyStock: SafetyStock?,
        safetyStockConfiguration: SafetyStockConfiguration,
        sqr: SupplyQuantityRecommendation
    ): SQRState {
        val isSafetyStockChanged = sqr.safetyStock?.toLong() != safetyStock?.value
        val isSafetyStockRiskMultiplier = safetyStock != null && safetyStockConfiguration.riskMultiplier != safetyStock.configuration.riskMultiplier
        val isSafetyStockSluRiskRating = safetyStock != null && safetyStockConfiguration.skuRiskRating != safetyStock.configuration.skuRiskRating
        return if (isSafetyStockChanged || isSafetyStockRiskMultiplier || isSafetyStockSluRiskRating) {
            PENDING
        } else {
            PROCESSED
        }
    }

    private suspend fun getStockUpdates(dc: DistributionCenterConfiguration, week: String): Map<StockUpdateKey, SkuQuantity> {
        val dateRange = calculateStockUpdateDateRangeForWeek(dc, week)

        val stockUpdates = stockUpdatesReadRepository.fetchStockUpdates(dc.dcCode, dateRange)

        return stockUpdates
            .groupBy { it.skuId }
            .map { (skuId, updates) ->
                StockUpdateKey(dc.dcCode, week, skuId) to updates.sumOf { it.quantity }
            }.toMap()
    }

    private fun calculateStockUpdateDateRangeForWeek(dc: DistributionCenterConfiguration, week: String): DateRange {
        val productionWeek = ProductionWeek(week, dc.productionStart, dc.zoneId)
        val startProductionDate = productionWeek.dcWeek.getStartDateInDcWeek(dc.productionStart, dc.zoneId)
        val lastProductionDate = productionWeek.dcWeek.getLastDateInDcWeek(dc.productionStart, dc.zoneId)

        return if (dc.hasCleardown) {
            val latestCleardown = dc.getLatestCleardown()
            if (lastProductionDate.isBefore(latestCleardown)) {
                val previousWeekCleardown = startProductionDate.with(TemporalAdjusters.previousOrSame(dc.cleardown))
                val nextWeekCleardown = lastProductionDate.with(TemporalAdjusters.previousOrSame(dc.cleardown))

                DateRange(previousWeekCleardown, nextWeekCleardown)
            } else {
                DateRange(latestCleardown, lastProductionDate)
            }
        } else {
            DateRange(startProductionDate, lastProductionDate)
        }
    }

    companion object : Logging
}

internal data class StockUpdateKey(
    val dcCode: String,
    val week: String,
    val skuId: UUID,
)
