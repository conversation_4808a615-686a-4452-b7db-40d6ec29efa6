package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.CalculationServiceMapper.toDailyCalculation
import com.hellofresh.cif.api.calculation.SortBy.SKU_CODE
import com.hellofresh.cif.api.calculation.SortBy.SKU_NAME
import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.mockk
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.math.BigDecimal.TWO
import java.math.BigDecimal.ZERO
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.ZoneId
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertContentEquals
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertIterableEquals
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class CalculationServiceWeeklyViewTest : AbstractCalculationServiceViewsTest() {
    private var service: CalculationsService =
        CalculationsService(
            SimpleMeterRegistry(),
            dcConfigService,
            usableInventoryEvaluatorMock,
            calculationsPendingStockUpdateServiceMock,
            prodCalculationRepositoryMock,
            mockk(),
            statsigFeatureFlagClient,
        )

    @Test
    fun `cannot aggregate weekly view based on empty daily view set`() {
        assertThrows<IllegalArgumentException> {
            WeeklyView.fromDaily(emptySet())
        }
    }

    @Test
    fun `should aggregate the daily view for two SKUs by week`() {
        // given
        val sku1 = Sku(
            skuId = UUID.randomUUID(),
            skuCode = "ABC-00-00000-0",
            skuCategories = "ABC",
            skuName = "aSkuName",
        )
        val sku2 = Sku(
            skuId = UUID.randomUUID(),
            skuCode = "ABC-00-00000-1",
            skuCategories = "XYZ",
            skuName = "aSkuName2",
        )

        val today = LocalDate.now()
        val dcWeek = DcWeek(today, dcBV.productionStart).value
        val dailyCalculations = listOf(
            givenCalculation(dcCodeBV, sku1, dcWeek, today).copy(
                expectedInbound = TWO,
                actualInbound = TWO,
                openingStock = ONE,
                expired = TWO,
                demanded = TWO,
                dailyNeeds = TEN,
                closingStock = ZERO,
                actualConsumption = TEN,
                dcCode = dcCodeBV,
                skuAtRisk = false,
                safetyStock = null,
                safetyStockNeeds = null,
                storageStock = ZERO,
                stagingStock = ZERO,
                poDueIn = null,
                netNeeds = TEN,
            ),
            givenCalculation(dcCodeBV, sku2, dcWeek, today),
            givenCalculation(dcCodeBV, sku1, dcWeek, today.plusDays(1)),
        )

        val request = givenRequest(dcWeek, dcCodeBV)
        mockRepository(request, dailyCalculations)

        // when
        val result = runBlocking { service.getWeeklyCalculations(request) }

        // then
        val expectedResult = dailyCalculations.map {
            toDailyCalculation(it, usableInventoryEvaluatorMock)
        }.groupBy { it.sku }
            .map { weeklyView(dcWeek, it.key, it.value) }
            .map { view ->
                // opening stock is the opening stock of the first day in the weeklyView
                val opening = when (view.sku) {
                    sku1 -> ONE
                    sku2 -> ZERO
                    else -> error("Non reachable state")
                }
                // closing stock is the closing stock of the last day in the weeklyView
                val closing = when (view.sku) {
                    sku1 -> TEN
                    sku2 -> TEN
                    else -> error("Non reachable state")
                }

                val netNeeds = when (view.sku) {
                    sku1 -> ONE
                    sku2 -> TEN
                    else -> error("Non reachable state")
                }

                view.copy(
                    calculation = view.calculation.copy(
                        usableStock = opening,
                        closingStock = closing,
                        storageStock = ONE.negate(),
                        netNeeds = netNeeds,
                    ),
                )
            }

        assertIterableEquals(expectedResult, result.calculationPage)
    }

    @Test
    fun `should aggregate same pos for weekly view`() {
        // given
        val dcWeek = "2022-W1"
        val sku1 = Sku(
            skuId = UUID.randomUUID(),
            skuCode = "ABC-00-00000-0",
            skuCategories = "ABC",
            skuName = "aSkuName",
        )
        val today = LocalDate.now()

        val po = UUID.randomUUID().toString()
        val po2 = po
        val po3 = UUID.randomUUID().toString()
        val dailyCalculations = listOf(
            givenCalculation(dcCodeVE, sku1, dcWeek, today).copy(
                expectedInboundPo = setOf(po),
            ),

            givenCalculation(dcCodeVE, sku1, dcWeek, today.plusDays(1)).copy(
                expectedInboundPo = setOf(po2, po3),
            ),
        )

        val request = givenRequest(dcWeek, dcCodeVE)
        mockRepository(request, dailyCalculations)

        // when
        val result = runBlocking { service.getWeeklyCalculations(request) }

        // then
        with(result.calculationPage.first().calculation.pos) {
            assertEquals(2, size)
            assertTrue(contains(po))
            assertTrue(contains(po3))
        }
    }

    @Test
    fun `usable stock should be the opening usable for the first day of the week`() {
        // given
        val week = "2022-W01"
        val today = LocalDate.now()
        val tomorrow = today.plusDays(1)

        val sku = Sku(
            skuId = UUID.randomUUID(),
            skuCode = "ABC-00-00000-0",
            skuCategories = "ABC",
            skuName = "aSkuName",
        )

        val todayCalculation = givenCalculation(dcCodeVE, sku, week, today)
        val tomorrowCalculation = todayCalculation
            .copy(
                productionWeek = week,
                date = tomorrow,
                openingStock = todayCalculation.openingStock + TEN,
            )
        assertTrue(todayCalculation.openingStock != tomorrowCalculation.openingStock)

        val request = givenRequest(week, dcCodeVE)
        mockRepository(request, listOf(todayCalculation, tomorrowCalculation))

        // when
        val result = runBlocking { service.getWeeklyCalculations(request) }
        val openingUsableStock = todayCalculation.openingStock
        assertEquals(
            openingUsableStock,
            result.calculationPage.first().calculation.usableStock,
        )
    }

    @Test
    fun `return empty weekly view when there is no daily calculations`() {
        // given
        val request = givenRequest("2022-W01")
        mockRepository(request, emptyList())

        // when
        val result = runBlocking { service.getWeeklyCalculations(request) }

        // then
        assertTrue(result.calculationPage.isEmpty())
    }

    @Test
    fun `should return all weekly views sorted by skuCode`() {
        val week = "2022-W1"
        val skus = ('F' downTo 'A').map {
            Sku(
                skuId = UUID.randomUUID(),
                skuCode = "${it}00-00-00000-0",
                skuCategories = "ABC",
                skuName = "SkuName",
            )
        }
        val calculationRecords = skus.map { givenCalculation(dcCodeBV, it, week, LocalDate.now()) }
        val requestSortByCode = givenRequest(week, dcCodeBV).copy(sortBy = SKU_CODE)
        mockRepository(requestSortByCode, calculationRecords)

        // when
        val result = runBlocking {
            service.getWeeklyCalculations(requestSortByCode).calculationPage
        }

        // then
        assertContentEquals(calculationRecords.sortedBy { it.code }.map { it.code }, result.map { it.sku.skuCode })
    }

    @Test
    fun `should return all weekly views sorted by skuName`() {
        val week = "2022-W1"
        val skus = ('F' downTo 'A').map {
            Sku(
                skuId = UUID.randomUUID(),
                skuCode = "000-00-00000-0",
                skuCategories = "ABC",
                skuName = "$it SkuName",
            )
        }
        val calculationRecords = skus.map { givenCalculation(dcCodeBV, it, week, LocalDate.now()) }
        val requestSortByName = givenRequest(week, dcCodeBV).copy(sortBy = SKU_NAME)
        mockRepository(requestSortByName, calculationRecords)

        // when
        val result = runBlocking {
            service.getWeeklyCalculations(requestSortByName).calculationPage
        }

        // then
        assertContentEquals(
            calculationRecords.map {
                toDailyCalculation(it, usableInventoryEvaluatorMock)
            }
                .groupBy { it.sku }.map {
                    weeklyView(week, it.key, it.value)
                }.sortedBy { it.sku.skuName }.map { it.sku },
            result.map { it.sku },
        )
    }

    @ParameterizedTest
    @EnumSource(SortBy::class)
    fun `weekly calculations are sorted by default by sku at risk and then selected sortBy`(sortBy: SortBy) {
        // given
        val givenWeek = DcWeek("2022-W01")
        val day = givenWeek.getLastDateInDcWeek(FRIDAY, ZoneId.of("UTC"))
        val skus = { sortByParam: SortBy, value: String ->
            when (sortByParam) {
                SKU_CODE -> Sku(
                    skuId = UUID.randomUUID(),
                    skuCode = value,
                    skuName = UUID.randomUUID().toString(),
                    skuCategories = "ABC",
                )

                SKU_NAME -> Sku(
                    skuId = UUID.randomUUID(),
                    skuCode = UUID.randomUUID().toString(),
                    skuName = value,
                    skuCategories = "ABC",
                )
            }
        }
        val skusAtRisk = ('C'..'D').map { skus(sortBy, "$it") }
            .map { sku ->
                val dailyView = givenCalculation(dcCodeVE, sku, givenWeek.toString(), day)
                dailyView.copy(skuAtRisk = true)
            }

        val skusNotAtRisk = ('A'..'B').map { skus(sortBy, "$it") }
            .map { sku ->
                val dailyView = givenCalculation(dcCodeVE, sku, givenWeek.toString(), day)
                dailyView.copy(skuAtRisk = false)
            }

        val requestSortByName = givenRequest(givenWeek.toString(), dcCodeVE).copy(sortBy = sortBy)
        mockRepository(requestSortByName, (skusAtRisk + skusNotAtRisk).shuffled())

        // when
        val result = runBlocking {
            service.getWeeklyCalculations(requestSortByName).calculationPage
        }

        // then
        assertEquals(
            listOf("C", "D", "A", "B"),
            result.map {
                when (sortBy) {
                    SKU_NAME -> it.sku.skuName
                    SKU_CODE -> it.sku.skuCode
                }
            },
        )
    }

    @Test
    fun `should aggregate safetyStock and safetyStockRequirement for the week`() {
        // given
        val dcWeek = "2022-W01"
        val today = LocalDate.now()

        val sku = Sku(
            skuId = UUID.randomUUID(),
            skuCode = "ABC-00-00000-0",
            skuCategories = "ABC",
            skuName = "aSkuName",
        )
        val safetyStock = BigDecimal(11)
        val safetyStockNeedsMonday = BigDecimal(21)
        val dailyCalculations = (0..6).map {
            val productionDay = today.plusDays(it.toLong())
            val safetyStockNeeds = if (productionDay.dayOfWeek == MONDAY) safetyStockNeedsMonday else ONE.negate()
            val dv = givenCalculation(dcCodeBV, sku, dcWeek, productionDay)
            dv.copy(safetyStock = safetyStock, safetyStockNeeds = safetyStockNeeds)
        }
        val request = givenRequest(dcWeek, dcCodeBV)
        mockRepository(request, dailyCalculations)

        // when
        val result = runBlocking { service.getWeeklyCalculations(request) }

        // then
        assertEquals(safetyStock, result.calculationPage.first().calculation.safetyStock)
        assertEquals(safetyStockNeedsMonday, result.calculationPage.first().calculation.safetyStockNeeds)
    }
}
