package com.hellofresh.cif.api.user

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import io.ktor.server.auth.jwt.JWTPrincipal
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

internal class LoggedInUserInfoTest {

    @Test
    fun `create LoggedIn User from JWT Token`() {
        val subject = UUID.randomUUID().toString()
        val email = UUID.randomUUID().toString()
        val name = UUID.randomUUID().toString()

        val jwt = JWT.create()
            .withSubject(subject)
            .withClaim("email", email)
            .withClaim("metadata", mapOf("name" to name))
            .sign(Algorithm.HMAC256("testSecret"))
            .let(JWT::decode)
            .let(::JWTPrincipal)

        val loggedInUser = jwt.getLoggedInUser()
        assertEquals(subject, loggedInUser.userId)
        assertEquals(email, loggedInUser.userEmail)
        assertEquals(name, loggedInUser.userName)
    }

    @Test
    fun `create LoggedIn User from JWT Token with no name`() {
        val subject = UUID.randomUUID().toString()
        val email = UUID.randomUUID().toString()

        val jwt = JWT.create()
            .withSubject(subject)
            .withClaim("email", email)
            .sign(Algorithm.HMAC256("testSecret"))
            .let(JWT::decode)
            .let(::JWTPrincipal)

        val loggedInUser = jwt.getLoggedInUser()
        assertEquals(subject, loggedInUser.userId)
        assertEquals(email, loggedInUser.userEmail)
        assertNull(loggedInUser.userName)
    }

    @Test
    fun `create LoggedIn User fails when no email exists from JWT Token`() {
        val subject = UUID.randomUUID().toString()

        val jwt = JWT.create()
            .withSubject(subject)
            .sign(Algorithm.HMAC256("testSecret"))
            .let(JWT::decode)
            .let(::JWTPrincipal)

        assertThrows<IllegalArgumentException> { jwt.getLoggedInUser() }
    }

    @Test
    fun `create logged in user from Azure jwt token`() {
        val subject = UUID.randomUUID().toString()
        val email = UUID.randomUUID().toString()
        val name = UUID.randomUUID().toString()

        val jwt: JWTPrincipal = JWT.create()
            .withSubject(subject)
            .withClaim("roleclaim", listOf("azure_role"))
            .withClaim("email", email)
            .withClaim("name", name)
            .sign(Algorithm.HMAC256("testSecret"))
            .let(JWT::decode)
            .let(::JWTPrincipal)

        val loggedInUser = jwt.getLoggedInUser()
        assertEquals(subject, loggedInUser.userId)
        assertEquals(email, loggedInUser.userEmail)
        assertEquals(name, loggedInUser.userName)
    }

    @Test
    fun `test isSupperUser true when roleclaim exists`() {
        val subject = UUID.randomUUID().toString()
        val email = UUID.randomUUID().toString()
        val name = UUID.randomUUID().toString()

        val jwt: JWTPrincipal = JWT.create()
            .withSubject(subject)
            .withClaim("roleclaim", listOf("siv.de.all.recommendation_manager"))
            .withClaim("email", email)
            .withClaim("name", name)
            .sign(Algorithm.HMAC256("testSecret"))
            .let(JWT::decode)
            .let(::JWTPrincipal)

        val loggedInUser = jwt.getLoggedInUser()

        assertEquals(subject, loggedInUser.userId)
        assertEquals(email, loggedInUser.userEmail)
        assertEquals(name, loggedInUser.userName)
        assertEquals(true, loggedInUser.roleClaim.isSuperManager())
    }

    @Test
    fun `test isSupperUser false when roleclaim dont have recommendation_manager`() {
        val subject = UUID.randomUUID().toString()
        val email = UUID.randomUUID().toString()
        val name = UUID.randomUUID().toString()

        val jwt: JWTPrincipal = JWT.create()
            .withSubject(subject)
            .withClaim("roleclaim", listOf("siv.de.all.viewer"))
            .withClaim("email", email)
            .withClaim("name", name)
            .sign(Algorithm.HMAC256("testSecret"))
            .let(JWT::decode)
            .let(::JWTPrincipal)

        val loggedInUser = jwt.getLoggedInUser()

        assertEquals(subject, loggedInUser.userId)
        assertEquals(email, loggedInUser.userEmail)
        assertEquals(name, loggedInUser.userName)
        assertEquals(false, loggedInUser.roleClaim.isSuperManager())
    }

    @Test
    fun `test isSupperUser is false when roleclaim do not exists`() {
        val subject = UUID.randomUUID().toString()
        val email = UUID.randomUUID().toString()
        val name = UUID.randomUUID().toString()

        val jwt: JWTPrincipal = JWT.create()
            .withSubject(subject)
            .withClaim("email", email)
            .withClaim("metadata", mapOf("name" to name))
            .sign(Algorithm.HMAC256("testSecret"))
            .let(JWT::decode)
            .let(::JWTPrincipal)

        val loggedInUser = jwt.getLoggedInUser()

        assertEquals(subject, loggedInUser.userId)
        assertEquals(email, loggedInUser.userEmail)
        assertEquals(name, loggedInUser.userName)
        assertEquals(false, loggedInUser.roleClaim.isSuperManager())
    }
}
