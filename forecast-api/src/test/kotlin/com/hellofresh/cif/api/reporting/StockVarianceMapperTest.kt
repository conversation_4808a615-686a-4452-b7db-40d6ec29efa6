package com.hellofresh.cif.api.reporting

import com.hellofresh.cif.api.calculation.generated.model.InboundVarianceValueResponse
import com.hellofresh.cif.api.configuration.Sku
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.StockInboundVarianceSku
import com.hellofresh.inventory.models.variance.DailyInventoryVarianceData
import java.math.BigDecimal
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class StockVarianceMapperTest {

    @Test
    fun `should correctly map stock variances to response`() {
        // given
        val numVariances = 2
        val previousCleardownWeek = DcWeek("2022-W33")
        val currentCleardownWeek = DcWeek("2022-W34")
        val stockVariances = (0L..numVariances).map {
            val sku = Sku("Sku Code$it", UUID.randomUUID(), "Sku Name$it")
            StockVariance(
                sku,
                "category $it",
                MONDAY,
                currentCleardownWeek,
                previousCleardownWeek,
                it,
                it + 1,
                it + 2,
                it + 3,
                it + 4,
                it + 5,
            )
        }

        // when
        val dcCode = "VE"
        val mappedResponse = mapStockVarianceResponse(dcCode, stockVariances)
        assertEquals(dcCode, mappedResponse.dc.code)

        // then
        assertEquals(stockVariances.size, mappedResponse.skus.size)
        mappedResponse.skus.forEachIndexed { index, skuReport ->
            val stockVariance = stockVariances[index]
            assertEquals(stockVariance.cleardownDay.name, mappedResponse.dc.cleardown?.value)
            assertEquals(previousCleardownWeek, stockVariance.previousCleardownWeek)
            assertEquals(currentCleardownWeek, stockVariance.currentCleardownWeek)
            assertEquals(stockVariance.sku.skuId, skuReport.sku.skuId)
            assertEquals(stockVariance.sku.skuCode, skuReport.sku.skuCode)
            assertEquals(stockVariance.sku.skuName, skuReport.sku.skuName)
            assertEquals(stockVariance.calculatedConsumption, skuReport.consumption.calculated)
            assertEquals(stockVariance.missing, skuReport.consumption.missing)
            assertEquals(stockVariance.weekConsumption, skuReport.consumption.theoretical)
            assertEquals(stockVariance.weekActualInbound, skuReport.inbound)
            assertEquals(stockVariance.openingStockLatestCleardown, skuReport.stock.closing)
            assertEquals(stockVariance.openingStockPreviousCleardown, skuReport.stock.opening)
            assertEquals(stockVariance.weekUnusable, skuReport.stock.waste)
            assertEquals(stockVariance.skuCategory, skuReport.skuCategories)
        }
    }

    @Test
    fun `should map empty variance list to empty skus API response`() {
        // when
        val mappedResponse = mapStockVarianceResponse("VE", emptyList())

        // then
        assertTrue(mappedResponse.skus.isEmpty())
    }

    @Test
    fun `should map a live variance report`() {
        val expectedTotalCleardownVariancePercent = 11L
        val expectedTotalLiveCleardownVariancePercent = 11L
        val givenLiveVariance =
            DailyInventoryVarianceData(
                LocalDate.now(ZoneOffset.UTC),
                SkuQuantity.fromLong(1),
                SkuQuantity.fromLong(2),
                SkuQuantity.fromLong(3)
            )

        val givenSkuOne = StockLiveVarianceSku(
            UUID.randomUUID(),
            "cod",
            "name",
            "PHF",
            19,
            17,
            listOf(givenLiveVariance),
        )
        val givenSkuTwo = StockLiveVarianceSku(
            UUID.randomUUID(),
            "cod",
            "name",
            "PHF",
            2,
            4,
            listOf(givenLiveVariance),
        )

        val result = mapLiveStockVarianceResponse(listOf(givenSkuOne, givenSkuTwo))

        assertEquals(2, result.stockLiveVariances.size)
        with(result.stockLiveVariances.first { it.skuId == givenSkuOne.skuId }) {
            assertEquals(givenSkuOne.skuCode, skuCode)
            assertEquals(givenSkuOne.skuName, skuName)
            assertEquals(givenSkuOne.skuCategories, skuCategories)
            assertEquals(givenSkuOne.cleardownVariance, productionCleardownVariancePercent)
            assertEquals(givenSkuOne.liveVariance, liveCleardownVariancePercent)
            assertEquals(1, data.size)
            assertEquals(givenLiveVariance.cleardownClosingStock, SkuQuantity.fromBigDecimal(data.first().closingStock))
            assertEquals(givenLiveVariance.liveClosingStock, SkuQuantity.fromBigDecimal(data.first().liveClosingStock))
            assertEquals(
                givenLiveVariance.inventoryQty,
                data.first().inventoryQty?.let { SkuQuantity.fromBigDecimal(it) }
            )
        }

        assertEquals(
            expectedTotalCleardownVariancePercent,
            result.totalCleardownVariancePercent.productionCleardownVariancePercent,
        )
        assertEquals(
            expectedTotalLiveCleardownVariancePercent,
            result.totalCleardownVariancePercent.liveCleardownVariancePercent,
        )
    }

    @Test
    fun `should return variances round half up`() {
        val givenLiveVariance =
            DailyInventoryVarianceData(
                LocalDate.now(ZoneOffset.UTC),
                SkuQuantity.fromLong(1),
                SkuQuantity.fromLong(2),
                SkuQuantity.fromLong(3)
            )
        val givenSkuOne = StockLiveVarianceSku(
            UUID.randomUUID(),
            "cod",
            "name",
            "PHF",
            0,
            1,
            listOf(givenLiveVariance),
        )
        val givenSkuTwo = StockLiveVarianceSku(
            UUID.randomUUID(),
            "cod",
            "name",
            "PHF",
            1,
            0,
            listOf(givenLiveVariance),
        )

        val result = mapLiveStockVarianceResponse(listOf(givenSkuOne, givenSkuTwo))

        assertEquals(2, result.stockLiveVariances.size)
        assertEquals(
            BigDecimal.ONE,
            result.totalCleardownVariancePercent.productionCleardownVariancePercent.toBigDecimal(),
        )
        assertEquals(
            BigDecimal.ONE,
            result.totalCleardownVariancePercent.liveCleardownVariancePercent.toBigDecimal(),
        )
    }

    @Test
    fun `should return total correct variance percent if the sku variance percent is more than 100 percent`() {
        val expectedHundred = 100L
        val givenLiveVariance =
            DailyInventoryVarianceData(
                LocalDate.now(ZoneOffset.UTC),
                SkuQuantity.fromLong(1),
                SkuQuantity.fromLong(2),
                SkuQuantity.fromLong(3)
            )
        val givenSkuOne = StockLiveVarianceSku(
            UUID.randomUUID(),
            "cod",
            "name",
            "PHF",
            1300,
            590,
            listOf(givenLiveVariance),
        )

        val givenSkuTwo = StockLiveVarianceSku(
            UUID.randomUUID(),
            "cod",
            "name",
            "PHF",
            12,
            37,
            listOf(givenLiveVariance),
        )

        val result = mapLiveStockVarianceResponse(listOf(givenSkuOne, givenSkuTwo))

        assertEquals(2, result.stockLiveVariances.size)
        assertEquals(
            expectedHundred,
            result.totalCleardownVariancePercent.productionCleardownVariancePercent,
        )
        assertEquals(
            expectedHundred,
            result.totalCleardownVariancePercent.liveCleardownVariancePercent,
        )
    }

    @Test
    fun `should map empty inbound variance list to empty skus API response`() {
        // when
        val mappedResponse = mapInboundVarianceResponse(emptyList())

        // then
        assertTrue(mappedResponse.inboundVariances.isEmpty())
    }

    @Test
    fun `should map a inbound variance report`() {
        val expectedPoVsInboundAverage = 2L
        val expectedAsnVsInboundAverage = 7L

        val givenSkuOne = StockInboundVarianceSku(
            poRef = "2330IT239939_O2",
            asnId = "ASN133760",
            supplierName = "test-supplier-name",
            skuId = UUID.randomUUID(),
            skuName = "Chicken meatballs / Polpette di pollo 240g",
            skuCode = "PTN-00-121215-4",
            skuCategory = "PTN",
            poVsInbound = SkuQuantity.fromLong(1),
            asnVsInbound = SkuQuantity.fromLong(5),
        )

        val givenSkuTwo = StockInboundVarianceSku(
            poRef = "2330IT239939_O2",
            asnId = "ASN133760",
            supplierName = "test-supplier-name",
            skuId = UUID.randomUUID(),
            skuName = "Chicken meatballs / Polpette di pollo 540g",
            skuCode = "PTN-00-121215-5",
            skuCategory = "PTN",
            poVsInbound = SkuQuantity.fromLong(2),
            asnVsInbound = SkuQuantity.fromLong(9),
        )

        val result = mapInboundVarianceResponse(listOf(givenSkuOne, givenSkuTwo))

        assertEquals(2, result.inboundVariances.size)

        val actualSkuOne = result.inboundVariances.first { it.skuId == givenSkuOne.skuId }
        assertStockInboundVarianceSku(givenSkuOne, actualSkuOne)
        val actualSkuTwo = result.inboundVariances.first { it.skuId == givenSkuTwo.skuId }
        assertStockInboundVarianceSku(givenSkuTwo, actualSkuTwo)

        assertEquals(
            expectedPoVsInboundAverage,
            result.poVsInboundAverage,
        )
        assertEquals(
            expectedAsnVsInboundAverage,
            result.asnVsInboundAverage,
        )
    }

    @Test
    fun `should map a inbound variance report when poVsInbound , asnVsInbound has null variances`() {
        val expectedPoVsInboundAverage = 2L
        val expectedAsnVsInboundAverage = 7L

        val givenSkuOne = StockInboundVarianceSku(
            poRef = "2330IT239939_O2",
            asnId = "ASN133760",
            supplierName = "test-supplier-name",
            skuId = UUID.randomUUID(),
            skuName = "Chicken meatballs / Polpette di pollo 240g",
            skuCode = "PTN-00-121215-4",
            skuCategory = "PTN",
            poVsInbound = SkuQuantity.fromLong(1),
            asnVsInbound = SkuQuantity.fromLong(5),
        )

        val givenSkuTwo = StockInboundVarianceSku(
            poRef = "2330IT239939_O2",
            asnId = "ASN133760",
            supplierName = "test-supplier-name",
            skuId = UUID.randomUUID(),
            skuName = "Chicken meatballs / Polpette di pollo 540g",
            skuCode = "PTN-00-121215-5",
            skuCategory = "PTN",
            poVsInbound = SkuQuantity.fromLong(2),
            asnVsInbound = SkuQuantity.fromLong(9),
        )

        val givenSkuThree = StockInboundVarianceSku(
            poRef = "2330IT239939_O2",
            asnId = "ASN133760",
            supplierName = "test-supplier-name",
            skuId = UUID.randomUUID(),
            skuName = "Chicken meatballs / Polpette di pollo 540g",
            skuCode = "PTN-00-121215-5",
            skuCategory = "PTN",
            poVsInbound = null,
            asnVsInbound = null,
        )

        val result = mapInboundVarianceResponse(listOf(givenSkuOne, givenSkuTwo, givenSkuThree))

        assertEquals(3, result.inboundVariances.size)

        val actualSkuOne = result.inboundVariances.first { it.skuId == givenSkuOne.skuId }
        assertStockInboundVarianceSku(givenSkuOne, actualSkuOne)
        val actualSkuTwo = result.inboundVariances.first { it.skuId == givenSkuTwo.skuId }
        assertStockInboundVarianceSku(givenSkuTwo, actualSkuTwo)

        assertEquals(
            expectedPoVsInboundAverage,
            result.poVsInboundAverage,
        )
        assertEquals(
            expectedAsnVsInboundAverage,
            result.asnVsInboundAverage,
        )
    }

    private fun assertStockInboundVarianceSku(expected: StockInboundVarianceSku, actual: InboundVarianceValueResponse) {
        assertEquals(expected.poRef, actual.poRef)
        assertEquals(expected.asnId, actual.asnId)
        assertEquals(expected.skuCode, actual.skuCode)
        assertEquals(expected.skuName, actual.skuName)
        assertEquals(expected.skuCategory, actual.skuCategories)
        assertEquals(expected.poVsInbound?.getValue()?.toLong(), actual.poVsInbound)
        assertEquals(expected.asnVsInbound?.getValue()?.toLong(), actual.asnVsInbound)
        assertEquals(expected.supplierName, actual.supplierName)
    }
}
