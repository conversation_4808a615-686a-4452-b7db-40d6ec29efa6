package com.hellofresh.cif.api.note

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.api.calculation.generated.model.CreateNoteRequest
import com.hellofresh.cif.api.calculation.generated.model.CreateNoteResponse
import com.hellofresh.cif.api.calculation.generated.model.GetNotesResponse
import com.hellofresh.cif.api.calculation.generated.model.NoteResponse
import com.hellofresh.cif.api.calculation.generated.model.UpdateNoteRequest
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.user.AuthUtils.addAuthHeader
import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking

internal class NoteApiTest {

    private val jwtSecret = "testSecret"
    private val noteService = mockk<NoteService>()
    private val jwksURI = "https://test.com"

    @Test
    fun `should be able to create note with the given input`() {
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        val createNoteRequest = createDtoNoteRequest()
        val note = createNote()

        coEvery {
            noteService.createNote(
                CreateNoteRequest(
                    skuId = createNoteRequest.skuId,
                    authorEmail = authorEmail,
                    authorName = authorName,
                    dcCodes = createNoteRequest.dcCodes.toSet(),
                    dcWeeks = createNoteRequest.weeks.map(::DcWeek).toSet(),
                    text = createNoteRequest.text,
                ),
            )
        } returns note

        runBlocking {
            post(
                "/notes",
                createNoteRequest,
                authorEmail,
                authorName,
            )
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val createNoteResponse = objectMapper.readValue(
                        bodyAsText(),
                        CreateNoteResponse::class.java,
                    )

                    createNoteResponse.apply {
                        assertEquals(this.id, note.id)
                        assertEquals(this.skuId, note.skuId)
                        assertEquals(this.authorEmail, note.authorEmail)
                        assertEquals(this.authorName, note.authorName)
                        assertEquals(this.dcCodes, note.dcCodes.toList())
                        assertEquals(this.weeks, note.weeks.map(DcWeek::toString))
                        assertEquals(this.createdAt, note.createdAt)
                        assertEquals(this.isEdited, note.isEdited)
                    }
                }
        }
    }

    @Test
    fun `should be able to update note with the given input`() {
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        val updateNoteRequest = getUpdateNoteRequest()
        val note = createNote()

        coEvery {
            noteService.updateNote(
                UpdateNoteRequest(
                    noteId = note.id,
                    authorEmail = authorEmail,
                    authorName = authorName,
                    dcWeeks = updateNoteRequest.weeks.map(::DcWeek).toSet(),
                    text = updateNoteRequest.text,
                ),
            )
        } returns note

        runBlocking {
            put(
                "/notes/${note.id}",
                updateNoteRequest,
                authorEmail,
                authorName,
            )
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val createNoteResponse = objectMapper.readValue(
                        bodyAsText(),
                        CreateNoteResponse::class.java,
                    )

                    createNoteResponse.apply {
                        assertEquals(this.id, note.id)
                        assertEquals(this.skuId, note.skuId)
                        assertEquals(this.authorEmail, note.authorEmail)
                        assertEquals(this.authorName, note.authorName)
                        assertEquals(this.dcCodes, note.dcCodes.toList())
                        assertEquals(this.weeks, note.weeks.map(DcWeek::toString))
                        assertEquals(this.createdAt, note.createdAt)
                        assertEquals(this.isEdited, note.isEdited)
                    }
                }
        }
    }

    @Test
    fun `should return 404 while updating the note that does not exist for the given note id`() {
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        val updateNoteRequest = getUpdateNoteRequest()
        val note = createNote()

        coEvery {
            noteService.updateNote(
                UpdateNoteRequest(
                    noteId = note.id,
                    authorEmail = authorEmail,
                    authorName = authorName,
                    dcWeeks = updateNoteRequest.weeks.map(::DcWeek).toSet(),
                    text = updateNoteRequest.text,
                ),
            )
        } throws NoteNotFoundException(IllegalArgumentException(note.id.toString()))

        runBlocking {
            put(
                "/notes/${note.id}",
                updateNoteRequest,
                authorEmail,
                authorName,
            )
                .apply {
                    assertEquals(HttpStatusCode.NotFound, status)
                }
        }
    }

    @Test
    fun `should return 404 while deleting the note that does not exist for the given note id`() {
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        val note = createNote()

        coEvery {
            noteService.deleteNote(
                id = note.id,
                authorEmail = authorEmail,
                authorName = authorName,
            )
        } throws NoteNotFoundException(IllegalArgumentException(note.id.toString()))

        runBlocking {
            delete(
                "/notes/${note.id}",
                authorEmail,
                authorName,
            )
                .apply {
                    assertEquals(HttpStatusCode.NotFound, status)
                }
        }
    }

    @Test
    fun `should be able to delete a note with the given note id`() {
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        val note = createNote()

        coEvery {
            noteService.deleteNote(
                note.id,
                authorName,
                authorEmail,
            )
        } returns Unit

        runBlocking {
            delete(
                "/notes/${note.id}",
                authorEmail,
                authorName,
            )
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                }
        }

        coVerify(exactly = 1) {
            noteService.deleteNote(
                note.id,
                authorName,
                authorEmail,
            )
        }
    }

    @Test
    fun `should be able to get note with the given input`() {
        val note = createNote()
        val dcCodes = listOf("VE", "BX")
        val queryParams = Parameters.build {
            dcCodes.forEach { this.append("dcCode", it) }
            this.append("weeks", "2022-W01")
        }.formUrlEncode()

        coEvery {
            noteService.getNotes(any())
        } returns mapOf(note.skuId to listOf(note))

        runBlocking {
            get("/notes?$queryParams")
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val getNoteResponse = objectMapper.readValue(
                        bodyAsText(),
                        GetNotesResponse::class.java,
                    )
                    assertEquals(note.skuId, getNoteResponse.notesBySkuId.first().skuId)
                    assertEquals(note.atRisk.toAPIAtRisk(), getNoteResponse.notesBySkuId.first().atRisk)
                    getNoteResponse.notesBySkuId.first().notes[0].apply {
                        assertNote(note)
                    }
                }
        }
    }

    @Test
    fun `should return empty notes for the given input`() {
        val dcCodes = listOf("VE", "BX")
        val queryParams = Parameters.build {
            dcCodes.forEach { this.append("dcCode", it) }
            this.append("weeks", "2022-W01")
        }.formUrlEncode()

        coEvery {
            noteService.getNotes(any())
        } returns emptyMap()

        runBlocking {
            get("/notes?$queryParams")
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val getNoteResponse = objectMapper.readValue(
                        bodyAsText(),
                        GetNotesResponse::class.java,
                    )
                    assertEquals(0, getNoteResponse.notesBySkuId.size)
                }
        }
    }

    @Test
    fun `should be able to get multiple notes in sorted order for the given input`() {
        val firstSkuId = UUID.randomUUID()
        val secondSkuId = UUID.randomUUID()
        val thirdSkuId = UUID.randomUUID()
        val firstNote = createNote().copy(skuId = firstSkuId, dcCodes = setOf("VE"))
        val secondNote = createNote().copy(skuId = secondSkuId, dcCodes = setOf("BX"))
        val thirdNote = createNote().copy(skuId = thirdSkuId, dcCodes = setOf("GR"))
        val dcCodes = listOf("VE", "BX")
        val queryParams = Parameters.build {
            dcCodes.forEach { this.append("dcCode", it) }
            this.append("weeks", "2022-W01")
        }.formUrlEncode()

        coEvery {
            noteService.getNotes(
                any(),
            )
        } returns mapOf(
            firstNote.skuId to listOf(firstNote),
            secondNote.skuId to listOf(secondNote),
            thirdNote.skuId to listOf(thirdNote),
        )

        runBlocking {
            get("/notes?$queryParams")
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val getNoteResponse = objectMapper.readValue(
                        bodyAsText(),
                        GetNotesResponse::class.java,
                    )
                    assertEquals(firstNote.skuId, getNoteResponse.notesBySkuId.first().skuId)
                    assertEquals(secondNote.skuId, getNoteResponse.notesBySkuId[1].skuId)
                    getNoteResponse.notesBySkuId[0].notes[0].apply {
                        assertNote(firstNote)
                    }

                    getNoteResponse.notesBySkuId[1].notes[0].apply {
                        assertNote(secondNote)
                    }

                    getNoteResponse.notesBySkuId[2].notes[0].apply {
                        assertNote(thirdNote)
                    }
                }
        }
    }

    @Test
    fun `unauthorized HTTP response when no jwt token is given`() {
        val createNoteRequest = createDtoNoteRequest()

        runBlocking {
            post(
                "/notes",
                createNoteRequest,
                null,
                null,
                false,
            )
                .apply {
                    assertEquals(HttpStatusCode.Unauthorized, status)
                }
        }
    }

    @Test
    fun `unauthorized HTTP response when jwt token doesn't include email claim`() {
        val authorName = UUID.randomUUID().toString()
        val createNoteRequest = createDtoNoteRequest()

        runBlocking {
            post(
                "/notes",
                createNoteRequest,
                null,
                authorName,
            )
                .apply {
                    assertEquals(HttpStatusCode.Unauthorized, status)
                }
        }
    }

    private fun createDtoNoteRequest() =
        CreateNoteRequest(
            skuId = UUID.randomUUID(),
            dcCodes = listOf(UUID.randomUUID().toString()),
            weeks = listOf("2023-W50"),
            text = UUID.randomUUID().toString(),
        )

    private fun getUpdateNoteRequest() =
        UpdateNoteRequest(
            weeks = listOf("2023-W50"),
            text = UUID.randomUUID().toString(),
        )

    private fun createNote() =
        Note(
            id = UUID.randomUUID(),
            skuId = UUID.randomUUID(),
            authorEmail = UUID.randomUUID().toString(),
            authorName = UUID.randomUUID().toString(),
            dcCodes = setOf("DC1", "DC2"),
            weeks = setOf(
                DcWeek("2023-W23"),
                DcWeek("2023-W24"),
            ),
            text = UUID.randomUUID().toString(),
            createdAt = OffsetDateTime.now(ZoneOffset.UTC),
            atRisk = listOf(AtRisk("DC1", DcWeek("2023-W24"))),
            0,
            false,
            false,
        )

    private fun post(
        postUrl: String,
        createNoteRequest: CreateNoteRequest,
        authorEmail: String?,
        authorName: String?,
        kwtAuthEnabled: Boolean = true,
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                noteModule(noteService, timeOutInMillis)()
            }
            response = client.post(postUrl) {
                setBody(prepareRequestBody(createNoteRequest))
                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                if (kwtAuthEnabled) this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }

    private fun put(
        url: String,
        updateNoteRequest: UpdateNoteRequest,
        authorEmail: String?,
        authorName: String?,
        kwtAuthEnabled: Boolean = true,
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                noteModule(noteService, timeOutInMillis)()
            }
            response = client.put(url) {
                setBody(objectMapper.writeValueAsString(updateNoteRequest))
                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                if (kwtAuthEnabled) this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }

    private fun delete(
        url: String,
        authorEmail: String?,
        authorName: String?,
        kwtAuthEnabled: Boolean = true
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                noteModule(noteService, timeOutInMillis)()
            }
            response = client.delete(url) {
                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                if (kwtAuthEnabled) this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }

    private fun get(
        getUrl: String
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                noteModule(noteService, timeOutInMillis)()
            }
            response = client.get(getUrl) {
                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                addAuthHeader("<EMAIL>", "testAuthor", jwtSecret)
            }
        }
        return response
    }

    private fun prepareRequestBody(createNoteRequest: CreateNoteRequest) =
        objectMapper.writeValueAsString(createNoteRequest)

    private fun NoteResponse.assertNote(note: Note) {
        assertEquals(this.id, note.id)
        assertEquals(this.authorEmail, note.authorEmail)
        assertEquals(this.authorName, note.authorName)
        assertEquals(this.weeks, note.weeks.map(DcWeek::toString))
        assertEquals(this.createdAt, note.createdAt)
        assertEquals(this.isEdited, note.isEdited)
    }

    companion object {
        val timeOutInMillis: Duration = Duration.parse("PT1S")
        val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
