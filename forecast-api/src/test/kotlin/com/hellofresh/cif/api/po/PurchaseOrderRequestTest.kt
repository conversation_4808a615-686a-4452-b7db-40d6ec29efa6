package com.hellofresh.cif.api.po

import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import org.junit.jupiter.api.assertThrows

class PurchaseOrderRequestTest {
    @Test
    fun `should fail if only one of fromDate and toDate is present`() {
        assertThrows<IllegalArgumentException> {
            PurchaseOrdersReq(UUID(0, 0), setOf("VE"), weeks = null, fromDate = LocalDate.now(), toDate = null)
        }
        assertThrows<IllegalArgumentException> {
            PurchaseOrdersReq(UUID(0, 0), setOf("VE"), weeks = null, fromDate = null, toDate = LocalDate.now())
        }
    }

    @Test
    fun `should fail if fromDate is after toDate`() {
        assertThrows<IllegalArgumentException> {
            PurchaseOrdersReq(
                UUID(0, 0),
                setOf("VE"),
                weeks = null,
                fromDate = LocalDate.now().plusDays(1),
                toDate = LocalDate.now(),
            )
        }
    }

    @Test
    fun `should fail if dc codes are empty`() {
        assertThrows<IllegalArgumentException> {
            PurchaseOrdersReq(
                skuId = UUID(0, 0),
                dcCodes = emptySet(),
                weeks = setOf(DcWeek.invoke(LocalDate.now(), MONDAY)),
                fromDate = null,
                toDate = null,
            )
        }
    }
}
