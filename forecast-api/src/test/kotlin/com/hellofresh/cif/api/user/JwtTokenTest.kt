package com.hellofresh.cif.api.user

import com.auth0.jwk.Jwk
import com.auth0.jwk.JwkProvider
import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.ktor.createJwtVerifier
import io.ktor.client.request.get
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import io.ktor.server.testing.ApplicationTestBuilder
import io.mockk.every
import io.mockk.mockk
import java.security.KeyPairGenerator
import java.security.interfaces.RSAPrivateKey
import java.security.interfaces.RSAPublicKey
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class JwtTokenTest {

    private val jwtCredentials = JwtCredentials(
        secret = "secret",
        realm = "realm",
        issuer = "https://issuer",
        clientId = "clientId",
        jwksURI = "https://example.com/.well-known/jwks.json"
    )

    private fun application(authEnabled: Boolean) = ApplicationTestBuilder().apply {
        application {
            configureJwtAuth(JwtCredentials("test", "test", "", "", "https://test.com"), authEnabled)
            routing {
                route("/") {
                    authenticate {
                        get {
                            call.respond("ok")
                        }
                    }
                }
            }
        }
    }

    @Test
    fun `does not authenticate when disabled`() {
        runBlocking {
            application(authEnabled = false).apply {
                client.get("/").apply {
                    assertEquals(HttpStatusCode.OK, this.status)
                }
            }
        }
    }

    @Test
    fun `returns 401 when authentication is enabled`() {
        runBlocking {
            application(authEnabled = true).apply {
                client.get("/").apply {
                    assertEquals(HttpStatusCode.Unauthorized, this.status)
                }
            }
        }
    }

    @Test
    fun `test createJwtVerifier with RSA key`() {
        val jwkProvider = mockk<JwkProvider>()

        val keyPairGenerator = KeyPairGenerator.getInstance("RSA")
        keyPairGenerator.initialize(2048)
        val keyPair = keyPairGenerator.generateKeyPair()

        val publicKeyId = keyPair.public as RSAPublicKey
        val privateKeyId = keyPair.private as RSAPrivateKey

        val jwk = mockk<Jwk> {
            every { publicKey } returns publicKeyId
        }

        every { jwkProvider["keyId"] } returns jwk

        val algorithm = Algorithm.RSA256(publicKeyId, privateKeyId)

        val token = JWT.create()
            .withKeyId("keyId")
            .withIssuer(jwtCredentials.issuer)
            .withAudience(jwtCredentials.clientId)
            .withClaim("email", "<EMAIL>")
            .withClaim("metadata", mapOf("name" to "test"))
            .withClaim("roleclaim", arrayListOf("test.role"))
            .sign(algorithm)

        // Act
        val verifier = createJwtVerifier(jwkProvider, jwtCredentials, "Bearer $token")

        // Assert
        assertNotNull(verifier)
        assert(verifier.verify(token).algorithm == "RS256")
    }

    @Test
    fun `test createJwtVerifier with HMAC key`() {
        val algorithm = Algorithm.HMAC256(jwtCredentials.secret)

        val token = JWT.create()
            .withClaim("email", "<EMAIL>")
            .withClaim("sub", UUID.randomUUID().toString())
            .sign(algorithm)

        val jwkProvider = mockk<JwkProvider>()

        // Act
        val verifier = createJwtVerifier(jwkProvider, jwtCredentials, "Bearer $token")

        // Assert
        assertNotNull(verifier.verify(token).algorithm)
        assert(verifier.verify(token).algorithm == "HS256")
    }
}
