package com.hellofresh.cif.api.stockupdate

import com.hellofresh.cif.api.stockupdate.StockUpdateInputDataService.Companion.mapToInputData
import com.hellofresh.cif.calculator.CalculatorClient
import com.hellofresh.cif.calculator.models.Calculation<PERSON>ey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.demand.DemandRepositoryImpl
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_FCMS
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventory.InventoryService.Companion.calculationInputDcDateRange
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.TimeRange
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.skuinput.repo.DAYS_IN_PAST
import com.hellofresh.cif.skuinput.repo.NUMBER_OF_DAYS_FOR_CALCULATION
import com.hellofresh.cif.skuinput.repo.SkuInputData
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.demand.models.Demand
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.default
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.TUESDAY
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class StockUpdateCalculationServiceTest {
    private val skuInputDataRepository = mockk<SkuInputDataRepository>(relaxed = true)
    private val inventoryService = mockk<InventoryService>(relaxed = true)
    private val purchaseOrderRepository = mockk<PurchaseOrderRepository>(relaxed = true)
    private val demandRepository = mockk<DemandRepositoryImpl>(relaxed = true)
    private val safetyStockRepository = mockk<SafetyStockRepository>(relaxed = true)

    private val calculatorClient = mockk<CalculatorClient>(relaxed = true)
    private val stockUpdateInputDataService = StockUpdateInputDataService(
        skuInputDataRepository,
        inventoryService,
        purchaseOrderRepository,
        demandRepository,
        safetyStockRepository,
        emptySet()
    )
    private val stockUpdateCalculationService = StockUpdateCalculationService(
        calculatorClient,
        stockUpdateInputDataService,
    )
    private val dcCode = "VE"
    private val distributionCenterConfiguration =
        DistributionCenterConfiguration(
            dcCode,
            MONDAY,
            TUESDAY,
            "dach",
            ZoneId.of("UTC"),
            true,
            true,
            wmsType = WMS_SYSTEM_FCMS,
        )
    private val today = LocalDate.now()

    @ParameterizedTest
    @CsvSource("PRODUCTION", "PRE_PRODUCTION")
    fun `should be able to get calculation stock updates results for different modes`(calculatorMode: CalculatorMode) {
        // Given
        val skuId = UUID.randomUUID()
        val productionWeek = DcWeek(today, distributionCenterConfiguration.productionStart).value
        val dayCalculationResults = createDayCalculationResult(productionWeek)
        val calculationKey = CalculationKey(skuId, dcCode, today)
        val stockUpdates = mapOf(calculationKey to SkuQuantity.fromLong(100L, UOM_UNIT))

        val inventorySnapshots = createInventorySnapshot(calculationKey)
        val skuInputData = createSkuInputData(calculationKey)
        val purchaseOrders = createPurchaseOrders(calculationKey)
        val demands = listOf<Demand>()

        coEvery { skuInputDataRepository.fetchSkuInputData(dcCode, skuId) } returns skuInputData
        coEvery {
            inventoryService.fetchInventorySnapshots(skuInputData.dcConfig.values.toSet(), skuId)
        } returns inventorySnapshots
        coEvery { purchaseOrderRepository.findPurchaseOrdersWithAsns(skuId, setOf(dcCode), any()) } returns purchaseOrders

        val dateRange = calculationInputDcDateRange(distributionCenterConfiguration, inventorySnapshots)
        coEvery { demandRepository.findDemands(setOf(dcCode), dateRange) } returns listOf()

        coEvery { skuInputDataRepository.fetchSkuInputData(dcCode, skuId) } returns skuInputData
        val dayCalculationResultList = listOf(dayCalculationResults)

        val safetyStock = SafetyStock(dcCode, skuId, productionWeek, 100, ONE, SkuRiskRating.MEDIUM)
        coEvery {
            safetyStockRepository.fetchSafetyStock(
                DcWeek(dateRange.fromDate, distributionCenterConfiguration.productionStart).value,
                dcCode,
            )
        } returns listOf(safetyStock)

        every {
            calculatorClient.runDailyCalculations(
                mapToInputData(
                    calculatorMode,
                    skuId,
                    skuInputData.skuSpecification.values.first(),
                    skuInputData.dcConfig.values.first(),
                    inventorySnapshots,
                    purchaseOrders,
                    demands,
                    stockUpdates,
                    mapOf(safetyStock.toKey() to safetyStock.value),
                    emptyList(),
                    emptySet()
                ),
            )
        } returns dayCalculationResultList

        // When
        val calculationResults = runBlocking {
            stockUpdateCalculationService.runStockUpdates(
                dcCode,
                skuId,
                setOf(productionWeek),
                stockUpdates,
                calculatorMode,
            )
        }

        // Then
        assertEquals(dayCalculationResultList, calculationResults.calculations)
    }

    @Test
    fun `should be able to get filtered calculation results`() {
        // Given
        val skuId = UUID.randomUUID()
        val productionWeek = "2022-W53"

        val dayCalculationResult = createDayCalculationResult(productionWeek)
        val dayCalculationResultOtherWeek = dayCalculationResult.copy(productionWeek = "2022-W54")
        val calculationKey = CalculationKey(skuId, dcCode, today)
        val stockUpdates = mapOf(calculationKey to SkuQuantity.fromLong(100L, UOM_UNIT))

        val skuInputData = createSkuInputData(calculationKey)
        val inventorySnapshots = createInventorySnapshot(calculationKey)
        val purchaseOrders = createPurchaseOrders(calculationKey)
        val demands = listOf<Demand>()
        coEvery { skuInputDataRepository.fetchSkuInputData(dcCode, skuId) } returns skuInputData
        coEvery {
            inventoryService.fetchInventorySnapshots(skuInputData.dcConfig.values.toSet(), skuId)
        } returns inventorySnapshots
        coEvery { purchaseOrderRepository.findPurchaseOrdersWithAsns(skuId, setOf(dcCode), any()) } returns purchaseOrders

        coEvery { skuInputDataRepository.fetchSkuInputData(dcCode, skuId) } returns skuInputData
        val dayCalculationResults = listOf(dayCalculationResult, dayCalculationResultOtherWeek)

        every {
            calculatorClient.runDailyCalculations(
                mapToInputData(
                    CalculatorMode.PRODUCTION,
                    skuId,
                    skuInputData.skuSpecification.values.first(),
                    skuInputData.dcConfig.values.first(),
                    inventorySnapshots,
                    purchaseOrders,
                    demands,
                    stockUpdates,
                    emptyMap(),
                    emptyList(),
                    emptySet()
                ),
            )
        } returns dayCalculationResults

        // When
        val calculationResults = runBlocking {
            stockUpdateCalculationService.runStockUpdates(
                dcCode,
                skuId,
                setOf(productionWeek),
                stockUpdates,
                CalculatorMode.PRODUCTION,
            )
        }

        // Then
        assertEquals(1, calculationResults.calculations.size)
        assertEquals(dayCalculationResult, calculationResults.calculations.first())
    }

    @Test
    fun `should be able to get calculation results when stock updates is empty`() {
        // Given
        val skuId = UUID.randomUUID()
        val productionWeek = "2022-W53"
        val dayCalculationResults = createDayCalculationResult(productionWeek)
        val calculationKey = CalculationKey(skuId, dcCode, LocalDate.now())

        // mock
        val skuInputData = createSkuInputData(calculationKey)
        val inventorySnapshots = createInventorySnapshot(calculationKey)
        val purchaseOrders = createPurchaseOrders(calculationKey)
        val demands = listOf<Demand>()
        coEvery { skuInputDataRepository.fetchSkuInputData(dcCode, skuId) } returns skuInputData
        coEvery {
            inventoryService.fetchInventorySnapshots(skuInputData.dcConfig.values.toSet(), skuId)
        } returns inventorySnapshots
        coEvery { purchaseOrderRepository.findPurchaseOrdersWithAsns(skuId, setOf(dcCode), any()) } returns purchaseOrders
        val dateRange = DateRange(
            fromDate = LocalDate.now(UTC).minusDays(DAYS_IN_PAST),
            toDate = LocalDate.now(UTC).plusDays(NUMBER_OF_DAYS_FOR_CALCULATION),
        )
        coEvery { demandRepository.findDemands(setOf(dcCode), dateRange) } returns listOf()

        coEvery { skuInputDataRepository.fetchSkuInputData(dcCode, skuId) } returns skuInputData
        val dayCalculationResultList = listOf(dayCalculationResults)
        every {
            calculatorClient.runDailyCalculations(
                mapToInputData(
                    CalculatorMode.PRODUCTION,
                    skuId,
                    skuInputData.skuSpecification.values.first(),
                    skuInputData.dcConfig.values.first(),
                    inventorySnapshots,
                    purchaseOrders,
                    demands,
                    emptyMap(),
                    emptyMap(),
                    emptyList(),
                    emptySet()
                ),
            )
        } returns dayCalculationResultList

        // When
        val calculationResults = runBlocking {
            stockUpdateCalculationService.runStockUpdates(
                dcCode,
                skuId,
                setOf(productionWeek),
                emptyMap(),
                CalculatorMode.PRODUCTION,
            )
        }

        // Then
        assertEquals(dayCalculationResultList, calculationResults.calculations)
    }

    @Test
    fun `should return all results when no week param requested`() {
        // Given
        val skuId = UUID.randomUUID()
        val productionWeek44 = "2022-W44"
        val productionWeek45 = "2022-W45"
        val dayCalculationResultDayOne = createDayCalculationResult(productionWeek44, skuId)
        val dayCalculationResultDayTwo = createDayCalculationResult(productionWeek45, skuId, today.plusDays(7))
        val dayResults = listOf(dayCalculationResultDayOne, dayCalculationResultDayTwo)
        val calculationKey = CalculationKey(skuId, dcCode, today)
        val skuInputData = createSkuInputData(calculationKey)
        coEvery {
            skuInputDataRepository.fetchSkuInputData(
                dcCode,
                skuId,
            )
        } returns skuInputData
        coEvery {
            inventoryService.fetchInventorySnapshots(skuInputData.dcConfig.values.toSet(), skuId)
        } returns createInventorySnapshot(calculationKey)
        coEvery {
            purchaseOrderRepository.findPurchaseOrdersWithAsns(
                skuId,
                setOf(dcCode),
                any(),
            )
        } returns createPurchaseOrders(calculationKey)
        every { calculatorClient.runDailyCalculations(any()) } returns dayResults

        // When
        val stockUpdateResults = runBlocking {
            stockUpdateCalculationService.runStockUpdates(
                dcCode,
                skuId,
                emptySet(),
                emptyMap(),
                CalculatorMode.PRODUCTION,
            )
        }

        assertEquals(
            dayResults.map { it.productionWeek }.toSet(),
            stockUpdateResults.calculations.map { it.productionWeek }.toSet(),
        )
    }

    @Test
    fun `should return calculations without and with stock updates`() {
        // Given
        val skuId = UUID.randomUUID()
        val productionWeek = "2022-W53"
        val dayCalculationResults = createDayCalculationResult(productionWeek)
        val dayCalculationResultsWithUpdates = createDayCalculationResult(
            productionWeek,
        ).copy(openingStock = dayCalculationResults.openingStock + SkuQuantity.fromLong(1000))
        val calculationKey = CalculationKey(skuId, dcCode, today)
        val stockUpdates = mapOf(calculationKey to SkuQuantity.fromLong(100L, UOM_UNIT))

        val inventorySnapshots = createInventorySnapshot(calculationKey)
        val skuInputData = createSkuInputData(calculationKey)
        val purchaseOrders = createPurchaseOrders(calculationKey)
        val demands = listOf<Demand>()

        coEvery { skuInputDataRepository.fetchSkuInputData(dcCode, skuId) } returns skuInputData
        coEvery {
            inventoryService.fetchInventorySnapshots(skuInputData.dcConfig.values.toSet(), skuId)
        } returns inventorySnapshots
        coEvery { purchaseOrderRepository.findPurchaseOrdersWithAsns(skuId, setOf(dcCode), any()) } returns purchaseOrders
        val dateRange = DateRange(
            fromDate = LocalDate.now(UTC).minusDays(DAYS_IN_PAST),
            toDate = LocalDate.now(UTC).plusDays(NUMBER_OF_DAYS_FOR_CALCULATION),
        )
        coEvery { demandRepository.findDemands(setOf(dcCode), dateRange) } returns listOf()

        coEvery { skuInputDataRepository.fetchSkuInputData(dcCode, skuId) } returns skuInputData

        val inputDataWithoutStockUpdates = mapToInputData(
            CalculatorMode.PRODUCTION,
            skuId,
            skuInputData.skuSpecification.values.first(),
            skuInputData.dcConfig.values.first(),
            inventorySnapshots,
            purchaseOrders,
            demands,
            emptyMap(),
            emptyMap(),
            emptyList(),
            emptySet()
        )
        every {
            calculatorClient.runDailyCalculations(inputDataWithoutStockUpdates)
        } returns listOf(dayCalculationResults)
        every {
            calculatorClient.runDailyCalculations(inputDataWithoutStockUpdates.copy(stockUpdates = stockUpdates))
        } returns listOf(dayCalculationResultsWithUpdates)

        // When
        val stockUpdateComparison = runBlocking {
            stockUpdateCalculationService.runStockUpdatesComparison(
                dcCode,
                skuId,
                inputDataWithoutStockUpdates.mode,
                stockUpdates,
                setOf(productionWeek)
            )
        }

        // Then
        assertEquals(dayCalculationResults, stockUpdateComparison.defaultCalculation.first())
        assertEquals(dayCalculationResultsWithUpdates, stockUpdateComparison.stockUpdateCalculations.first())
    }

    private fun createDayCalculationResult(productionWeek: String, skuId: UUID = UUID.randomUUID(), date: LocalDate = today) = DayCalculationResult(
        UOM_UNIT,
        skuId, dcCode, date, SkuQuantity.fromLong(1),
        SkuQuantity.fromLong(
            2,
        ),
        SkuQuantity.fromLong(3), SkuQuantity.fromLong(4), emptySet(), SkuQuantity.fromLong(5), emptySet(),
        SkuQuantity.fromLong(
            6,
        ),
        SkuQuantity.fromLong(
            7,
        ),
        SkuQuantity.fromLong(
            8,
        ),
        SkuQuantity.fromLong(9), productionWeek, SkuQuantity.fromLong(10), netNeeds = SkuQuantity.fromLong(10),
    )

    private fun createSkuInputData(
        calculationKey: CalculationKey,
    ): SkuInputData =
        SkuInputData(
            dcConfig = mapOf(
                calculationKey.dcCode to
                    distributionCenterConfiguration,
            ),
            skuSpecification = mapOf(calculationKey.cskuId to SkuSpecification.default),
        )

    private fun createInventorySnapshot(
        calculationKey: CalculationKey,
    ) = InventorySnapshots(
        listOf(
            InventorySnapshot(
                calculationKey.dcCode,
                UUID.randomUUID(),
                calculationKey.date.atStartOfDay(),
                listOf(
                    SkuInventory(
                        calculationKey.cskuId,
                        listOf(
                            Inventory(
                                SkuQuantity.fromBigDecimal(BigDecimal(4L)),
                                null,
                                location = Location("", LOCATION_TYPE_STAGING, null),
                                null,
                            ),
                        ),
                    ),
                ),
            ),
        ),
        emptyList(),
        emptyList(),
    )

    fun createPurchaseOrders(
        calculationKey: CalculationKey,
    ) = listOf(
        PurchaseOrder(
            "PO",
            "POREF",
            UUID.randomUUID(),
            calculationKey.dcCode,
            TimeRange(calculationKey.date.atStartOfDay(UTC), calculationKey.date.atStartOfDay(UTC).plusMinutes(1)),
            null,
            listOf(
                PurchaseOrderSku(
                    calculationKey.cskuId,
                    SkuQuantity.fromLong(5L),
                    listOf(
                        DeliveryInfo(
                            UUID.randomUUID().toString(),
                            calculationKey.date.atStartOfDay(UTC),
                            CLOSED,
                            SkuQuantity.fromLong(4L),
                        ),
                    ),
                ),
            ),
            poStatus = APPROVED,
        ),
    )
}
