package com.hellofresh.cif.api.reporting

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.api.calculation.generated.model.StockInboundVarianceReportingResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.user.AuthUtils.addAuthHeader
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.StockInboundVarianceSku
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.mockk
import java.util.UUID
import kotlin.test.Test
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals

class InboundVarianceApiTest {
    private val stockReportingService: StockReportingService = mockk()
    private val jwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"

    @Test
    fun `should return inbound variances`() {
        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                    stockReportingRoutingModule(stockReportingService, Duration.parse("PT1S"))()
                }
                val dcWeek = "2023-W14"
                val paramsOk = Parameters.build {
                    this.append("week", dcWeek)
                }.formUrlEncode()
                val expectedInboundVarianceList = listOf(
                    StockInboundVarianceSku(
                        poRef = "test-po-ref",
                        asnId = "test-asn-id",
                        supplierName = "test-supplier-name",
                        skuId = UUID.randomUUID(),
                        skuName = "test-sku-name",
                        skuCode = "test-sku-code",
                        skuCategory = "test-sku-category",
                        poVsInbound = SkuQuantity.fromLong(10L),
                        asnVsInbound = SkuQuantity.fromLong(10L),
                    ),
                )
                coEvery {
                    stockReportingService.getStockInboundVarianceReport(
                        any(), any(),
                    )
                } returns expectedInboundVarianceList

                client.get("/dc/VE/inboundVariance?$paramsOk") {
                    addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    val bodyStr = this.body<String>()
                    val body = jacksonObjectMapper()
                        .findAndRegisterModules()
                        .readValue(bodyStr, StockInboundVarianceReportingResponse::class.java)
                    assertEquals(HttpStatusCode.Companion.OK, this.status)
                    assertEquals(mapInboundVarianceResponse(expectedInboundVarianceList), body)
                }
            }
        }
    }

    @Test
    fun `should return Bad Request if required query params are missing`() {
        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                    stockReportingRoutingModule(
                        stockReportingService,
                        Duration.parse("PT1S")
                    )()
                }
                client.get("/dc/VE/inboundVariance") {
                    addAuthHeader(authorEmail, authorName, jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.Companion.BadRequest, this.status)
                }
            }
        }
    }
}
