package com.hellofresh.cif.api.demand

import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.mockk.coEvery
import io.mockk.mockk
import java.time.OffsetDateTime
import java.time.ZoneOffset
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

class DemandServiceTest {
    private val demandRepositoryMock: DemandRepository = mockk()
    private val demandService = DemandService(demandRepositoryMock)

    @Test
    fun `should return the demand info`() {
        // given
        val dcCodes = listOf("VE")
        val dcWeek = DcWeek("2023-W01")
        val expectedDemandInfo =
            mapOf(
                "VE" to dcCodes.map {
                    DcDemandTimestampWeek(
                        it,
                        dcWeek,
                        OffsetDateTime.now(ZoneOffset.UTC)
                    )
                }
            )
        coEvery { demandRepositoryMock.getLatestDemandUpdates(dcCodes, listOf(dcWeek)) } returns expectedDemandInfo

        // when
        val result = runBlocking { demandService.getLatestDemandInfo(dcCodes, listOf(dcWeek)) }

        // then
        assertEquals(expectedDemandInfo, result)
    }
}
