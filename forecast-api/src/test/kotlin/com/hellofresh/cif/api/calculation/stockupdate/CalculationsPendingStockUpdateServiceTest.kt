package com.hellofresh.cif.api.calculation.stockupdate

import com.hellofresh.cif.api.calculation.AllPages
import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.InventoryRefreshType
import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.api.calculation.fixtures.default
import com.hellofresh.cif.api.calculation.stockupdate.CalculationsPendingStockUpdateService.Companion.selectCalculatorModeRepository
import com.hellofresh.cif.api.stockupdate.StockUpdateApiService
import com.hellofresh.cif.api.stockupdate.StockUpdateResults
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.calculator.models.default
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.featureflags.Context
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.inventory.DcSku
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.models.SkuQuantity
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.Called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.verify
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class CalculationsPendingStockUpdateServiceTest {

    private val dcRepository = mockk<DcRepository>()
    private val dcConfigService = DcConfigService(SimpleMeterRegistry(), dcRepository)
    private val stockUpdateApiService = mockk<StockUpdateApiService>(relaxed = true)
    private val dcCode = "DC"
    private val dc = DistributionCenterConfiguration.default(dcCode)

    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(
        setOf(FeatureFlag.StockUpdate(setOf(ContextData(Context.MARKET, dc.market))))
    )

    private val calculationsPendingStockUpdateService =
        CalculationsPendingStockUpdateService(dcConfigService, stockUpdateApiService, statsigFeatureFlagClient)

    private val currentWeekDate = dc.getLatestProductionStart()
    private val currentWeek = DcWeek(currentWeekDate, dc.productionStart).value
    private val skuId = UUID.randomUUID()
    private val stockUpdate = StockUpdate(
        skuId = skuId,
        dcCode = dcCode,
        date = currentWeekDate,
        week = currentWeek,
        quantity = SkuQuantity.fromLong(100),
        reason = "",
        reasonDetail = null,
        authorName = "",
        authorEmail = "",
        version = 1,
        createdAt = currentWeekDate.atTime(LocalTime.NOON),
        deleted = false,
    )
    private val calculationRequest = CalculationRequest(
        dcCodes = listOf(dcCode),
        weeks = listOf("2024-W34"),
        pageRequest = AllPages,
        consumptionDaysAhead = 0,
        inventoryRefreshType = InventoryRefreshType.CLEARDOWN,
    )

    @BeforeTest
    fun beforeEach() {
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(dc)
    }

    @ParameterizedTest
    @CsvSource(
        "CLEARDOWN,0, true",
        "CLEARDOWN,1, false",
        "LIVE,0, false",
        "LIVE,1, false",
    )
    fun `stock update are just fetched for default view production`(
        inventoryRefreshType: InventoryRefreshType,
        consumptionDaysAhead: Int,
        enabled: Boolean,
    ) {
        runBlocking {
            calculationsPendingStockUpdateService.getCurrentStockUpdates(
                calculationRequest.copy(
                    consumptionDaysAhead = consumptionDaysAhead,
                    inventoryRefreshType = inventoryRefreshType,
                ),
            )
        }

        if (enabled) {
            coVerify(exactly = 1) {
                stockUpdateApiService.getCurrentStockUpdates(setOf(dcCode))
            }
        } else {
            verify { stockUpdateApiService wasNot Called }
        }
    }

    @Test
    fun `stock update pending calculations are not processed for empty stock updates`() {
        val currentWeekDate = dc.getLatestProductionStart()

        val calculationKey = CalculationKey(UUID.randomUUID(), dcCode, currentWeekDate)
        val calculations =
            mapOf(
                calculationKey to CalculationRecord.default(dcCode)
                    .copy(date = calculationKey.date, productionWeek = currentWeek),
            )

        val pendingCalculations = runBlocking {
            calculationsPendingStockUpdateService.processPendingCalculations(
                calculationRequest.copy(
                    weeks = listOf(currentWeek),
                ),
                calculations,
                emptyMap(),
            ) { emptyList() }
        }

        assertTrue(pendingCalculations.isEmpty())
    }

    @Test
    fun `stock update pending calculations are not processed for past weeks requests`() {
        val currentWeekDate = dc.getLatestProductionStart()
        val pastWeekDate = currentWeekDate.minusWeeks(1)
        val pastWeek = DcWeek(pastWeekDate, dc.productionStart).value

        val calculationKey = CalculationKey(UUID.randomUUID(), dcCode, pastWeekDate)
        val calculations =
            mapOf(
                calculationKey to CalculationRecord.default(dcCode)
                    .copy(date = pastWeekDate, productionWeek = pastWeek),
            )

        val pendingCalculations = runBlocking {
            calculationsPendingStockUpdateService.processPendingCalculations(
                calculationRequest.copy(
                    weeks = listOf(pastWeek),
                ),
                calculations,
                mapOf(
                    DcSku(calculationKey.dcCode, calculationKey.cskuId) to (
                        mapOf(currentWeekDate to mockk<StockUpdate>())
                        ),
                ),
            ) { emptyList() }
        }

        assertTrue(pendingCalculations.isEmpty())
    }

    @Test
    fun `stock update pending calculations are empty when stock updates are in sync`() {
        val currentWeekDate = dc.getLatestProductionStart()
        val currentWeek = DcWeek(currentWeekDate, dc.productionStart).value

        val calculationKey = CalculationKey(skuId, dcCode, currentWeekDate)
        val calculations = mapOf(
            calculationKey to CalculationRecord.default(dcCode)
                .copy(
                    cskuId = skuId, date = currentWeekDate, productionWeek = currentWeek,
                    stockUpdate = stockUpdate.quantity.getValue(),
                ),
        )

        val pendingCalculations = runBlocking {
            calculationsPendingStockUpdateService.processPendingCalculations(
                calculationRequest.copy(
                    weeks = listOf(currentWeek),
                ),
                calculations,
                mapOf(
                    DcSku(calculationKey.dcCode, calculationKey.cskuId) to (mapOf(currentWeekDate to stockUpdate)),
                ),
            ) { emptyList() }
        }

        assertTrue(pendingCalculations.isEmpty())
    }

    @Test
    fun `stock update pending calculations are returned for skus with stock updates not in sync`() {
        val currentWeekDate = dc.getLatestProductionStart()
        val currentWeek = DcWeek(currentWeekDate, dc.productionStart).value
        val nextWeekDate = currentWeekDate.plusWeeks(1)
        val nextWeek = DcWeek(nextWeekDate, dc.productionStart).value

        val calculationKey = CalculationKey(skuId, dcCode, currentWeekDate)
        val calculationKeyNextWeek = calculationKey.copy(date = nextWeekDate)
        val calculationRecord = CalculationRecord.default(dcCode)
            .copy(
                cskuId = skuId,
                date = currentWeekDate,
                productionWeek = currentWeek,
                stockUpdate = null,
            )
        val calculationKey2 = CalculationKey(UUID.randomUUID(), dcCode, currentWeekDate)
        val stockUpdate2 = stockUpdate.copy(skuId = calculationKey2.cskuId, quantity = SkuQuantity.fromLong(1000))

        val calculations = mapOf(
            calculationKey to calculationRecord,
            calculationKeyNextWeek to calculationRecord.copy(date = nextWeekDate, productionWeek = nextWeek),
            calculationKey2 to CalculationRecord.default(dcCode)
                .copy(
                    cskuId = calculationKey2.cskuId, date = currentWeekDate, productionWeek = currentWeek,
                    stockUpdate = stockUpdate2.quantity.getValue(),
                ),
        )

        val currentStockUpdates = mapOf<DcSku, Map<LocalDate, StockUpdate?>>(
            DcSku(calculationKey.dcCode, calculationKey.cskuId) to (
                mapOf(calculationKey.date to stockUpdate, calculationKey.date.plusDays(1) to null)
                ),
            DcSku(calculationKey2.dcCode, calculationKey2.cskuId) to (
                mapOf(calculationKey2.date to null, calculationKey2.date.plusDays(1) to stockUpdate2)
                ),
        )

        coEvery {
            stockUpdateApiService.runStockUpdates(
                dcCode, skuId, setOf(currentWeek, nextWeek),
                mapOf(calculationKey to stockUpdate.quantity),
                selectCalculatorModeRepository(calculationRequest.consumptionDaysAhead),
            )
        } returns StockUpdateResults(
            listOf(
                DayCalculationResult.default().copy(
                    cskuId = calculationKey.cskuId,
                    date = calculationKey.date,
                    productionWeek = currentWeek,
                    dcCode = calculationKey.dcCode,
                    closingStock = SkuQuantity.fromLong(100),
                    stockUpdate = stockUpdate.quantity,
                ),
                DayCalculationResult.default().copy(
                    cskuId = calculationKeyNextWeek.cskuId,
                    date = calculationKeyNextWeek.date,
                    productionWeek = nextWeek,
                    dcCode = calculationKeyNextWeek.dcCode,
                    closingStock = SkuQuantity.fromLong(1000),
                    stockUpdate = null,
                ),
            ),
            emptyMap(),
            emptySet(),
            emptyMap(),
        )

        val pendingCalculations = runBlocking {
            calculationsPendingStockUpdateService.processPendingCalculations(
                calculationRequest.copy(
                    weeks = listOf(currentWeek),
                ),
                calculations,
                currentStockUpdates,
            ) { emptyList() }
        }

        coVerify(exactly = 1) { stockUpdateApiService.runStockUpdates(any(), any(), any(), any(), any()) }

        assertEquals(2, pendingCalculations.size)
        assertEquals(100.toBigDecimal(), pendingCalculations[calculationKey]?.closingStock)
        assertEquals(stockUpdate.quantity.getValue(), pendingCalculations[calculationKey]?.stockUpdate)
        assertEquals(1000.toBigDecimal(), pendingCalculations[calculationKeyNextWeek]?.closingStock)
        assertNull(pendingCalculations[calculationKeyNextWeek]?.stockUpdate)
    }

    @Test
    fun `stock update pending calculations for multiple skus`() {
        val currentWeekDate = dc.getLatestProductionStart()
        val currentWeek = DcWeek(currentWeekDate, dc.productionStart).value

        val calculationKey = CalculationKey(skuId, dcCode, currentWeekDate)
        val calculationKey2 = CalculationKey(UUID.randomUUID(), dcCode, currentWeekDate)
        val stockUpdate2 = stockUpdate.copy(skuId = calculationKey2.cskuId, quantity = SkuQuantity.fromLong(1000))

        val calculations = mapOf(
            calculationKey to CalculationRecord.default(dcCode)
                .copy(
                    cskuId = skuId, date = currentWeekDate, productionWeek = currentWeek,
                    stockUpdate = null, closingStock = ZERO,
                ),
            calculationKey2 to CalculationRecord.default(dcCode)
                .copy(
                    cskuId = calculationKey2.cskuId, date = currentWeekDate, productionWeek = currentWeek,
                    stockUpdate = null, closingStock = ONE,
                ),
        )

        val currentStockUpdates = mapOf(
            DcSku(calculationKey.dcCode, calculationKey.cskuId) to (mapOf(calculationKey.date to stockUpdate)),
            DcSku(calculationKey2.dcCode, calculationKey2.cskuId) to (mapOf(calculationKey2.date to stockUpdate2)),
        )

        coEvery {
            stockUpdateApiService.runStockUpdates(
                dcCode, skuId, setOf(currentWeek),
                mapOf(calculationKey to stockUpdate.quantity),
                selectCalculatorModeRepository(calculationRequest.consumptionDaysAhead),
            )
        } returns StockUpdateResults(
            listOf(
                DayCalculationResult.default().copy(
                    cskuId = calculationKey.cskuId,
                    date = calculationKey.date,
                    productionWeek = currentWeek,
                    dcCode = calculationKey.dcCode,
                    closingStock = SkuQuantity.fromBigDecimal(TEN),
                    stockUpdate = stockUpdate.quantity,
                ),
            ),
            emptyMap(),
            emptySet(),
            emptyMap(),
        )

        coEvery {
            stockUpdateApiService.runStockUpdates(
                dcCode, calculationKey2.cskuId, setOf(currentWeek),
                mapOf(calculationKey2 to stockUpdate2.quantity),
                selectCalculatorModeRepository(calculationRequest.consumptionDaysAhead),
            )
        } returns StockUpdateResults(
            listOf(
                DayCalculationResult.default().copy(
                    cskuId = calculationKey2.cskuId,
                    date = calculationKey2.date,
                    productionWeek = currentWeek,
                    dcCode = calculationKey2.dcCode,
                    closingStock = SkuQuantity.fromBigDecimal(TEN.pow(2)),
                    stockUpdate = stockUpdate2.quantity,
                ),
            ),
            emptyMap(),
            emptySet(),
            emptyMap(),
        )

        val pendingCalculations = runBlocking {
            calculationsPendingStockUpdateService.processPendingCalculations(
                calculationRequest.copy(
                    weeks = listOf(currentWeek),
                ),
                calculations,
                currentStockUpdates,
            ) { emptyList() }
        }

        assertEquals(2, pendingCalculations.size)
        assertEquals(TEN, pendingCalculations[calculationKey]?.closingStock)
        assertEquals(stockUpdate.quantity.getValue(), pendingCalculations[calculationKey]?.stockUpdate)

        assertEquals(TEN.pow(2), pendingCalculations[calculationKey2]?.closingStock)
        assertEquals(stockUpdate2.quantity.getValue(), pendingCalculations[calculationKey2]?.stockUpdate)
    }

    @Test
    fun `should fetch missing calculations to compare with current stock updates and stock update was updated in last 5 minutes`() {
        val currentWeekDate = dc.getLatestProductionStart()
        val currentWeek = DcWeek(currentWeekDate, dc.productionStart).value
        val nextWeekDate = currentWeekDate.plusWeeks(1)
        val nextWeek = DcWeek(nextWeekDate, dc.productionStart).value

        val calculationKey = CalculationKey(skuId, dcCode, currentWeekDate)
        val calculationKeyNextWeek = calculationKey.copy(date = nextWeekDate)

        val skuCode = UUID.randomUUID().toString()
        val calculationsRequested = mapOf(
            calculationKeyNextWeek to CalculationRecord.default(dcCode)
                .copy(
                    cskuId = calculationKeyNextWeek.cskuId,
                    date = calculationKeyNextWeek.date,
                    productionWeek = nextWeek,
                    code = skuCode,
                    closingStock = ONE,
                ),
        )

        val extraCalculationsNeeded = listOf(
            CalculationRecord.default(dcCode)
                .copy(
                    cskuId = calculationKey.cskuId,
                    date = calculationKey.date,
                    productionWeek = currentWeek,
                    stockUpdate = null,
                ),
        )

        val currentStockUpdates = mapOf(
            DcSku(calculationKey.dcCode, calculationKey.cskuId) to (
                mapOf(
                    calculationKey.date to stockUpdate.copy(
                        createdAt = LocalDateTime.now(UTC).minusMinutes(1),
                    ),
                )
                ),
        )

        coEvery {
            stockUpdateApiService.runStockUpdates(
                dcCode, skuId, setOf(nextWeek),
                mapOf(calculationKey to stockUpdate.quantity),
                selectCalculatorModeRepository(calculationRequest.consumptionDaysAhead),
            )
        } returns StockUpdateResults(
            listOf(
                DayCalculationResult.default().copy(
                    cskuId = calculationKeyNextWeek.cskuId,
                    date = calculationKeyNextWeek.date,
                    productionWeek = nextWeek,
                    dcCode = calculationKeyNextWeek.dcCode,
                    closingStock = SkuQuantity.fromBigDecimal(TEN),
                ),
            ),
            emptyMap(),
            emptySet(),
            emptyMap(),
        )

        val mockCalculationFunction = mockk<(CalculationRequest) -> List<CalculationRecord>>()

        coEvery {
            mockCalculationFunction.invoke(
                match { req ->
                    currentWeek == req.weeks.first() &&
                        skuCode == req.skuCodes.first() &&
                        calculationRequest.inventoryRefreshType == req.inventoryRefreshType &&
                        calculationRequest.consumptionDaysAhead == req.consumptionDaysAhead
                },
            )
        } returns extraCalculationsNeeded

        val pendingCalculations = runBlocking {
            calculationsPendingStockUpdateService.processPendingCalculations(
                calculationRequest.copy(
                    weeks = listOf(currentWeek),
                ),
                calculationsRequested,
                currentStockUpdates,
                mockCalculationFunction,
            )
        }

        assertEquals(1, pendingCalculations.size)
        assertEquals(TEN, pendingCalculations[calculationKeyNextWeek]?.closingStock)
    }

    @Test
    fun `ignore pending calculations that do not request stock update weeks and stock update was updated more than 5 minutes ago`() {
        val currentWeekDate = dc.getLatestProductionStart()
        val currentWeek = DcWeek(currentWeekDate, dc.productionStart).value
        val nextWeekDate = currentWeekDate.plusWeeks(1)
        val nextWeek = DcWeek(nextWeekDate, dc.productionStart).value

        val calculationKey = CalculationKey(skuId, dcCode, currentWeekDate)
        val calculationKeyNextWeek = calculationKey.copy(date = nextWeekDate)

        val skuCode = UUID.randomUUID().toString()
        val calculationsRequested = mapOf(
            calculationKeyNextWeek to CalculationRecord.default(dcCode)
                .copy(
                    cskuId = calculationKeyNextWeek.cskuId,
                    date = calculationKeyNextWeek.date,
                    productionWeek = nextWeek,
                    code = skuCode,
                    closingStock = ONE,
                ),
        )

        val currentStockUpdates = mapOf(
            DcSku(calculationKey.dcCode, calculationKey.cskuId) to (
                mapOf(
                    calculationKey.date to stockUpdate.copy(
                        createdAt = LocalDateTime.now(UTC).minusMinutes(10),
                    ),
                )
                ),
        )
        val mockCalculationFunction = mockk<(CalculationRequest) -> List<CalculationRecord>>()

        val pendingCalculations = runBlocking {
            calculationsPendingStockUpdateService.processPendingCalculations(
                calculationRequest.copy(
                    weeks = listOf(currentWeek),
                ),
                calculationsRequested,
                currentStockUpdates,
                mockCalculationFunction,
            )
        }

        coVerify { mockCalculationFunction wasNot Called }
        assertEquals(0, pendingCalculations.size)
    }
}
