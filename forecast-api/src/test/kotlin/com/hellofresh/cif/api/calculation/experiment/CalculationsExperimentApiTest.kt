package com.hellofresh.cif.api.calculation.experiment

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.cif.api.calculation.generated.model.DailyCalculationExperimentResponse
import com.hellofresh.cif.api.calculation.generated.model.ErrorResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.stockupdate.StockUpdateResults
import com.hellofresh.cif.api.user.AuthUtils.addAuthHeader
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.ParametersBuilder
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

internal class CalculationsExperimentApiTest {
    private val dcCode = "VE"
    private val dailyCalculationExperimentService = mockk<CalculationExperimentService>(relaxed = true)
    private val today = LocalDate.now()
    private val jwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"

    @ParameterizedTest
    @MethodSource("getPostUrl")
    fun `should be able to get experimental calculations for the given input`(
        postUrl: String,
        skuId: String,
        calculatorMode: CalculatorMode
    ) {
        val weeks = setOf("2022-W41")
        val dayCalculation = createDayCalculationResult(UUID.fromString(skuId))
        val expectedExperiment = BigDecimal(1000)

        val dailyCalculationExperimentData = DailyCalculationExperimentData(
            dcCode,
            UUID.fromString(skuId),
            weeks,
            calculatorMode,
            mapOf(today to expectedExperiment),
        )
        coEvery {
            dailyCalculationExperimentService.getCalculationExperiment(
                dailyCalculationExperimentData,
            )
        } returns StockUpdateResults(
            listOf(dayCalculation),
            dailyCalculationExperimentData.calculationExperiments().mapValues { (_, value) -> SkuQuantity.fromBigDecimal(value, UOM_UNIT) },
            emptySet(),
            emptyMap(),
        )

        runBlocking {
            post(
                postUrl,
                today,
                expectedExperiment.toLong(),
            )
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val calculationExperimentResponses = objectMapper.readValue(
                        bodyAsText(),
                        Array<DailyCalculationExperimentResponse>::class.java,
                    )
                    assertEquals(1, calculationExperimentResponses.size)
                    calculationExperimentResponses[0].apply {
                        dayCalculation.apply {
                            assertEquals(date, date)
                            assertEquals(openingStock.getValue().toInt(), opening)
                            assertEquals(actualInbound.getValue().toInt(), inbound)
                            assertEquals(unusable, unusable)
                            assertEquals(closingStock.getValue().toInt(), closing)
                            assertEquals(demanded.getValue().toInt(), consumption)
                            assertEquals(actualConsumption, actualConsumption)
                        }
                        assertEquals(expectedExperiment.toInt(), experiment)
                    }
                }
        }
    }

    @Test
    fun `should return bad request while getting daily experimental calculations with more than 1 DCs as input`() {
        val skuId = UUID.randomUUID().toString()
        runBlocking {
            post(
                "/calculation/experiment/$skuId?dcCode=VE&dcCode=BO",
                today,
                100,
            )
                .apply {
                    assertBadRequest("Multiple DCs are not supported")
                }
        }
    }

    @Test
    fun `should return bad request while getting daily experimental calculations with zero DC as input`() {
        val skuId = UUID.randomUUID().toString()
        runBlocking {
            post(
                "/calculation/experiment/$skuId",
                today,
                100,
            )
                .apply {
                    assertBadRequest("dcCode is not specified")
                }
        }
    }

    @Test
    fun `should return Bad Request while getting experimental calculations with invalid skuId`() {
        val invalidSkuId = "invalid-sku-id-not-a-UUID"
        val postUrl = "/calculation/experiment/$dcCode/$invalidSkuId"
        runBlocking {
            post(
                postUrl,
                today,
                100,
            )
                .apply {
                    assertEquals(HttpStatusCode.BadRequest, status)
                    assertEquals("""{"reason":"Invalid UUID string: $invalidSkuId"}""", bodyAsText())
                }
        }
    }

    // This method will be deleted once we moved to - new experiment api in stock overview
    private fun post(
        postUrl: String,
        date: LocalDate,
        experiment: Long
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                calculationExperimentModule(
                    dailyCalculationExperimentService,
                    timeOutInMillis,
                )()
            }
            response = client.post(postUrl) {
                setBody(prepareRequestBody(date, experiment))
                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }

    // This method will be deleted once we moved to - new experiment api in stock overview
    private fun prepareRequestBody(date: LocalDate, experiment: Long) = """
        [
           {
              "date":"$date",
              "experiment":$experiment
           }
        ]
    """.trimIndent()

    private fun createDayCalculationResult(
        skuId: UUID
    ) = DayCalculationResult(
        UOM_UNIT,
        skuId, dcCode, today,
        SkuQuantity.fromLong(
            0,
        ),
        SkuQuantity.fromLong(1), SkuQuantity.fromLong(2), SkuQuantity.fromLong(3),
        setOf(
            "A",
        ),
        SkuQuantity.fromLong(
            4,
        ),
        setOf(
            "B",
        ),
        SkuQuantity.fromLong(
            5,
        ),
        SkuQuantity.fromLong(6), SkuQuantity.fromLong(7), SkuQuantity.fromLong(8), "2022-W41",
        SkuQuantity.fromLong(9), netNeeds = SkuQuantity.fromLong(10),
    )

    private suspend fun HttpResponse.assertBadRequest(expectedErrorMessage: String) {
        val errorResponse = objectMapper.readValue(bodyAsText(), ErrorResponse::class.java)
        assertEquals(HttpStatusCode.BadRequest, status)
        assertNotNull(errorResponse.reason)
        assertTrue(errorResponse.reason!!.contains(expectedErrorMessage))
    }

    companion object {
        val timeOutInMillis: Duration = Duration.parse("PT1S")
        val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()
        private val skuId = UUID.randomUUID().toString()

        @JvmStatic
        @Suppress("unused")
        private fun getPostUrl() = Stream.of(
            Arguments.of(getPostUrlUsingDcCodeAsPathParam(null), skuId, CalculatorMode.PRODUCTION),
            Arguments.of(getPostUrlUsingDcCodeAsPathParam(CalculatorMode.PRODUCTION), skuId, CalculatorMode.PRODUCTION),
            Arguments.of(
                getPostUrlUsingDcCodeAsPathParam(CalculatorMode.PRE_PRODUCTION),
                skuId,
                CalculatorMode.PRE_PRODUCTION
            ),
            Arguments.of(getPostUrlUsingDcCodeAsQueryParam(null), skuId, CalculatorMode.PRODUCTION),
            Arguments.of(
                getPostUrlUsingDcCodeAsQueryParam(CalculatorMode.PRODUCTION),
                skuId,
                CalculatorMode.PRODUCTION
            ),
            Arguments.of(
                getPostUrlUsingDcCodeAsQueryParam(CalculatorMode.PRE_PRODUCTION),
                skuId,
                CalculatorMode.PRE_PRODUCTION
            ),
        )

        private fun getPostUrlUsingDcCodeAsPathParam(calculatorMode: CalculatorMode?): String {
            val queryParams = Parameters.build {
                setOf("2022-W41").forEach { append("weeks", it) }
                calculatorMode?.also {
                    appendInventoryRefreshParamValue(calculatorMode)
                }
            }.formUrlEncode()
            return "/calculation/experiment/VE/$skuId?$queryParams"
        }

        private fun getPostUrlUsingDcCodeAsQueryParam(calculatorMode: CalculatorMode?): String {
            val queryParams = Parameters.build {
                setOf("VE").forEach { append("dcCode", it) }
                setOf("2022-W41").forEach { append("weeks", it) }
                calculatorMode?.also {
                    appendInventoryRefreshParamValue(calculatorMode)
                }
            }.formUrlEncode()
            return "/calculation/experiment/$skuId?$queryParams"
        }

        private fun ParametersBuilder.appendInventoryRefreshParamValue(calculatorMode: CalculatorMode) =
            append(
                "consumptionDaysAhead",
                when (calculatorMode) {
                    CalculatorMode.PRODUCTION -> 0
                    CalculatorMode.PRE_PRODUCTION -> 1
                    else -> error("Invalid mode: $calculatorMode")
                }.toString(),
            )
    }
}
