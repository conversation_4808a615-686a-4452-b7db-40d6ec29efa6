
### GET Daily Calculations for Verden and Week 2022-W25
GET {{host}}/calculation/dailyView?dcCode=VE&page=1&weeks=2022-W47&skuCount=6
Accept: application/json

### Calculation Filters
GET {{host}}/calculation/filters?weeks=2023-W40&dcCode=VE&AdditionalFilters=with_projected_waste&consumptionDaysAhead=0&inventoryRefreshType=cleardown
Accept: application/json
###
GET {{host}}/calculation/filters?weeks=2023-W40&weeks=2023-W41&weeks=2023-W42&dcCode=VE&inventoryRefreshType=cleardown&supplierId=14d28d66-f461-470f-ac8e-046fe8927265
Accept: application/json

### GET Weekly Calculations for Verden and Week 2022-W25
GET {{host}}/calculation/weeklyView?dcCode=VE&page=1&weeks=2022-W47&skuCount=6
Accept: application/json

### GET Daily Calculations for Verden and Week 2022-W25
###  DailyView serialized as CSV
GET {{host}}/calculation/dailyView?dcCode&VE&page=1&weeks=2022-W25&skuCount=6
Accept: text/csv

### GET Weekly Calculations for Verden and Week 2022-W25
###  WeeklyView serialized as CSV
GET {{host}}/calculation/weeklyView?dcCode=VE&page=1&weeks=2022-W25&skuCount=6
Accept: text/csv

### GET Date Code Detail
GET {{host}}/sku/46ab5972-0e5d-48c1-a6ba-8f0e4d929b40/detail?limit=100&dcCodes=GR
Accept: application/json

### GET Stock Variance Report
GET {{host}}/dc/VE/stockVariance
Accept: application/json

### POST perform the calculation experiment
POST {{host}}/calculation/experiment/VE/59567f74-0a3e-4ac5-9a3f-40e944fa4e1a?weeks=2023-W13
Accept: application/json
Content-Type: application/json

< ./calculationExperimentRequestBody.json

### POST perform the weekly calculation experiment
POST {{host}}/calculation/experiment/b5f4d935-69e8-459b-b414-0e1b6a886d08/weeklyView?dcCode=VE
Accept: application/json
Content-Type: application/json

< ./weeklyCalculationExperimentRequestBody.json
