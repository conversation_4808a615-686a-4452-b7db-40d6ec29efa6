package com.hellofresh.cif.distributionCenter.api

import com.auth0.jwk.JwkProvider
import com.auth0.jwk.JwkProviderBuilder
import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.interfaces.DecodedJWT
import com.auth0.jwt.interfaces.JWTVerifier
import com.hellofresh.cif.distributionCenter.JwtConfiguration
import com.hellofresh.cif.distributionCenter.service.DistributionCenterService
import io.ktor.serialization.jackson.jackson
import io.ktor.server.application.Application
import io.ktor.server.auth.authentication
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.jwt.jwt
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.routing.Route
import io.ktor.server.routing.routing
import java.net.URI
import java.security.interfaces.RSAPublicKey
import java.text.SimpleDateFormat
import kotlin.time.Duration

const val DEFAULT_LEEWAY_SECONDS: Long = 10

enum class AcceptedAlgorithm(val value: String) {
    RS256("RS256"),
    HS256("HS256"),
    NONE("none")
}

class RoutingModule {
    fun dcConfigRoutingModule(
        dcService: DistributionCenterService,
        jwtConfiguration: JwtConfiguration,
        timeout: Duration
    ): Application.() -> Unit = {
        configureJwtAuth(jwtConfiguration)
        routing {
            distributionCenterRouteUpdate(dcService, timeout).also {
                configureSerialization(it)
            }
            distributionCenterRouteRetrieve(dcService)
        }
    }
}

private fun configureSerialization(app: Route) {
    app.install(ContentNegotiation) {
        jackson {
            this.findAndRegisterModules()
            this.dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss")
        }
    }
}

private fun Application.configureJwtAuth(jwtConfiguration: JwtConfiguration) {
    val jwksUrl = URI(jwtConfiguration.jwksURI).toURL()

    val jwkProvider = JwkProviderBuilder(jwksUrl)
        .build()

    authentication {
        jwt {
            realm = jwtConfiguration.realm

            verifier { token ->
                createJwtVerifier(jwkProvider, jwtConfiguration, token.toString())
            }

            validate { credential ->
                JWTPrincipal(credential.payload)
            }
        }
    }
}

fun getJwtAlgorithmType(jwt: DecodedJWT): String = jwt.algorithm

fun createJwtVerifier(jwkProvider: JwkProvider, jwtCredentials: JwtConfiguration, token: String): JWTVerifier {
    val jwt = JWT.decode(token.split("Bearer ")[1])

    return when (getJwtAlgorithmType(jwt)) {
        AcceptedAlgorithm.RS256.value -> {
            val jwk = jwkProvider[jwt.keyId]
            val algorithm = Algorithm.RSA256(jwk.publicKey as RSAPublicKey, null)
            JWT.require(algorithm)
                .withIssuer(jwtCredentials.issuer)
                .withAudience(jwtCredentials.clientId)
                .acceptLeeway(DEFAULT_LEEWAY_SECONDS)
                .build()
        }

        AcceptedAlgorithm.HS256.value -> {
            JWT.require(Algorithm.HMAC256(jwtCredentials.secret))
                .withClaimPresence("sub")
                .withClaimPresence("email")
                .build()
        }

        AcceptedAlgorithm.NONE.value -> {
            JWT.require(Algorithm.none())
                .withIssuer(jwtCredentials.issuer)
                .withAudience(jwtCredentials.clientId)
                .acceptLeeway(DEFAULT_LEEWAY_SECONDS)
                .build()
        }

        else -> {
            throw IllegalArgumentException("Unsupported jwt algorithm ${getJwtAlgorithmType(jwt)}")
        }
    }
}
