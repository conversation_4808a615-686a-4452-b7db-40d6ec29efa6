package com.hellofresh.cif.distributionCenter.service

import com.hellofresh.cif.lib.MeteredJob
import dev.inmo.krontab.builder.buildSchedule
import dev.inmo.krontab.doInfinity
import io.micrometer.core.instrument.MeterRegistry
import java.util.concurrent.ExecutorService
import kotlin.coroutines.cancellation.CancellationException
import kotlin.time.Duration
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import org.apache.logging.log4j.kotlin.Logging

class DcConfigPublishScheduler(
    dcPublisher: DcConfigurationKafkaPublisher,
    private val duration: Duration,
    meterRegistry: MeterRegistry,
    private val executor: ExecutorService
) {

    private val meteredJob = MeteredJob(meterRegistry, "dc-config-publisher-duration", dcPublisher::publishDcConfigs)

    suspend fun scheduleJob() {
        CoroutineScope(executor.asCoroutineDispatcher()).launch {
            buildSchedule { seconds { 0 every duration.inWholeSeconds.toInt() } }
                .doInfinity {
                    meteredJob.execute()
                }
        }.invokeOnCompletion {
            if (it is CancellationException) {
                logger.warn("Scheduler is cancelled.", it)
            } else {
                logger.error("Scheduler has failed!", it)
            }
        }
    }

    companion object : Logging
}
