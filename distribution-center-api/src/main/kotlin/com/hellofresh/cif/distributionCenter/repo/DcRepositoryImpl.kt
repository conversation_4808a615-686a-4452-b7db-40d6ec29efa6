package com.hellofresh.cif.distributionCenter.repo

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.model.DcParamRequest
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distribution_center_api.schema.config.Tables.DISTRIBUTION_CENTER
import com.hellofresh.cif.distribution_center_api.schema.config.tables.records.DistributionCenterRecord
import com.hellofresh.cif.distribution_center_api.schema.public_.Tables.INVENTORY_PROCESSED_SNAPSHOTS
import java.time.DayOfWeek
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import kotlinx.coroutines.future.await
import org.jooq.Record
import org.jooq.impl.DSL
import org.jooq.impl.SQLDataType

const val DAYS_IN_PAST = 7L

class DcRepositoryImpl(private val metricsDSLContext: MetricsDSLContext) : DcRepository {

    private val updateDcCleardown = "update-dc-cleardown"
    private val updateDc = "update-dc"
    private val findDcByCode = "find-dc-by-code"
    private val getAllDcs = "get-all-dcs"

    override suspend fun updateCleardown(dcCode: String, cleardown: DayOfWeek, authorEmail: String): DcConfiguration? =
        metricsDSLContext.withTagName(updateDcCleardown)
            .transactionResultAsync { txConfig ->
                txConfig.dsl().fetchOne(DISTRIBUTION_CENTER, DISTRIBUTION_CENTER.DC_CODE.equalIgnoreCase(dcCode))
                    ?.let { dcConfigRecord ->
                        dcConfigRecord.cleardown = cleardown.name
                        dcConfigRecord.lastUpdatedByEmail = authorEmail
                        dcConfigRecord.published = false
                        txConfig.dsl().executeUpdate(dcConfigRecord)
                        mapDbRecord(dcConfigRecord)
                    }
            }.await()

    override suspend fun updateDc(dcParamRequest: DcParamRequest): DcConfiguration? =
        metricsDSLContext.withTagName(updateDc)
            .transactionResultAsync { txConfig ->
                val updatedRecord = txConfig.dsl().update(DISTRIBUTION_CENTER)
                    .set(DISTRIBUTION_CENTER.CLEARDOWN, dcParamRequest.cleardown.name)
                    .set(DISTRIBUTION_CENTER.PRODUCTION_START, dcParamRequest.productionStart.name)
                    .set(DISTRIBUTION_CENTER.ENABLED, dcParamRequest.enabled)
                    .set(DISTRIBUTION_CENTER.HAS_CLEARDOWN, dcParamRequest.hasCleardown)
                    .set(DISTRIBUTION_CENTER.LAST_UPDATED_BY_EMAIL, dcParamRequest.lastUpdatedByEmail)
                    .set(DISTRIBUTION_CENTER.PUBLISHED, false)
                    .set(DISTRIBUTION_CENTER.SCHEDULED_CLEARDOWN_TIME, dcParamRequest.scheduledClearDownTime)
                    .set(DISTRIBUTION_CENTER.PO_CUTOFF_TIME, dcParamRequest.poCutoffTime)
                    .where(DISTRIBUTION_CENTER.DC_CODE.equalIgnoreCase(dcParamRequest.dcCode))
                    .returning()
                    .fetchOne()

                updatedRecord?.let { dcConfigRecord ->
                    mapDbRecord(dcConfigRecord)
                }
            }.await()

    override suspend fun find(dcCode: String, fromDateTime: LocalDateTime?, toDateTime: LocalDateTime?): DcConfiguration? {
        val fromDateTimeCondition = fromDateTime
            ?: LocalDateTime.now().minusDays(DAYS_IN_PAST)

        val toDateTimeCondition = toDateTime
            ?: LocalDateTime.now()

        return metricsDSLContext.withTagName(findDcByCode)
            .selectDistinct(
                DISTRIBUTION_CENTER.DC_CODE,
                DISTRIBUTION_CENTER.MARKET,
                DISTRIBUTION_CENTER.PRODUCTION_START,
                DISTRIBUTION_CENTER.CLEARDOWN,
                DISTRIBUTION_CENTER.ZONE_ID,
                DISTRIBUTION_CENTER.GLOBAL_DC,
                DISTRIBUTION_CENTER.ENABLED,
                DISTRIBUTION_CENTER.LAST_UPDATED_BY_EMAIL,
                DISTRIBUTION_CENTER.HAS_CLEARDOWN,
                DISTRIBUTION_CENTER.SCHEDULED_CLEARDOWN_TIME,
                DISTRIBUTION_CENTER.PO_CUTOFF_TIME,
                DISTRIBUTION_CENTER.WMS_TYPE,
                INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_ID,
                INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_TIME,
            )
            .from(DISTRIBUTION_CENTER)
            .leftJoin(INVENTORY_PROCESSED_SNAPSHOTS)
            .on(
                DISTRIBUTION_CENTER.DC_CODE.eq(INVENTORY_PROCESSED_SNAPSHOTS.DC_CODE)
                    .and(
                        INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_TIME.between(
                            DSL.field(
                                "({0} AT TIME ZONE {1})",
                                SQLDataType.TIMESTAMPWITHTIMEZONE,
                                fromDateTimeCondition,
                                DISTRIBUTION_CENTER.ZONE_ID,
                            ),
                            DSL.field(
                                "({0} AT TIME ZONE {1})",
                                SQLDataType.TIMESTAMPWITHTIMEZONE,
                                toDateTimeCondition,
                                DISTRIBUTION_CENTER.ZONE_ID,
                            ),
                        ),
                    ),
            )
            .where(
                DISTRIBUTION_CENTER.DC_CODE.equalIgnoreCase(dcCode),
            )
            .orderBy(INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_TIME.desc())
            .fetchAsync()
            .thenApply { records ->
                if (records.isNullOrEmpty()) {
                    return@thenApply null
                }
                val record = records.first().into(DISTRIBUTION_CENTER)
                val snapshots = records.groupBy { it.get(DISTRIBUTION_CENTER.DC_CODE) }
                    .flatMap { (_, value) -> createSnapshotTimes(value) }
                mapDbRecord(record).copy(snapshotTimes = snapshots)
            }
            .await()
    }

    override suspend fun getAllDcs(market: String): List<DcConfiguration> =
        metricsDSLContext.withTagName(getAllDcs)
            .selectFrom(DISTRIBUTION_CENTER)
            .where(DISTRIBUTION_CENTER.MARKET.eq(market))
            .fetchAsync()
            .thenApply {
                it.map { v -> mapDbRecord(v) }
            }.await()

    override suspend fun getUnpublishedDcConfigs(): List<DcConfiguration> =
        metricsDSLContext.withTagName("unpublished-dcs")
            .selectFrom(DISTRIBUTION_CENTER)
            .where(DISTRIBUTION_CENTER.PUBLISHED.eq(false))
            .fetchAsync()
            .thenApply {
                it.map { v -> mapDbRecord(v) }
            }.await()

    override suspend fun updatePublishedFlag(dcCode: String, published: Boolean) {
        metricsDSLContext.withTagName("update-publish-dc")
            .update(DISTRIBUTION_CENTER)
            .set(DISTRIBUTION_CENTER.PUBLISHED, published)
            .set(DISTRIBUTION_CENTER.UPDATED_AT, LocalDateTime.now(ZoneOffset.UTC))
            .where(DISTRIBUTION_CENTER.DC_CODE.equalIgnoreCase(dcCode))
            .executeAsync()
            .await()
    }

    private fun mapDbRecord(dcConfigRecord: DistributionCenterRecord) =
        DcConfiguration(
            dcCode = dcConfigRecord.dcCode,
            market = dcConfigRecord.market,
            productionStart = DayOfWeek.valueOf(dcConfigRecord.productionStart),
            cleardown = dcConfigRecord.cleardown?.let { v -> DayOfWeek.valueOf(v) },
            zoneId = ZoneId.of(dcConfigRecord.zoneId),
            globalDc = dcConfigRecord.globalDc,
            enabled = dcConfigRecord.enabled,
            lastUpdatedByEmail = dcConfigRecord.lastUpdatedByEmail,
            hasCleardown = dcConfigRecord.hasCleardown,
            scheduledClearDownTime = dcConfigRecord.scheduledCleardownTime,
            wmsType = WmsSystem.valueOf(dcConfigRecord.wmsType ?: WmsSystem.UNRECOGNIZED.value),
            poCutoffTime = dcConfigRecord.poCutoffTime,
        )

    private fun createSnapshotTimes(records: List<Record>): List<SnapshotTime> =
        records.mapNotNull { record ->
            val zoneId = record.get(DISTRIBUTION_CENTER.ZONE_ID)
            val snapshotId = record.get(INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_ID)
            val snapshotTime = record.get(INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_TIME)
            if (snapshotId != null && snapshotTime != null) {
                SnapshotTime(
                    snapshotId = snapshotId,
                    snapshotTime = snapshotTime.atZoneSameInstant(ZoneId.of(zoneId)).toOffsetDateTime(),
                )
            } else {
                null
            }
        }
}
