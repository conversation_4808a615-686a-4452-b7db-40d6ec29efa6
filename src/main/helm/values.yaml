---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
# Deployment experiment, see build.gradle.kts
tag: 'latest'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

provisionDashboards:
  enabled: true
  dashboardLabel: 'grafana_dashboard'

rds-prom-exporter:
  environment: '@tier@'
  tribe: '@tribe@'
  squad: '@squad@'

  config:
    - instance: 'inventory-db000-@tier@'
      alerts:
        RDSDiskWillFillInXHours: { }
        RDSConnectionNumberHigh: { }
        RDSDiskIsXPercentFull: { }
        RDSCPUUsageHigh:
          threshold: 90
          for: 5m
        RDSMemoryUsageHigh: { }
        RDSDiskReadLatencyIncrease: { }

prometheus-cloudwatch-exporter:
    serviceAccount:
        annotations:
            eks.amazonaws.com/role-arn: 'arn:aws:iam::************:role/csku-inventory-forecast-@tier@-role'

    serviceMonitor:
        enabled: true
        interval: 1m
        labels:
            prometheus: csku-inventory-forecast-sqs-@tier@

    config: |-
        region: eu-west-1
        period_seconds: 60
        delay_seconds: 30
        set_timestamp: false
        use_get_metric_data: true
        metrics:
        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateAgeOfOldestMessage
          aws_statistics: [Maximum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: []

        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateNumberOfMessagesDelayed
          aws_statistics: [Average]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateNumberOfMessagesNotVisible
          aws_statistics: [Average]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateNumberOfMessagesVisible
          aws_statistics: [Average]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfEmptyReceives
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfMessagesDeleted
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfMessagesReceived
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfMessagesSent
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: SentMessageSize
          aws_statistics: [Average, Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

alerts:
  systemRules:
    - name: '@<EMAIL>'
      rules:
        - alert: '@projectName.upperCamelCase@AppNotRunning'
          expr: 'sum(kube_pod_container_status_running{container=~".*@projectKey@.*",namespace="scm"}) by (container) < 1'
          for: '5m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'App Not Running'
            description: 'No instance found for the past 5 Minutes'

        - alert: '@projectName.upperCamelCase@AppRestarts'
          expr: 'sum(increase(kube_pod_container_status_restarts_total{container=~".*@projectKey@.*",namespace="scm"}[20m])) by (container) > 1'
          for: '20m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'App Restarted Multiple Times'
            description: 'Instance restarted {{ humanize $value }} during the past 20 Minutes'

        - alert: '@projectName.upperCamelCase@CPUThrottle'
          expr: 'sum(increase(container_cpu_cfs_throttled_periods_total{namespace="scm", pod=~".*@projectKey@.*", container!="POD", container!=""}[5m])) by (container, pod, namespace) / sum(increase(container_cpu_cfs_periods_total{namespace="@tribe@", pod=~".*@projectKey@.*", container!="POD", container!=""}[5m])) by (container, pod, namespace) > 20'
          for: '10m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'CPU is throttling'
            description: 'CPU is throttling {{ humanize $value }}% for the past 10 Minutes'

        - alert: '@projectName.upperCamelCase@SinkError'
          expr: 'sum(increase(cif_inventory_forecast_db_sink_pipeline_processor_0_error[1m])) by (job) > 1'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Database sink encountered errors'
            description: 'Database sink failed {{ humanize $value }} while processing for the past 1 Minute'

        - alert: '@projectName.upperCamelCase@SinkDropped'
          expr: 'sum(increase(cif_inventory_forecast_db_sink_pipeline_processor_0_dropped[1m])) by (job) > 1'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Database sink dropped messages'
            description: 'Database sink has dropped {{ humanize $value }} messages while processing for the past 1 Minute'

        - alert: '@projectName.upperCamelCase@NoReplicas'
          expr: 'abs(kube_deployment_status_replicas_available{namespace="scm", deployment=~".*@projectKey@.*"}) < 1'
          for: '5m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'No replicas running'
            description: 'No replicas running for the past 1 Minute'

        - alert: '@projectName.upperCamelCase@CPUUsageTooHigh'
          expr: '(sum(increase(container_cpu_usage_seconds_total{namespace="scm", pod=~".*@projectKey@.*", container!="POD", container!=""}[1m])) by (pod) / sum(kube_pod_container_resource_limits_cpu_cores{namespace="@tribe@", pod=~".*@projectKey@.*", container!="POD", container!=""}) by (pod)) > 0.8 AND ON(pod) abs(kube_deployment_status_replicas_available{namespace="@tribe@", deployment=~".*@projectKey@.*"}) == 1'
          for: '5m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'CPU Usage is Too High'
            description: 'CPU usage is too high and there is no room to scale up (CPU usage + max number of replicas configured reached)'

        - alert: '@projectName.upperCamelCase@MemoryUsageTooHigh'
          expr: '(sum(container_memory_working_set_bytes{namespace="scm",pod=~".*@projectKey@.*", container!="POD", container!=""}) by (pod) / sum(kube_pod_container_resource_limits_memory_bytes{namespace="@tribe@",pod=~".*@projectKey@.*", container!="POD", container!=""}) by (pod)) > 0.8'
          for: '5m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Memory Usage is Too High'
            description: 'Memory usage is too high and there is no room to scale up (Memory usage + max number of replicas configured reached)'

        - alert: '@projectName.upperCamelCase@ConsumerLag'
          expr: 'max(kafka_consumergroup_group_lag{group=~"csku-inventory-forecast.*|cif-kafka-db-sink.*", consumer_id!~".*StreamThread.*"}) by (group) > 500'
          for: '20m'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Consumer group lag'
            description: 'Consumer group {{ $labels.name }} has lag of {{ humanize $value }}.'

        - alert: '@projectName.upperCamelCase@InventoryCronJobsFailingSince30Mins'
          expr: 'sum(procurement_csku_inventory_forecast_sanity_checker_monitor_inventory_cron_jobs{source="pg_cron_job"}) by (type, jobName) >= 1'
          for: '30m'
          labels:
            slack: 'proj-siv-alerts-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Inventory cron jobs are failing or not running as expected for last 30 mins'
            description: 'Inventory cron job {{ $labels.jobName }} {{ $labels.type }} failed or did not run for last 30 mins'

        - alert: '@projectName.upperCamelCase@CPUThrottlingHigh'
          expr: 'sum(increase(container_cpu_cfs_throttled_periods_total{container="cif-calculator-job-app"}[5m])) by (container, pod, namespace) / sum(increase(container_cpu_cfs_periods_total{}[5m])) by (container, pod, namespace) > 0.6'
          for: '15m'
          labels:
            slack: 'proj-siv-alerts-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Processes experience elevated CPU throttling.'
            description: '{{ $value | humanizePercentage }} throttling of CPU in namespace {{ $labels.namespace }} for container {{ $labels.container }} in pod {{ $labels.pod }}.'

  # scm prometheus
  additionalRules:
    - name: '@<EMAIL>-rules'
      rules:
        - alert: '@projectName.upperCamelCase@DeserializationError'
          expr: 'sum(rate(procurement_csku_inventory_forecast_component_failure_total{name="deserialization"}[1m])) by (topic) > 0'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Deserialization Errors'
            description: '{{ humanize $value }} deserialization errors happened'

        - alert: '@projectName.upperCamelCase@CalculatorJobFailure'
          expr: 'sum(increase(procurement_csku_inventory_forecast_component_failure_total{name="calculator-job"}[2m])) by (pod) > 0'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Calculator job failed'
            description: '{{ $labels.pod }} failed to run calculator job {{ humanize $value }} times in last 2 mins.'

        - alert: '@projectName.upperCamelCase@NoSku'
          expr: 'min(procurement_csku_inventory_forecast_number_of_skus{application="calculator-job"}) by (pod) == 0'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'No sku in a Calculator pod'
            description: '{{ $labels.pod }} has 0 sku to run calculations for,'

        - alert: '@projectName.upperCamelCase@CalculatorLatency'
          expr: 'max(procurement_csku_inventory_forecast_import_duration_seconds_max{name="calculator-job"}) by (pod) > 60'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Calculator job took more than 1 minute'
            description: '{{ $labels.pod }} took {{ humanize $value }} seconds to run calculations.'

        - alert: '@projectName.upperCamelCase@CalculatorSkuCandidates'
          expr: 'max(procurement_csku_inventory_forecast_calculator_job_sku_candidates{name="calculator-job"}) by (pod) > 10000'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Calculator job close to max sku candidate size. Evaluate necessary escalation'
            description: '{{ $labels.pod }} has {{ humanize $value }} candidates to run calculations.'

        - alert: '@projectName.upperCamelCase@CalculatorProducerFailure'
          expr: 'sum(rate(procurement_csku_inventory_forecast_producer_failure_total[2m])) by (pod) > 0'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Producer in the calculator failed to write'
            description: '{{ $labels.pod }} failed to write with the rate of {{ humanize $value }} per seconds.'

        - alert: '@projectName.upperCamelCase@APIErrorRate'
          expr: '(sum(increase(istio_requests_total{destination_service_name="cif-forecast-api-k8s",response_code=~"50.*"}[5m])) by (pod))*100/(sum(increase(istio_requests_total{destination_service_name="cif-forecast-api-k8s",response_code=~"20.*"}[5m])) by (pod)) > 20'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Last'
            description: '{{ $labels.pod }} responded with {{ humanize $value }}% 5xx response in last 1 minute.'

        - alert: '@projectName.upperCamelCase@APILatency'
          expr: 'histogram_quantile(0.99,sum(increase(istio_request_duration_milliseconds_bucket{destination_app="cif-forecast-api-app"}[1m])) by (le,pod))/1000 > 5'
          labels:
            slack: '@slackAlertChannel@-@tier@'
            severity: 'P4'
          annotations:
            summary: 'Last'
            description: '{{ $labels.pod }} has P99 latency of {{ humanize $value }}s in last 1 minute.'

        - alert: '@projectName.upperCamelCase@SkuIdCollisionFound'
          expr: 'sum(procurement_csku_inventory_forecast_sanity_checker_sku_id_collision) >= 1'
          for: '15m'
          labels:
              slack: '@slackAlertChannel@-@tier@'
              severity: 'P4'
          annotations:
              summary: 'Sku Id Collision Found'
              description: 'Sku Id Collision Found.'

        - alert: '@projectName.upperCamelCase@SkuCodeMarketCollisionFound'
          expr: 'sum(procurement_csku_inventory_forecast_sanity_checker_sku_code_market_collision) >= 1'
          for: '15m'
          labels:
              slack: '@slackAlertChannel@-@tier@'
              severity: 'P4'
          annotations:
              summary: 'Sku Code Market Collision Found'
              description: 'Sku Code Market Collision Found.'

        - alert: '@projectName.upperCamelCase@LowFreeSpace'
          expr: >
            sum(node_filesystem_avail_bytes{ job="cif-csku-inventory-forecast-rds-prom-exporter-k8s",instance="inventory-db000-live"}) /
            sum(node_filesystem_size_bytes{ job="cif-csku-inventory-forecast-rds-prom-exporter-k8s",instance="inventory-db000-live"}) < 0.3
          for: '5m'
          labels:
            severity: P4
            slack: '@slackAlertChannel@-@tier@'
          annotations:
            summary: 'Free space is low @ inventory-db000-live'
            description: 'The DB free space is less than the threshold. (current value: {{ humanize $value }})'
            dashboard: 'https://grafana.live-k8s.hellofresh.io/d/FuMo_ZEGk/csku-inventory-forecast?orgId=1&viewPanel=133&refresh=5m'

        - alert: '@projectName.upperCamelCase@LowFreeSpaceReplica'
          expr: >
            sum(node_filesystem_avail_bytes{ job="cif-csku-inventory-forecast-rds-prom-exporter-k8s",instance="inventory-replica-db001-live"}) /
            sum(node_filesystem_size_bytes{ job="cif-csku-inventory-forecast-rds-prom-exporter-k8s",instance="inventory-replica-db001-live"}) < 0.3
          for: '5m'
          labels:
            severity: P4
            slack: '@slackAlertChannel@-@tier@'
          annotations:
            summary: 'Free space is low @ inventory-replica-db001-live'
            description: 'The DB free space is less than the threshold. (current value: {{ humanize $value }})'
            dashboard: 'https://grafana.live-k8s.hellofresh.io/d/FuMo_ZEGk/csku-inventory-forecast?orgId=1&viewPanel=133&refresh=5m'

        - alert: '@projectName.upperCamelCase@LowFreeSpaceReplica'
          expr: >
            sum(node_filesystem_avail_bytes{ job="cif-csku-inventory-forecast-rds-prom-exporter-k8s",instance="inventory-replica-db002-live"}) /
            sum(node_filesystem_size_bytes{ job="cif-csku-inventory-forecast-rds-prom-exporter-k8s",instance="inventory-replica-db002-live"}) < 0.3
          for: '5m'
          labels:
            severity: P4
            slack: '@slackAlertChannel@-@tier@'
          annotations:
            summary: 'Free space is low @ inventory-replica-db002-live'
            description: 'The DB free space is less than the threshold. (current value: {{ humanize $value }})'
            dashboard: 'https://grafana.live-k8s.hellofresh.io/d/FuMo_ZEGk/csku-inventory-forecast?orgId=1&viewPanel=133&refresh=5m'

        -   alert: '@projectName.upperCamelCase@SQSMessageProcessingLag'
            expr: 'sum by (queue_name) (aws_sqs_approximate_number_of_messages_visible_average{service="cif-csku-inventory-forecast-prometheus-cloudwatch-exporter"}) > 0'
            for: '10m'
            labels:
                slack: '@slackAlertChannel@-@tier@'
                severity: 'P2'
            annotations:
                summary: 'SQS {{ $labels.queue_name }} was not processed for last 10 mins'
                description: 'SQS {{ $labels.queue_name }} was not processed for last 10 mins'
