---
rds-prom-exporter:
  environment: '@tier@'
  tribe: '@tribe@'
  squad: '@squad@'

  config:
    - instance: 'inventory-replica-db001-live'
      RDSDiskWillFillInXHours: { }
      RDSConnectionNumberHigh: { }
      RDSDiskIsXPercentFull: { }
      RDSCPUUsageHigh:
        threshold: 90
        for: 5m
      RDSMemoryUsageHigh: { }
      RDSDiskReadLatencyIncrease: { }

    - instance: 'inventory-replica-db002-live'
      RDSDiskWillFillInXHours: { }
      RDSConnectionNumberHigh: { }
      RDSDiskIsXPercentFull: { }
      RDSCPUUsageHigh:
        threshold: 90
        for: 5m
      RDSMemoryUsageHigh: { }
      RDSDiskReadLatencyIncrease: { }
