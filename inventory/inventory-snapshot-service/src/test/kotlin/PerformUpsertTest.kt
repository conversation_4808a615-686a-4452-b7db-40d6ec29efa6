package com.hellofresh.inventory.snapshot

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_SNAPSHOT_RAW
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_SNAPSHOT_RAW_SKU
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventorySnapshotRawRecord
import com.hellofresh.cif.inventory.snapshot.schema.tables.records.InventorySnapshotRawSkuRecord
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotKey
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotValue
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.temporal.ChronoUnit
import java.util.concurrent.Executors
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class PerformUpsertTest {
    private lateinit var dsl: MetricsDSLContext
    private lateinit var update: PerformUpsert

    @BeforeEach
    fun setUp() {
        dsl = DSL.using(
            DefaultConfiguration().apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            },
        ).withMetrics(SimpleMeterRegistry())
        update = PerformUpsert(dsl)
    }

    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(INVENTORY_SNAPSHOT_RAW).execute()
    }

    @Test
    fun `insert new rows`() {
        val crList = (0..2).map { i -> generateTestData(i) }
        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to crList))
        runBlocking { update(consumerRecords) }

        val rawRecords = dsl.fetch(INVENTORY_SNAPSHOT_RAW).sortedBy { it.dcCode }
        val skuRecords = dsl.fetch(INVENTORY_SNAPSHOT_RAW_SKU).sortedBy { it.dcCode }

        assertEquals(3, rawRecords.map { it.snapshotId }.distinct().count())
        assertEquals(3, skuRecords.map { it.snapshotId }.distinct().count())
        assertEquals(rawRecords.map { it.snapshotId }.toSet(), skuRecords.map { it.snapshotId }.toSet())
        rawRecords.forEachIndexed { i, r ->
            assertEquals(crList[i].key(), crList[i].value(), r, skuRecords.first { it.snapshotId == r.snapshotId })
        }
    }

    @Test
    fun `Records with same values are not inserted`() {
        val input = generateTestData(0)

        runBlocking { update(ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(input)))) }

        val rawRecords1 = dsl.fetch(INVENTORY_SNAPSHOT_RAW).sortedBy { it.dcCode }
        val skuRecords1 = dsl.fetch(INVENTORY_SNAPSHOT_RAW_SKU).sortedBy { it.dcCode }
        assertEquals(1, rawRecords1.size)
        assertEquals(1, skuRecords1.size)

        runBlocking { update(ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(input)))) }
        val rawRecords2 = dsl.fetch(INVENTORY_SNAPSHOT_RAW).sortedBy { it.dcCode }
        val skuRecords2 = dsl.fetch(INVENTORY_SNAPSHOT_RAW_SKU).sortedBy { it.dcCode }

        assertEquals(1, rawRecords2.size)
        assertEquals(1, skuRecords1.size)
        assertEquals(rawRecords2[0].createdAt, rawRecords1[0].createdAt)
        assertEquals(skuRecords1[0].createdAt, skuRecords2[0].createdAt)
    }

    private fun assertEquals(
        k: InventorySnapshotKey,
        v: InventorySnapshotValue,
        record: InventorySnapshotRawRecord,
        skuRecord: InventorySnapshotRawSkuRecord
    ) {
        assertEquals(k.skuCode, skuRecord.skuCode)
        assertEquals(v.stockState, skuRecord.state)
        assertEquals(k.dcCode, record.dcCode)
        assertEquals(
            v.snapshotTime.toInstant().truncatedTo(ChronoUnit.MILLIS),
            record.snapshotTime.toInstant().truncatedTo(ChronoUnit.MILLIS)
        )
        assertEquals(v.snapshotId, record.snapshotId)
        assertEquals(
            v.expirationTime?.toInstant()?.truncatedTo(ChronoUnit.MILLIS),
            skuRecord.expirationTimestamp.toInstant().truncatedTo(ChronoUnit.MILLIS)
        )
        assertEquals(v.quantity, skuRecord.quantity)
        assertEquals(v.locationId, skuRecord.locationId)
        assertEquals(v.locationType.name, skuRecord.locationType)
        assertEquals(v.transportModuleId, skuRecord.transportModuleId)
        assertEquals(v.messageCount, record.messageCount)
        assertEquals(v.poReference, skuRecord.poReference)
    }

    companion object {
        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)
    }
}
