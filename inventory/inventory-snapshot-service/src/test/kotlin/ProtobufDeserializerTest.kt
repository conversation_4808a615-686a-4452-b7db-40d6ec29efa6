package com.hellofresh.inventory.snapshot

import com.google.protobuf.Timestamp
import com.google.type.Decimal
import com.hellofresh.dateUtil.models.toOffsetDateTime
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotKeyDeserializer
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotValueDeserializer
import com.hellofresh.inventory.snapshot.deserializer.MESSAGE_COUNT_HEADER
import com.hellofresh.proto.shared.distributionCenter.inventory.v1.LocationType.LOCATION_TYPE_INBOUND
import com.hellofresh.proto.shared.distributionCenter.inventory.v1.StockState.STOCK_STATE_ACTIVE
import com.hellofresh.proto.stream.distributionCenter.inventory.snapshot.v1.InventorySnapshotKey
import com.hellofresh.proto.stream.distributionCenter.inventory.snapshot.v1.InventorySnapshotValue
import com.hellofresh.proto.stream.distributionCenter.inventory.snapshot.v1.InventorySnapshotValue.InventorySnapshot
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.apache.kafka.common.header.internals.RecordHeaders
import org.junit.jupiter.api.assertThrows

class ProtobufDeserializerTest {
    @Test
    fun `InventorySnapshotKey and InventorySnapshotValue are deserialized`() {
        val recordKey = createKey()
        val recordValue = createValue()

        val key = recordKey.toByteArray()
        val value = recordValue.toByteArray()
        val expectedMessageCount = 123
        val messageCountHeaders = RecordHeaders().add(
            MESSAGE_COUNT_HEADER,
            expectedMessageCount.toString().toByteArray(Charsets.UTF_8)
        )
        with(InventorySnapshotKeyDeserializer.deserialize("", key)) {
            assertEquals(recordKey.distributionCenterCode, dcCode)
            assertEquals(recordKey.skuCode, skuCode)
        }
        with(InventorySnapshotValueDeserializer.deserialize("", messageCountHeaders, value)) {
            assertEquals(recordValue.inventorySnapshot.snapshotId, snapshotId.toString())
            assertEquals(recordValue.snapshotTime.toOffsetDateTime(), snapshotTime)
            assertEquals(recordValue.stockState.name, stockState)
            assertEquals(recordValue.inventorySnapshot.quantity.value.toBigDecimal(), quantity)
            assertEquals(recordValue.expirationTime.toOffsetDateTime(), expirationTime)
            assertEquals(recordValue.inventorySnapshot.locationType.name, locationType.name)
            assertEquals(expectedMessageCount, messageCount)
        }
    }

    @Test
    fun `default protobuf expiry date timestamp is deserialized as null`() {
        val recordValue = createValue(Timestamp.getDefaultInstance())

        val value = recordValue.toByteArray()
        with(
            InventorySnapshotValueDeserializer.deserialize(
                "",
                RecordHeaders().add(MESSAGE_COUNT_HEADER, 1.toString().toByteArray(Charsets.UTF_8)),
                value
            )
        ) {
            assertNull(expirationTime)
        }
    }

    @Test
    fun `SkuDemandForecastKey can not be null`() {
        assertThrows<IllegalStateException> {
            InventorySnapshotKeyDeserializer.deserialize("", null)
        }
    }

    @Test
    fun `null SkuDemandForecastVal can not be null`() {
        assertThrows<IllegalStateException> {
            assertNull(InventorySnapshotValueDeserializer.deserialize("", null, null as ByteArray?))
        }
    }

    private fun createKey() = InventorySnapshotKey.newBuilder().apply {
        distributionCenterCode = UUID.randomUUID().toString()
        skuCode = UUID.randomUUID().toString()
    }.build()

    private fun createValue(expiration: Timestamp? = null) = InventorySnapshotValue.newBuilder().apply {
        snapshotTime = Timestamp.newBuilder().apply {
            seconds = OffsetDateTime.now(UTC).toEpochSecond()
            nanos = OffsetDateTime.now(UTC).nano
        }.build()
        inventorySnapshot = InventorySnapshot.newBuilder().apply {
            snapshotId = UUID.randomUUID().toString()
            quantity = Decimal.newBuilder().setValue("100").build()
            locationType = LOCATION_TYPE_INBOUND
        }.build()
        expirationTime = expiration ?: Timestamp.newBuilder().apply {
            seconds = OffsetDateTime.now(UTC).toEpochSecond()
            nanos = OffsetDateTime.now(UTC).nano
        }.build()
        stockState = STOCK_STATE_ACTIVE
    }.build()
}
