package com.hellofresh.inventory.snapshot.job

import com.hellofresh.inventory.snapshot.repository.DcSnapshotInfo
import com.hellofresh.inventory.snapshot.repository.InventoryRawSnapshot
import com.hellofresh.inventory.snapshot.repository.InventorySnapshotInfo
import com.hellofresh.inventory.snapshot.repository.SkuInventoryRawSnapshot
import io.mockk.mockk
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class InventorySnapshotResolverTest {

    private val defaultDcCode = "DC"
    private val youFoodzDcCode = "YC"

    @Test
    fun `inventory snapshot is returned if its complete`() {
        val snapshotID1 = UUID.randomUUID()
        val snapshotIdTime = OffsetDateTime.now(ZoneOffset.UTC)

        val inventorySnapshotInfo = InventorySnapshotInfo(snapshotID1, snapshotIdTime, true)

        val inventoryRawSnapshotMap = mapOf(
            snapshotID1 to InventoryRawSnapshot(
                snapshotID1, snapshotIdTime, 2,
                mapOf(
                    defaultDcCode to listOf(mockk<SkuInventoryRawSnapshot>(), mockk<SkuInventoryRawSnapshot>()),
                ),
            ),
        )

        val snapshotDataToBeProcessed = InventorySnapshotResolver.getCompletedInventorySnapshots(
            defaultDcCode,
            inventorySnapshotInfo,
        ) { inventoryRawSnapshotMap[it] }

        assertNotNull(snapshotDataToBeProcessed)
        assertEquals(snapshotID1, snapshotDataToBeProcessed.snapshotId)
        assertEquals(snapshotIdTime, snapshotDataToBeProcessed.snapshotTimestamp)
        assertEquals(
            inventoryRawSnapshotMap[snapshotID1]!!.dcSnapshots[defaultDcCode],
            snapshotDataToBeProcessed.skuInventorySnapshots,
        )
    }

    @Test
    fun `inventory snapshot is returned , where yf collision identified and its is complete`() {
        val snapshotID1 = UUID.randomUUID()
        val snapshotIdTime = OffsetDateTime.now(ZoneOffset.UTC)

        val completedInventorySnapshot1 = InventorySnapshotInfo(snapshotID1, snapshotIdTime, true)

        val inventoryRawSnapshotMap = mapOf(
            snapshotID1 to InventoryRawSnapshot(
                snapshotID1, snapshotIdTime, 2,
                mapOf(
                    defaultDcCode to listOf(mockk<SkuInventoryRawSnapshot>(), mockk<SkuInventoryRawSnapshot>()),
                    youFoodzDcCode to listOf(mockk<SkuInventoryRawSnapshot>(), mockk<SkuInventoryRawSnapshot>()),
                ),
            ),
        )

        val snapshotDataToBeProcessed = InventorySnapshotResolver.getCompletedInventorySnapshots(
            defaultDcCode,
            completedInventorySnapshot1,
        ) { inventoryRawSnapshotMap[it] }

        assertNotNull(snapshotDataToBeProcessed)
        assertEquals(snapshotID1, snapshotDataToBeProcessed.snapshotId)
        assertEquals(snapshotIdTime, snapshotDataToBeProcessed.snapshotTimestamp)
        assertEquals(
            inventoryRawSnapshotMap[snapshotID1]!!.dcSnapshots[defaultDcCode],
            snapshotDataToBeProcessed.skuInventorySnapshots,
        )
        assertEquals(
            defaultDcCode,
            snapshotDataToBeProcessed.dcCode,
        )
    }

    @Test
    fun `inventory snapshot is returned if we have at least 2 raw snapshots recorded and it is not complete`() {
        val snapshotID1 = UUID.randomUUID()
        val snapshotIdTime = OffsetDateTime.now(ZoneOffset.UTC)
        val snapshotID2 = UUID.randomUUID()
        val snapshotIdTime2 = OffsetDateTime.now(ZoneOffset.UTC).minusMinutes(10)

        val incompleteInventorySnapshot = InventorySnapshotInfo(snapshotID1, snapshotIdTime, true)
        val completedInventorySnapshot = InventorySnapshotInfo(snapshotID2, snapshotIdTime2, true)

        val inventoryRawSnapshotMap = mapOf(
            snapshotID1 to InventoryRawSnapshot(
                snapshotID1, snapshotIdTime, 2,
                mapOf(
                    defaultDcCode to listOf(mockk<SkuInventoryRawSnapshot>()),
                ),
            ),
            snapshotID2 to InventoryRawSnapshot(
                snapshotID2, snapshotIdTime2, 2,
                mapOf(
                    defaultDcCode to listOf(mockk<SkuInventoryRawSnapshot>(), mockk<SkuInventoryRawSnapshot>()),
                ),
            ),
        )

        val snapshotDataToBeProcessed = InventorySnapshotResolver.getCompletedInventorySnapshots(
            defaultDcCode,
            completedInventorySnapshot,
        ) { inventoryRawSnapshotMap[it] }

        assertNotNull(snapshotDataToBeProcessed)
        assertEquals(snapshotID2, snapshotDataToBeProcessed.snapshotId)
        assertEquals(snapshotIdTime2, snapshotDataToBeProcessed.snapshotTimestamp)
        assertEquals(
            inventoryRawSnapshotMap[snapshotID2]!!.dcSnapshots[defaultDcCode],
            snapshotDataToBeProcessed.skuInventorySnapshots,
        )

        val incompleteSnapshotData = InventorySnapshotResolver.getCompletedInventorySnapshots(
            defaultDcCode,
            incompleteInventorySnapshot,
        ) { inventoryRawSnapshotMap[it] }

        assertNull(incompleteSnapshotData)
    }

    @Test
    fun `no inventory snapshot candidate if there are less than 2 raw snapshots recorded when latest is complete`() {
        val snapshotID1 = UUID.randomUUID()
        val snapshotIdTime = OffsetDateTime.now(ZoneOffset.UTC)

        val dcSnapshotsInfo = DcSnapshotInfo(
            defaultDcCode,
            listOf(
                InventorySnapshotInfo(snapshotID1, snapshotIdTime, true),
            ),
        )

        val inventoryRawSnapshotMap = mapOf(
            snapshotID1 to InventoryRawSnapshot(snapshotID1, snapshotIdTime, 1, emptyMap()),
        )

        dcSnapshotsInfo.pendingInventorySnapshotsInfo.map { pendingInventorySnapshotInfo ->
            val snapshotDataToBeProcessed = InventorySnapshotResolver.getCompletedInventorySnapshots(
                defaultDcCode,
                pendingInventorySnapshotInfo,
            ) { inventoryRawSnapshotMap[it] }

            assertNull(snapshotDataToBeProcessed)
        }
    }

    @Test
    fun `no inventory snapshot candidate if there are less than 3 raw snapshots recorded when skipping latest`() {
        val snapshotID1 = UUID.randomUUID()
        val snapshotIdTime = OffsetDateTime.now(ZoneOffset.UTC)
        val snapshotID2 = UUID.randomUUID()
        val snapshotIdTime2 = snapshotIdTime.minusMinutes(10)

        val dcSnapshotsInfo = DcSnapshotInfo(
            defaultDcCode,
            listOf(
                InventorySnapshotInfo(snapshotID1, snapshotIdTime, true),
                InventorySnapshotInfo(snapshotID2, snapshotIdTime2, true),
            ),
        )

        val inventoryRawSnapshotMap = mapOf(
            snapshotID1 to InventoryRawSnapshot(snapshotID1, snapshotIdTime, null, emptyMap()),
        )
        dcSnapshotsInfo.pendingInventorySnapshotsInfo.map { pendingInventorySnapshotInfo ->
            val snapshotDataToBeProcessed = InventorySnapshotResolver.getCompletedInventorySnapshots(
                defaultDcCode,
                pendingInventorySnapshotInfo,
            ) { inventoryRawSnapshotMap[it] }

            assertNull(snapshotDataToBeProcessed)
        }
    }
}
