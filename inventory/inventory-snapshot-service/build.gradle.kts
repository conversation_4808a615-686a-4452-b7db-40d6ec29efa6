plugins {
    id("com.hellofresh.cif.application-conventions")
    alias(libs.plugins.jooq)
    hellofresh.`test-fixtures`
}

description = "Dumps the raw inventory snapshots into DB."
group = "$group.inventory.snapshot"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "inventory_snapshot_raw|inventory_snapshot_raw_sku|inventory_snapshot|inventory_all_snapshots|" +
                            "dc_config|inventory_processed_snapshots|file_uploads|file_upload_status|file_type"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(projects.inventoryModels)
    implementation(projects.distributionCenterLib)
    implementation(projects.skuInputsLib)
    implementation(projects.skuSpecificationLib)
    implementation(projects.lib.db)
    implementation(projects.dateUtilModels)
    implementation(projects.lib.sqs)
    implementation(projects.lib.s3)
    implementation(projects.lib)
    implementation(projects.safetyStock.safetyStockLib)
    implementation(projects.lib.featureflags)
    implementation(projects.purchaseOrder.purchaseOrderModels)
    implementation(libs.apache.commonscsv)
    implementation(libs.coroutines.core)
    implementation(libs.protobuf.grpc)

    testImplementation(libs.testcontainers.core)
    testImplementation(libs.testcontainers.postgresql)
    testImplementation(libs.testcontainers.kafka)
    testImplementation(libs.testcontainers.junit)
    testImplementation(projects.libTests)
    testImplementation(libs.mockk)
    testImplementation(testFixtures(projects.inventoryModels))
    testImplementation(testFixtures(projects.distributionCenterModels))
    testImplementation(testFixtures(projects.inventory.inventorySnapshotService))
    testImplementation(testFixtures(projects.lib.featureflags))
    testFixturesImplementation(projects.inventoryModels)
}
