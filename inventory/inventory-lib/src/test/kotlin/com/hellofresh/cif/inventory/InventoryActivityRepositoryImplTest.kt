package com.hellofresh.cif.inventory

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.inventory.InventoryActivityRepository.InventoryActivityEventType
import com.hellofresh.cif.inventory.lib.schema.Tables.DC_CONFIG
import com.hellofresh.cif.inventory.lib.schema.Tables.INVENTORY_ACTIVITY
import com.hellofresh.cif.inventory.lib.schema.enums.InventoryActivityType.ADJ
import com.hellofresh.cif.inventory.lib.schema.enums.InventoryActivityType.MOV
import com.hellofresh.cif.inventory.lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.inventory.lib.schema.tables.records.InventoryActivityRecord
import com.hellofresh.cif.models.DateTimeRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.InventoryAdjustment
import com.hellofresh.inventory.models.InventoryAdjustmentTypeId.PCK
import com.hellofresh.inventory.models.InventoryAdjustmentValue
import com.hellofresh.inventory.models.InventoryMovement
import com.hellofresh.inventory.models.InventoryMovementValue
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_PRODUCTION
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_UNSPECIFIED
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class InventoryActivityRepositoryImplTest {
    private val newDcConfigRecord = DcConfigRecord(
        "VE", "DACH", "MONDAY", "FRIDAY", "Europe/Berlin",
        true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, LocalTime.now(), null, LocalTime.now()
    )

    @BeforeEach
    fun initData() {
        dsl.deleteFrom(DC_CONFIG).execute()
        dsl.deleteFrom(INVENTORY_ACTIVITY).execute()
    }

    @Test
    fun `should fetch Inventory Adjustments for the given list of dcs and date ranges`() {
        val inventoryActivityRecord = InventoryActivityRecord().apply {
            hashMessage = "0"
            activityId = UUID.randomUUID()
            activityTime = OffsetDateTime.now(UTC)
            dcCode = "VE"
            skuId = UUID.randomUUID()
            type = ADJ
            typeId = PCK.name
            value = JSONB.valueOf(
                objectMapper.writeValueAsString(
                    InventoryAdjustmentValue(
                        quantity = SkuQuantity.fromBigDecimal(BigDecimal(-100)), locationId = "adj-location-1",
                        locationType = LOCATION_TYPE_PRODUCTION,
                        expirationDate = null, remainingQuantity = SkuQuantity.fromBigDecimal(BigDecimal.ZERO),
                        transportModuleId = "adjTmId", poNumber = "poN",
                    ),
                ),
            )
            publishedTime = OffsetDateTime.now(UTC)
        }

        dsl.batchInsert(newDcConfigRecord, inventoryActivityRecord).execute()

        runBlocking {
            val records = inventoryActivityRepository.fetchInventoryActivity(
                setOf("VE"),
                DateTimeRange(
                    fromDateTime = inventoryActivityRecord.activityTime.minusDays(1),
                    toDateTime = inventoryActivityRecord.activityTime.withOffsetSameInstant(ZoneOffset.ofHours(-1)),
                ),
            )
            assertEquals(1, records.size)
            val inventoryAdjustment: InventoryAdjustment = records.first() as InventoryAdjustment
            with(inventoryAdjustment) {
                assertEquals(inventoryActivityRecord.activityId, activityId)
                assertEquals(
                    inventoryActivityRecord.activityTime.toLocalDate(),
                    activityTime.toLocalDate(),
                )
                assertEquals(inventoryActivityRecord.dcCode, dcCode)
                assertEquals(inventoryActivityRecord.skuId, skuId)
                assertEquals(inventoryActivityRecord.typeId, typeId)
                assertEquals(inventoryActivityRecord.type, ADJ)
                assertEquals(BigDecimal(-100), inventoryAdjustment.quantity.getValue())
                assertEquals("adj-location-1", inventoryAdjustment.locationId)
                assertEquals(LOCATION_TYPE_PRODUCTION, inventoryAdjustment.locationType)
                assertEquals("adjTmId", inventoryAdjustment.transportModuleId)
                assertEquals("poN", inventoryAdjustment.poNumber)
                assertEquals(BigDecimal.ZERO, inventoryAdjustment.remainingQuantity.getValue())
                assertNull(inventoryAdjustment.expirationDate)
            }
        }
    }

    @Test
    fun `should fetch Inventory Movements for the given list of dcs and date ranges`() {
        val inventoryMovementValue = createInventoryMovementValue()
        val inventoryMovementRecord = createInventoryMovementRecord(inventoryMovementValue)
        dsl.batchInsert(newDcConfigRecord, inventoryMovementRecord).execute()

        runBlocking {
            val records = inventoryActivityRepository.fetchInventoryActivity(
                setOf("VE"),
                DateTimeRange(
                    fromDateTime = OffsetDateTime.now(UTC).minusDays(1),
                    toDateTime = OffsetDateTime.now(UTC),
                ),
            )
            assertEquals(1, records.size)
            val inventoryMovement: InventoryMovement = records.first() as InventoryMovement
            inventoryMovement.apply {
                assertEquals(inventoryMovementRecord.activityId, activityId)
                assertEquals(inventoryMovementRecord.activityTime.toLocalDate(), activityTime.toLocalDate())
                assertEquals(inventoryMovementRecord.dcCode, dcCode)
                assertEquals(inventoryMovementRecord.skuId, skuId)
                assertEquals(inventoryMovementRecord.typeId, typeId)
                assertEquals(inventoryMovementRecord.type, MOV)
                assertEquals(inventoryMovementValue.quantity, inventoryMovement.quantity)
                assertEquals(inventoryMovementValue.originLocationId, inventoryMovement.originLocationId)
                assertEquals(inventoryMovementValue.originLocationType, inventoryMovement.originLocationType)
                assertEquals(inventoryMovementValue.destinationLocationId, inventoryMovement.destinationLocationId)
                assertEquals(inventoryMovementValue.destinationLocationType, inventoryMovement.destinationLocationType)
                assertEquals(inventoryMovementValue.transportModuleId, inventoryMovement.transportModuleId)
                assertEquals(inventoryMovementValue.remainingQuantity, inventoryMovement.remainingQuantity)
                assertEquals(inventoryMovementValue.poNumber, inventoryMovement.poNumber)
                assertNull(inventoryMovement.expirationDate)
            }
        }
    }

    @Test
    fun `should fetch empty Inventory Movements for the given date not in range`() {
        dsl.batchInsert(createInventoryMovementRecord()).execute()
        runBlocking {
            val records = inventoryActivityRepository.fetchInventoryActivity(
                setOf("VE"),
                DateTimeRange(
                    fromDateTime = OffsetDateTime.now(UTC).minusDays(10),
                    toDateTime = OffsetDateTime.now(UTC).minusDays(2),
                ),
            )
            assertEquals(0, records.size)
        }
    }

    @Test
    fun `should fetch activity for the given list of dcs and date ranges and event type and type ID`() {
        val inventoryActivityRecord = getInventoryActivityRecord()

        dsl.batchInsert(
            newDcConfigRecord,
            inventoryActivityRecord,
            createInventoryMovementRecord().apply {
                type = MOV
            },
        ).execute()

        runBlocking {
            val records = inventoryActivityRepository.fetchInventoryActivity(
                setOf("VE"),
                DateTimeRange(
                    fromDateTime = OffsetDateTime.now(UTC).minusDays(1),
                    toDateTime = OffsetDateTime.now(UTC),
                ),
                InventoryActivityRepository.InventoryActivityEventType(
                    inventoryActivityRecord.type,
                    setOf(inventoryActivityRecord.typeId)
                ),
            )
            assertEquals(1, records.size)
            val inventoryAdjustment: InventoryAdjustment = records.first() as InventoryAdjustment
            with(inventoryAdjustment) {
                assertEquals(inventoryActivityRecord.activityId, activityId)
                assertEquals(inventoryActivityRecord.dcCode, dcCode)
                assertEquals(inventoryActivityRecord.typeId, typeId)
                assertEquals(inventoryActivityRecord.type, ADJ)
            }
        }
    }

    @Test
    fun `should fetch activity for the given list of dcs and date ranges and event type`() {
        val inventoryActivityRecord1 = getInventoryActivityRecord()
        val inventoryActivityRecord2 = getInventoryActivityRecord().apply {
            type = inventoryActivityRecord1.type
        }

        dsl.batchInsert(newDcConfigRecord, inventoryActivityRecord1, inventoryActivityRecord2).execute()

        runBlocking {
            val records = inventoryActivityRepository.fetchInventoryActivity(
                setOf("VE"),
                DateTimeRange(
                    fromDateTime = OffsetDateTime.now(UTC).minusDays(1),
                    toDateTime = OffsetDateTime.now(UTC),
                ),
                InventoryActivityEventType(inventoryActivityRecord1.type),
            )
            assertEquals(2, records.size)

            assertEquals(
                setOf(inventoryActivityRecord1.activityId, inventoryActivityRecord2.activityId),
                records.map { it.activityId }.toSet(),
            )
        }
    }

    @Test
    fun `should fetch activity for the given list of dcs and date ranges and event type ids`() {
        val inventoryActivityRecord1 = getInventoryActivityRecord()
        val inventoryActivityRecord2 = createInventoryMovementRecord().apply {
            type = com.hellofresh.cif.inventory.lib.schema.enums.InventoryActivityType.entries.first {
                it != inventoryActivityRecord1.type
            }
            typeId = UUID.randomUUID().toString()
        }

        dsl.batchInsert(newDcConfigRecord, inventoryActivityRecord1, inventoryActivityRecord2).execute()

        runBlocking {
            val records = inventoryActivityRepository.fetchInventoryActivity(
                setOf("VE"),
                DateTimeRange(
                    fromDateTime = OffsetDateTime.now(UTC).minusDays(1),
                    toDateTime = OffsetDateTime.now(UTC),
                ),
                InventoryActivityEventType(
                    null,
                    setOf(inventoryActivityRecord1.typeId, inventoryActivityRecord2.typeId)
                ),
            )
            assertEquals(2, records.size)
        }
    }

    private fun getInventoryActivityRecord() = InventoryActivityRecord().apply {
        hashMessage = UUID.randomUUID().toString()
        activityId = UUID.randomUUID()
        activityTime = OffsetDateTime.now(UTC)
        dcCode = "VE"
        skuId = UUID.randomUUID()
        type = ADJ
        typeId = PCK.name
        value = JSONB.valueOf(
            objectMapper.writeValueAsString(
                InventoryAdjustmentValue(
                    quantity = SkuQuantity.fromBigDecimal(BigDecimal(-100)), locationId = "adj-location-1", locationType = LOCATION_TYPE_PRODUCTION,
                    expirationDate = null, remainingQuantity = SkuQuantity.fromBigDecimal(BigDecimal.ZERO), transportModuleId = "adjTmId", poNumber = null,
                ),
            ),
        )
        publishedTime = OffsetDateTime.now(UTC)
    }

    private fun createInventoryMovementRecord(inventoryMovementValue: InventoryMovementValue = createInventoryMovementValue()) =
        InventoryActivityRecord().apply {
            hashMessage = UUID.randomUUID().toString()
            activityId = UUID.randomUUID()
            activityTime = OffsetDateTime.now(UTC)
            dcCode = newDcConfigRecord.dcCode
            skuId = UUID.randomUUID()
            type = MOV
            typeId = PCK.name
            value = JSONB.valueOf(
                objectMapper.writeValueAsString(inventoryMovementValue),
            )
            publishedTime = OffsetDateTime.now(UTC)
        }

    private fun createInventoryMovementValue() = InventoryMovementValue(
        quantity = SkuQuantity.fromBigDecimal(BigDecimal(100)),
        originLocationId = "origin-mov-location-1",
        originLocationType = LOCATION_TYPE_UNSPECIFIED,
        destinationLocationId = "mov-dest-location-1",
        destinationLocationType = LOCATION_TYPE_PRODUCTION,
        expirationDate = null,
        remainingQuantity = SkuQuantity.fromBigDecimal(BigDecimal.ZERO),
        transportModuleId = "movTmId",
        poNumber = "poNumber",
    )

    companion object {
        lateinit var inventoryActivityRepository: InventoryActivityRepository
        lateinit var dsl: MetricsDSLContext
        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)
        private val objectMapper = ObjectMapper().findAndRegisterModules()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            inventoryActivityRepository = InventoryActivityRepositoryImpl(dsl)
        }
    }
}
