package com.hellofresh.cif.inventorylivejob.repository

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.inventory_live_job.schema.Tables.INVENTORY_LIVE_SNAPSHOT
import com.hellofresh.cif.inventory_live_job.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.inventory_live_job.schema.tables.records.InventoryLiveSnapshotRecord
import com.hellofresh.cif.inventorylivejob.FunctionalTest
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.InventoryValue
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import com.hellofresh.inventory.models.inventory.live.default
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals

class InventoryLiveSnapshotRepositoryImplTest : FunctionalTest() {
    private val defaultZoneId = ZoneId.of("Europe/Berlin")
    private val defaultInventoryLiveSnapshot = LiveInventorySnapshot.Companion.default()
        .copy(snapshotTime = LocalDateTime.now(defaultZoneId))

    @Test
    fun `should fetch Inventory Live Snapshots for the given list of dcs and date ranges`() {
        val inventoryLiveSnapshot = defaultInventoryLiveSnapshot.copy(
            dcCode = "VE",
            snapshotTime = defaultInventoryLiveSnapshot.snapshotTime.plusDays(1),
            skus = listOf(
                SkuLiveInventory(
                    skuId = UUID.randomUUID(),
                    inventory = defaultInventoryLiveSnapshot.skus.flatMap {
                        it.inventory.map { it.copy(qty = SkuQuantity.fromBigDecimal(BigDecimal(1000L))) }
                    },
                    cleardownTime = LocalDateTime.now(defaultZoneId),
                ),
            ),
        )
        val inventoryLiveRecord1 = createInventoryLiveSnapshotRecord(defaultInventoryLiveSnapshot, defaultZoneId)
        val inventoryLiveRecord2 = createInventoryLiveSnapshotRecord(inventoryLiveSnapshot, defaultZoneId)
        dsl.batchInsert(
            createDcConfigRecord(defaultInventoryLiveSnapshot.dcCode),
            createDcConfigRecord(inventoryLiveSnapshot.dcCode),
            inventoryLiveRecord1,
            inventoryLiveRecord2,
        ).execute()

        // when
        runBlocking {
            val result =
                inventoryLiveSnapshotRepository.fetchLiveInventorySnapshot(
                    setOf(defaultInventoryLiveSnapshot.dcCode, inventoryLiveSnapshot.dcCode),
                    DateRange(
                        defaultInventoryLiveSnapshot.snapshotTime.toLocalDate(),
                        inventoryLiveSnapshot.snapshotTime.toLocalDate(),
                    ),
                )

            assertEquals(2, result.size)
            assertLiveSnapshot(
                defaultInventoryLiveSnapshot,
                result.first { it.dcCode == defaultInventoryLiveSnapshot.dcCode },
            )
            assertLiveSnapshot(inventoryLiveSnapshot, result.first { it.dcCode == inventoryLiveSnapshot.dcCode })
        }
    }

    private fun assertLiveSnapshot(expectedLiveInventorySnapshot: LiveInventorySnapshot, liveInventorySnapshot: LiveInventorySnapshot) {
        with(expectedLiveInventorySnapshot) {
            assertEquals(dcCode, liveInventorySnapshot.dcCode)
            assertEquals(snapshotId, liveInventorySnapshot.snapshotId)
            assertLocalDateTime(snapshotTime, liveInventorySnapshot.snapshotTime)
            assertEquals(skus.size, liveInventorySnapshot.skus.size)
            skus.forEach { expectedSku ->
                val sku = liveInventorySnapshot.skus.first { it.skuId == expectedSku.skuId }
                assertEquals(expectedSku.skuId, sku.skuId)
                assertEquals(expectedSku.inventory, sku.inventory)
                assertLocalDateTime(expectedSku.cleardownTime, sku.cleardownTime)
            }
        }
    }

    private fun assertLocalDateTime(expectedLocalDateTime: LocalDateTime?, localDateTime: LocalDateTime?) {
        assertEquals(
            expectedLocalDateTime?.truncatedTo(ChronoUnit.SECONDS),
            localDateTime?.truncatedTo(ChronoUnit.SECONDS),
        )
    }

    @Test
    fun `should fetch empty Inventory Live Snapshots for the given dates not within range`() {
        val inventoryLiveSnapshot = defaultInventoryLiveSnapshot.copy(
            dcCode = "dcCode2",
            snapshotTime = defaultInventoryLiveSnapshot.snapshotTime.plusDays(1),
            skus = listOf(
                SkuLiveInventory(
                    UUID.randomUUID(),
                    inventory = defaultInventoryLiveSnapshot.skus.flatMap {
                        it.inventory.map { it.copy(qty = SkuQuantity.fromBigDecimal(BigDecimal(1000L))) }
                    },
                ),
            ),
        )
        val inventoryLiveRecord1 = createInventoryLiveSnapshotRecord(defaultInventoryLiveSnapshot, defaultZoneId)
        val inventoryLiveRecord2 = createInventoryLiveSnapshotRecord(inventoryLiveSnapshot, defaultZoneId)
        dsl.batchInsert(
            createDcConfigRecord(defaultInventoryLiveSnapshot.dcCode),
            createDcConfigRecord(inventoryLiveSnapshot.dcCode),
            inventoryLiveRecord1,
            inventoryLiveRecord2,
        ).execute()

        // when
        runBlocking {
            val result =
                inventoryLiveSnapshotRepository.fetchLiveInventorySnapshot(
                    setOf(defaultInventoryLiveSnapshot.dcCode, inventoryLiveSnapshot.dcCode),
                    DateRange(
                        LocalDate.now().minusDays(10),
                        LocalDate.now().minusDays(2),
                    ),
                )

            assertEquals(0, result.size)
        }
    }

    @Test
    fun `should upsert the inventory live snapshot - delete the existing snapshot using dc & date`() {
        val zoneId = ZoneId.of(newDcConfigRecord.zoneId)
        val inventoryLiveRecord = createInventoryLiveSnapshotRecord(
            defaultInventoryLiveSnapshot
                .copy(
                    skus = listOf(
                        defaultInventoryLiveSnapshot.skus.first().copy(cleardownTime = LocalDateTime.now(zoneId)),
                    ),
                ),
            zoneId,
        )
        dsl.batchInsert(newDcConfigRecord, inventoryLiveRecord).execute()
        val newSnapshotId = UUID.randomUUID()
        val inventoryLiveSnapshotUpsert = LiveInventorySnapshot(
            dcCode = inventoryLiveRecord.dcCode,
            snapshotId = newSnapshotId,
            snapshotTime = inventoryLiveRecord.snapshotTime.withSecond(0).atZoneSameInstant(zoneId).toLocalDateTime(),
            skus = listOf(
                defaultInventoryLiveSnapshot.skus.first().copy(
                    cleardownTime = defaultInventoryLiveSnapshot.skus.first().cleardownTime?.plusHours(2),
                ),
            ),
        )
        runBlocking {
            inventoryLiveSnapshotRepository.upsertInventoryLiveSnapshot(listOf(inventoryLiveSnapshotUpsert))
        }

        val inventoryLiveSnapshotRecords = dsl.selectFrom(INVENTORY_LIVE_SNAPSHOT).fetch()

        assertEquals(1, inventoryLiveSnapshotRecords.size)
        assertLiveSnapshot(inventoryLiveSnapshotUpsert, inventoryLiveSnapshotRecords.first(), zoneId)
    }

    private fun assertLiveSnapshot(
        expectedLiveInventorySnapshot: LiveInventorySnapshot,
        inventoryLiveSnapshotRecord: InventoryLiveSnapshotRecord,
        zoneId: ZoneId
    ) {
        with(expectedLiveInventorySnapshot) {
            assertEquals(dcCode, inventoryLiveSnapshotRecord.dcCode)
            assertEquals(snapshotId, inventoryLiveSnapshotRecord.snapshotId)
            assertLocalDateTime(
                snapshotTime,
                inventoryLiveSnapshotRecord.snapshotTime.atZoneSameInstant(zoneId).toLocalDateTime(),
            )

            skus.first().also { expectedSku ->
                assertEquals(expectedSku.skuId, inventoryLiveSnapshotRecord.skuId)
                assertEquals(
                    expectedSku.inventory,
                    objectMapper.readValue<InventoryValue>(inventoryLiveSnapshotRecord.value.data()).inventory,
                )
                assertLocalDateTime(
                    expectedSku.cleardownTime,
                    inventoryLiveSnapshotRecord.cleardownTime?.atZoneSameInstant(zoneId)?.toLocalDateTime(),
                )
            }
        }
    }

    private fun createDcConfigRecord(dcCode: String) = DcConfigRecord(
        dcCode, "DACH", "MONDAY", "FRIDAY", defaultZoneId.id,
        true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null, LocalTime.now()
    )
}
