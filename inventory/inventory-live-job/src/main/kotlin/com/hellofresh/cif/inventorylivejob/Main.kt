package com.hellofresh.cif.inventorylivejob

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.demand.DemandRepositoryImpl
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.inventory.InventoryActivityRepositoryImpl
import com.hellofresh.cif.inventory.InventoryRepositoryImpl
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventory.LiveInventoryRepositoryImpl
import com.hellofresh.cif.inventorylivejob.job.InventoryLiveSnapshotJob
import com.hellofresh.cif.inventorylivejob.repository.impl.InventoryLiveSnapshotRepositoryImpl
import com.hellofresh.cif.inventorylivejob.service.ActivityInventoryProcessor
import com.hellofresh.cif.inventorylivejob.service.DemandProcessor
import com.hellofresh.cif.inventorylivejob.service.InventoryLiveSnapshotProcessor
import com.hellofresh.cif.lib.KrontabScheduler
import com.hellofresh.cif.lib.MeteredJob
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit.MINUTES
import org.apache.logging.log4j.kotlin.logger

private const val HTTP_PORT = 8081

private const val JOOQ_POOL_THREADS = 2
val logger = logger("com.hellofresh.cif.inventorylivejob.main")
private fun jobTimeMinutes(): Int = ConfigurationLoader.getStringOrFail("job.time_minutes").toInt()

private fun getBlockedDcs(): Set<String> = ConfigurationLoader.getList("dc.blocked").toSet()

fun main() {
    val meterRegistry = createMeterRegistry()

    val masterDslContext = DBConfiguration.jooqMasterDslContext(JOOQ_POOL_THREADS, meterRegistry)
    val readOnlyDslContext = DBConfiguration.jooqReadOnlyDslContext(JOOQ_POOL_THREADS, meterRegistry)

    val dcConfigService = DcConfigService(meterRegistry)
    val statsigFeatureFlagClient = StatsigFactory.build(
        ::shutdownHook,
        sdkKey = ConfigurationLoader.getStringOrFail("HF_STATSIG_SDK_KEY"),
        userId = ConfigurationLoader.getStringOrFail("application.name"),
        isOffline = ConfigurationLoader.getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
        hfTier = ConfigurationLoader.getStringOrFail("HF_TIER"),
    )

    val inventoryRepository = InventoryRepositoryImpl(readOnlyDslContext)
    val liveInventoryRepository = LiveInventoryRepositoryImpl(readOnlyDslContext)
    val inventoryActivityRepository = InventoryActivityRepositoryImpl(readOnlyDslContext)
    val inventoryService =
        InventoryService(
            inventoryRepository,
            liveInventoryRepository,
            inventoryActivityRepository,
            statsigFeatureFlagClient,
        )
    val inventorySnapshotJob = InventoryLiveSnapshotJob(
        dcConfigService,
        InventoryRepositoryImpl(readOnlyDslContext),
        InventoryLiveSnapshotRepositoryImpl(readOnlyDslContext, masterDslContext, dcConfigService),
        InventoryLiveSnapshotProcessor(
            ActivityInventoryProcessor(inventoryActivityRepository),
            DemandProcessor(
                DemandRepositoryImpl(readOnlyDslContext, statsigFeatureFlagClient),
                SkuInputDataRepositoryImpl(readOnlyDslContext, dcConfigService),
                statsigFeatureFlagClient,
            ),
            inventoryService,
            inventoryRepository,
        ),
        getBlockedDcs(),
    )

    shutdownNeeded {
        KrontabScheduler(
            period = jobTimeMinutes(),
            timeUnit = MINUTES,
            executor = Executors.newSingleThreadExecutor(),
        )
    }.schedule {
        logger.info("Starting inventory live job.")
        MeteredJob(meterRegistry, "inventory-live-job", inventorySnapshotJob::run).execute()
        logger.info("Finished inventory live job.")
    }

    StatusServer.run(
        meterRegistry,
        HTTP_PORT,
    )
}
