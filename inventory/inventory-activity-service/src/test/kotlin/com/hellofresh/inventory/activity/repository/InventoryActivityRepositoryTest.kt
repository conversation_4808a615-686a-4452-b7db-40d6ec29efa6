package com.hellofresh.inventory.activity.repository

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.inventory.activity.schema.Tables.INVENTORY_ACTIVITY
import com.hellofresh.cif.inventory.activity.schema.tables.records.InventoryActivityRecord
import com.hellofresh.inventory.activity.FunctionalTest
import com.hellofresh.inventory.models.InventoryAdjustment
import com.hellofresh.inventory.models.InventoryAdjustmentValue
import com.hellofresh.inventory.models.InventoryMovement
import com.hellofresh.inventory.models.InventoryMovementValue
import com.hellofresh.inventory.models.default
import java.time.ZoneOffset.UTC
import java.time.temporal.ChronoUnit.SECONDS
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class InventoryActivityRepositoryTest : FunctionalTest() {

    @Test
    fun `empty inventory activity doesnt save any value`() {
        runBlocking { inventoryActivityRepository.save(emptyList()) }
        assertEquals(0, dsl.fetchCount(INVENTORY_ACTIVITY))
    }

    @Test
    fun `inventory activities are persisted`() {
        val inventoryMovementMessage = InventoryActivityMessage(
            UUID.randomUUID().toString(),
            InventoryMovement.default(),
        )
        val inventoryAdjustmentMessage = InventoryActivityMessage(
            UUID.randomUUID().toString(),
            InventoryAdjustment.default(),
        )
        runBlocking { inventoryActivityRepository.save(listOf(inventoryMovementMessage, inventoryAdjustmentMessage)) }

        val records = dsl.selectFrom(INVENTORY_ACTIVITY).fetch()
        assertEquals(2, records.size)

        assertInventoryActivity(
            inventoryMovementMessage,
            records.first { it.hashMessage == inventoryMovementMessage.hash }
        )
        assertInventoryActivity(
            inventoryAdjustmentMessage,
            records.first { it.hashMessage == inventoryAdjustmentMessage.hash }
        )
    }

    @Test
    fun `inventory activities with same hash are omitted`() {
        val inventoryMovementMessage1 = InventoryActivityMessage(
            UUID.randomUUID().toString(),
            InventoryMovement.default(),
        )
        val inventoryMovementMessage2 = inventoryMovementMessage1.copy(
            inventoryActivity = InventoryAdjustment.default(),
        )
        runBlocking { inventoryActivityRepository.save(listOf(inventoryMovementMessage1)) }

        val records = dsl.selectFrom(INVENTORY_ACTIVITY).fetch()
        assertEquals(1, records.size)
        assertInventoryActivity(inventoryMovementMessage1, records.first())

        runBlocking { inventoryActivityRepository.save(listOf(inventoryMovementMessage2)) }
        val records2 = dsl.selectFrom(INVENTORY_ACTIVITY).fetch()
        assertEquals(1, records2.size)
        assertInventoryActivity(inventoryMovementMessage1, records2.first())
    }

    private fun assertInventoryActivity(
        inventoryAdjustmentMessage: InventoryActivityMessage,
        inventoryActivityRecord: InventoryActivityRecord
    ) {
        with(inventoryAdjustmentMessage.inventoryActivity) {
            assertEquals(inventoryAdjustmentMessage.hash, inventoryActivityRecord.hashMessage)
            assertEquals(activityId, inventoryActivityRecord.activityId)
            assertEquals(
                activityTime.atZoneSameInstant(UTC).truncatedTo(SECONDS),
                inventoryActivityRecord.activityTime.atZoneSameInstant(UTC).truncatedTo(SECONDS),
            )
            assertEquals(
                publishedTime.atZoneSameInstant(UTC).truncatedTo(SECONDS),
                inventoryActivityRecord.publishedTime.atZoneSameInstant(UTC).truncatedTo(SECONDS),
            )
            assertEquals(dcCode, inventoryActivityRecord.dcCode)
            assertEquals(skuId, inventoryActivityRecord.skuId)
            assertEquals(typeId, inventoryActivityRecord.typeId)
            when (this) {
                is InventoryMovement -> {
                    val movement = objectMapper.readValue<InventoryMovementValue>(inventoryActivityRecord.value.data())
                    assertEquals(quantity, movement.quantity)
                    assertEquals(originLocationId, movement.originLocationId)
                    assertEquals(originLocationType, movement.originLocationType)
                    assertEquals(destinationLocationId, movement.destinationLocationId)
                    assertEquals(destinationLocationType, movement.destinationLocationType)
                    assertEquals(remainingQuantity, movement.remainingQuantity)
                    assertEquals(transportModuleId, movement.transportModuleId)
                    assertEquals(poNumber, movement.poNumber)
                }

                is InventoryAdjustment -> {
                    val adjustment = objectMapper.readValue<InventoryAdjustmentValue>(
                        inventoryActivityRecord.value.data()
                    )
                    assertEquals(quantity, adjustment.quantity)
                    assertEquals(locationId, adjustment.locationId)
                    assertEquals(locationType, adjustment.locationType)
                    assertEquals(remainingQuantity, adjustment.remainingQuantity)
                    assertEquals(transportModuleId, adjustment.transportModuleId)
                    assertEquals(poNumber, adjustment.poNumber)
                }
            }
        }
    }
}
