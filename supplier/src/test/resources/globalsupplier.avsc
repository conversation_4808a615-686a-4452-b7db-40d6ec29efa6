{"name": "supplier", "namespace": "com.hellofresh.planning.supplier", "type": "record", "fields": [{"name": "id", "type": "string", "logicalType": "uuid"}, {"name": "name", "type": "string"}, {"name": "addresses", "type": {"type": "array", "items": {"name": "supplier_address", "type": "record", "fields": [{"name": "legal_address", "type": "boolean", "default": false, "doc": "One of those addresses is the legal address"}, {"name": "street", "type": "string"}, {"name": "city", "type": "string"}, {"name": "post_code", "type": "string"}, {"name": "region", "type": "string", "default": ""}, {"name": "country", "type": "string"}, {"name": "distribution_centers", "type": {"type": "array", "items": {"name": "dc_assignment", "type": "record", "fields": [{"name": "id", "type": "string", "logicalType": "uuid"}]}}, "default": []}]}}}]}