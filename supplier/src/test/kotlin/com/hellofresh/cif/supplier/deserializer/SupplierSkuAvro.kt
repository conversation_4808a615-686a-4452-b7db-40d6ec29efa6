package com.hellofresh.cif.supplier.deserializer

import io.confluent.kafka.schemaregistry.avro.AvroSchema
import io.confluent.kafka.schemaregistry.client.MockSchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroSerializer
import java.util.UUID
import org.apache.avro.generic.GenericData.EnumSymbol
import org.apache.avro.generic.GenericRecord
import org.apache.avro.generic.GenericRecordBuilder

object SupplierSkuAvro {
    private val supplySkuAvroSchema = SupplierSkuAvro::class.java.classLoader.getResourceAsStream(
        "suppliersku.avsc"
    )?.readAllBytes()?.let {
        String(it, Charsets.UTF_8)
    }
    private val supplierSkuAvroSchema = AvroSchema(supplySkuAvroSchema)
    private val supplierSkuAvroSerializer = KafkaAvroSerializer(MockSchemaRegistryClient()).apply {
        configure(mapOf("schema.registry.url" to "mock://localhost:0"), false)
    }
    val testSupplierSkuSchemaRegistryClient = MockSchemaRegistryClient().apply {
        register("test-topic", supplierSkuAvroSchema, 1, 1)
    }

    fun schemaRegistryClient(topics: List<String>) =
        MockSchemaRegistryClient().apply {
            topics.forEach { topic ->
                register(topic, supplierSkuAvroSchema, 1, 1)
            }
        }

    fun avroSupplierSku(
        id: UUID,
        supplierId: UUID,
        culinarySkuId: UUID,
        market: String,
        status: String
    ): ByteArray {
        val schema = supplierSkuAvroSchema.rawSchema()
        val builder = GenericRecordBuilder(schema)
        builder.apply {
            set("event_type", EnumSymbol(schema, "entity_created"))
            set("id", id.toString())
            set("supplier_code", 111474)
            set("culinary_sku_code", "SPI-00-11999-5")
            set("supplier_id", supplierId.toString())
            set("culinary_sku_id", culinarySkuId.toString())
            set("market", market)
            set("status", status)
            set("created_at", "2023-06-13T17:23:50.623Z")
            set("updated_at", "2023-06-13T17:23:50.623Z")
        }
        val record: GenericRecord = builder.build()
        return supplierSkuAvroSerializer.serialize("", record)
    }
}
