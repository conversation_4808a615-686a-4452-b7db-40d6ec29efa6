package com.hellofresh.cif.supplier

import com.hellofresh.cif.supplier.deserializer.GlobalSupplierAvro
import com.hellofresh.cif.supplier.deserializer.SupplierAvro
import com.hellofresh.cif.supplier.model.Supplier
import com.hellofresh.cif.supplier.schema.Tables
import com.hellofresh.cif.supplier.schema.tables.records.SupplierRecord
import java.time.Duration
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.producer.ProducerRecord
import org.testcontainers.shaded.org.awaitility.Awaitility

class SupplierIntegrationTest : IntegrationTest() {

    @Test
    fun `should be able to process supplier records from facility + global supplier topics`() {
        // given
        val expectedSupplier1 = Supplier(UUID.randomUUID(), "Test Supplier", UUID.randomUUID())
        val expectedSupplier2 = Supplier(UUID.randomUUID(), "Test Supplier 2", null)

        // when
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                supplierApp.runApp()
            }
            topicProducer.send(
                ProducerRecord(
                    supplierTopic,
                    0,
                    expectedSupplier1.id.toString(),
                    SupplierAvro.avroSupplier(expectedSupplier1.id, expectedSupplier1.name, expectedSupplier1.parentId),
                ),
            ).get()
            topicProducer.send(
                ProducerRecord(
                    globalSupplierTopic,
                    0,
                    expectedSupplier2.id.toString(),
                    GlobalSupplierAvro.avroGlobalSupplier(
                        expectedSupplier2.id,
                        expectedSupplier2.name,
                        expectedSupplier2.parentId
                    ),
                ),
            ).get()

            var supplierRecords = emptyList<SupplierRecord>()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    supplierRecords = dsl.selectFrom(Tables.SUPPLIER).fetch()
                    supplierRecords.size == 2
                }

            supplierRecords.first { it.id == expectedSupplier1.id }
                .also { supplierRecord ->
                    assertEquals(expectedSupplier1.name, supplierRecord.name)
                    assertEquals(expectedSupplier1.parentId, supplierRecord.parentId)
                }
            supplierRecords.first { it.id == expectedSupplier2.id }
                .also { supplierRecord ->
                    assertEquals(expectedSupplier2.name, supplierRecord.name)
                    assertEquals(expectedSupplier2.parentId, supplierRecord.parentId)
                }

            app.cancel()
        }
    }
}
