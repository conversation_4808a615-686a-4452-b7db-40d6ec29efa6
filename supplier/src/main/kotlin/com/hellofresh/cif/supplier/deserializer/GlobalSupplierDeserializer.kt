package com.hellofresh.cif.supplier.deserializer

import com.hellofresh.cif.lib.kafka.serde.getValue
import com.hellofresh.cif.supplier.model.Supplier
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroDeserializer
import java.util.UUID
import org.apache.avro.generic.GenericArray
import org.apache.avro.generic.GenericRecord
import org.apache.kafka.common.serialization.Deserializer

private const val ADDRESSES = "addresses"
private const val DISTRIBUTION_CENTERS = "distribution_centers"
private const val ID = "id"

class GlobalSupplierDeserializer(schemaRegistryClient: SchemaRegistryClient) : Deserializer<List<Supplier>> {

    private val inner = KafkaAvroDeserializer(schemaRegistryClient)

    override fun close() {
        inner.close()
    }

    override fun configure(configs: MutableMap<String, *>?, isKey: Boolean) {
        inner.configure(configs, isKey)
    }

    override fun deserialize(topic: String?, data: ByteArray?): List<Supplier> {
        val record = inner.deserialize(topic, data) as? GenericRecord ?: return emptyList()

        val id: String by record
        val name: String by record
        val distributionCenterIds = mapToDistributionCenterIds(record)

        return distributionCenterIds.map { distributionCenterId ->
            Supplier(
                id = UUID.fromString(distributionCenterId),
                name = name,
                parentId = id.takeIf { it.isNotBlank() }?.let { UUID.fromString(it) }
            )
        }
    }
    private fun mapToDistributionCenterIds(source: GenericRecord): Set<String> {
        val addresses = source[ADDRESSES] as GenericArray<*>
        return addresses.flatMap { address ->
            val addressRecord = address as GenericRecord
            val distributionCenters = addressRecord[DISTRIBUTION_CENTERS]
                as GenericArray<*>
            distributionCenters.map { distributionCenter ->
                val dcRecord = distributionCenter as GenericRecord
                dcRecord[ID].toString()
            }
        }.toSet()
    }
}
