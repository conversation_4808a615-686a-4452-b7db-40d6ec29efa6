application.name=supplier

parallelism=1

#KafkaConsumer
group.id=csku-inventory-forecast.supplier.v2
auto.offset.reset=earliest
# 5 min
max.poll.interval.ms=600000
# poll will either wait 5 seconds or for the 20KB or 500 records
fetch.min.bytes=20000
fetch.max.wait.ms=5000
max.poll.records=500

#
poll.interval_ms=20
poll.timeout=PT1S
process.timeout=PT30S

consumer.poison-pill.strategy=LOG_ERROR_IGNORE
