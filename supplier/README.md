# How to start the supplier service locally ?

1. Run docker compose up broker kafdrop
2. Run docker compose up inventory-postgres db-migration
3. Create a topic “public.planning.facility.v1” in the broker 'http://localhost:19000'
4. Mirror the topic \
   docker exec --env-file kafka-live-su.env -it broker sh -c '/usr/src/kafka-mirror.sh "public.planning.facility.v1"'
5. Copy the following environment variables IDE in 'Edit Configurations'
   HF_AIVEN_PASSWORD=GET_PASSWORD_FROM_VAULT;HF_INVENTORY_DB_MASTER_HOST=localhost;HF_INVENTORY_DB_PASSWORD=123456;HF_INVENTORY_DB_USERNAME=cif;HF_SCHEMA_REGISTRY_URL='https://kafka-live-hellofresh-live.aivencloud.com:23411';HF_TIER=local
6. Add the broker address in application.properties
   bootstrap.servers=localhost:29092
