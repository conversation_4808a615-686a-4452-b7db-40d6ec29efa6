plugins {
    id("com.hellofresh.cif.application-conventions")
    alias(libs.plugins.jooq)
}

description = "Process Goods-Received-Note information from Kafka topics to the goods_received_note Database table"
group = "$group.goodsReceivedNote"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "goods_received_note|dc_config|uom"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)
    api(projects.lib.featureflags)
    implementation(projects.lib)
    implementation(projects.lib.db)
    implementation(projects.dateUtilModels)
    implementation(libs.hikaricp)
    implementation(libs.postgresql.driver)
    implementation(libs.hellofresh.schemaregistry) {
        exclude(group = "com.google.api.grpc", module = "proto-google-common-protos")
    }
    implementation(libs.protobuf.grpc)
    implementation(projects.purchaseOrder.purchaseOrderLib)
    implementation(projects.distributionCenterLib)
    implementation(projects.skuModels)
    implementation(projects.skuSpecificationLib)

    testImplementation(testFixtures(projects.lib.featureflags))
    testImplementation(testFixtures(projects.skuModels))
    testImplementation(testFixtures(projects.distributionCenterModels))
    testImplementation(projects.libTests)
    testImplementation(libs.mockk)
}
