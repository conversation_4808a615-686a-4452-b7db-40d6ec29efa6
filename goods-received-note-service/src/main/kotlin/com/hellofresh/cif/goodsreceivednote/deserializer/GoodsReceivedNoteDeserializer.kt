package com.hellofresh.cif.goodsreceivednote.deserializer

import com.hellofresh.cif.goodsreceivednote.models.GRNDeliveryLineState
import com.hellofresh.cif.goodsreceivednote.models.GRNKey
import com.hellofresh.cif.goodsreceivednote.models.GRNPurchaseOrderDelivery
import com.hellofresh.cif.goodsreceivednote.models.GRNPurchaseOrderDeliveryLine
import com.hellofresh.cif.goodsreceivednote.models.GRNValue
import com.hellofresh.cif.goodsreceivednote.service.EPOCH_0_YEAR
import com.hellofresh.cif.goodsreceivednote.service.GoodsReceivedNoteMapper.logger
import com.hellofresh.cif.lib.uom.toUOM
import com.hellofresh.dateUtil.models.toNullableLocalDate
import com.hellofresh.dateUtil.models.toNullableOffsetDateTime
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryLineState
import java.time.OffsetDateTime
import org.apache.kafka.common.serialization.Deserializer

object GoodsReceivedNoteKeyDeserializer : Deserializer<GRNKey> {
    override fun deserialize(topic: String?, data: ByteArray?): GRNKey {
        check(data != null) { "Key can not be null" }
        return with(
            com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteKey.parseFrom(
                data,
            ),
        ) {
            GRNKey(
                dcCode = dcCode,
                reference = reference,
            )
        }
    }
}

object GoodsReceivedNoteValueDeserializer : Deserializer<GRNValue> {

    override fun deserialize(topic: String?, data: ByteArray?): GRNValue {
        check(data != null) { "Value can not be null" }
        return with(
            GoodsReceivedNoteValue.parseFrom(
                data,
            ),
        ) {
            GRNValue(
                deliveriesList = getDeliveryList(deliveriesList),
            )
        }
    }

    private fun getDeliveryList(deliveriesList: List<GoodsReceivedNoteValue.PurchaseOrderDelivery>): List<GRNPurchaseOrderDelivery> =
        deliveriesList.map { purchaseOrderDelivery ->
            GRNPurchaseOrderDelivery(
                id = purchaseOrderDelivery.id,
                deliveryTime = getDeliveryDateTime(purchaseOrderDelivery),
                linesList = getPurchaseOrderDeliveryLine(purchaseOrderDelivery),
            )
        }

    private fun getPurchaseOrderDeliveryLine(purchaseOrderDelivery: GoodsReceivedNoteValue.PurchaseOrderDelivery?):
        List<GRNPurchaseOrderDeliveryLine> =
        purchaseOrderDelivery?.linesList?.map { purchaseOrderDeliveryLine ->
            GRNPurchaseOrderDeliveryLine(
                skuCode = purchaseOrderDeliveryLine.skuCode,
                state = mapPurchaseOrderDeliveryLineState(purchaseOrderDeliveryLine.state),
                skuUom = purchaseOrderDeliveryLine.skuUom.toUOM(),
                palletizedQuantity = purchaseOrderDeliveryLine.palletizedQuantity.value,
                expirationDate = purchaseOrderDeliveryLine.expirationDate.toNullableLocalDate(),
            )
        } ?: emptyList()

    private fun mapPurchaseOrderDeliveryLineState(deliveryLineState: DeliveryLineState?): GRNDeliveryLineState =
        when (deliveryLineState) {
            DeliveryLineState.DELIVERY_LINE_STATE_OPEN -> GRNDeliveryLineState.DELIVERY_LINE_STATE_OPEN
            DeliveryLineState.DELIVERY_LINE_STATE_RECEIVED -> GRNDeliveryLineState.DELIVERY_LINE_STATE_RECEIVED
            DeliveryLineState.DELIVERY_LINE_STATE_REJECTED -> GRNDeliveryLineState.DELIVERY_LINE_STATE_REJECTED
            DeliveryLineState.DELIVERY_LINE_STATE_CLOSED -> GRNDeliveryLineState.DELIVERY_LINE_STATE_CLOSED
            DeliveryLineState.DELIVERY_LINE_STATE_CANCELLED -> GRNDeliveryLineState.DELIVERY_LINE_STATE_CANCELLED
            DeliveryLineState.DELIVERY_LINE_STATE_EXPECTED -> GRNDeliveryLineState.DELIVERY_LINE_STATE_EXPECTED
            else -> GRNDeliveryLineState.DELIVERY_LINE_STATE_UNSPECIFIED
        }

    private fun getDeliveryDateTime(delivery: GoodsReceivedNoteValue.PurchaseOrderDelivery): OffsetDateTime =
        delivery.deliveryTime.toNullableOffsetDateTime()
            ?.let {
                if (it.year <= EPOCH_0_YEAR) {
                    // for some delivery we get epoch 0 delivery date, but due
                    // to timezone conversion we can't rely on exact match
                    getExpectedDeliveryDateTime(delivery)
                } else {
                    it
                }
            } ?: getExpectedDeliveryDateTime(delivery)

    private fun getExpectedDeliveryDateTime(delivery: GoodsReceivedNoteValue.PurchaseOrderDelivery): OffsetDateTime {
        val startTime = delivery.expectedDeliveryStartTime.toNullableOffsetDateTime()
        requireNotNull(startTime) {
            "Couldn't find any valid delivery time from delivery ${delivery.id}"
        }
        logger.warn("Broken date for delivery ${delivery.id}, using expectedDeliveryStartTime $startTime")
        return startTime
    }
}
