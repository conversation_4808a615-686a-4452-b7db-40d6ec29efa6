package com.hellofresh.cif.goodsreceivednote.deserializer

import com.hellofresh.cif.goodsreceivednote.models.GRNDeliveryLineState
import com.hellofresh.cif.goodsreceivednote.models.GRNKey
import com.hellofresh.cif.goodsreceivednote.models.GRNPurchaseOrderDelivery
import com.hellofresh.cif.goodsreceivednote.models.GRNPurchaseOrderDeliveryLine
import com.hellofresh.cif.goodsreceivednote.models.GRNValue
import com.hellofresh.cif.goodsreceivednote.service.GoodsReceivedNoteMapper.logger
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.SkuUOM.UOM_GAL
import com.hellofresh.cif.models.SkuUOM.UOM_KG
import com.hellofresh.cif.models.SkuUOM.UOM_LBS
import com.hellofresh.cif.models.SkuUOM.UOM_LITRE
import com.hellofresh.cif.models.SkuUOM.UOM_OZ
import com.hellofresh.cif.models.SkuUOM.UOM_UNRECOGNIZED
import com.hellofresh.cif.models.SkuUOM.UOM_UNSPECIFIED
import com.hellofresh.dateUtil.models.toNullableLocalDate
import com.hellofresh.dateUtil.models.toNullableOffsetDateTime
import com.hellofresh.proto.stream.ye.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteKey as YfGoodsReceivedNoteKey
import com.hellofresh.proto.stream.ye.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue as YfGoodsReceivedNoteValue
import com.hellofresh.proto.stream.ye.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryLineState as YfDeliveryLineState
import com.hellofresh.proto.stream.ye.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.UnitOfMeasure
import java.time.OffsetDateTime
import org.apache.kafka.common.serialization.Deserializer

object YfGoodsReceivedNoteKeyDeserializer : Deserializer<GRNKey> {
    override fun deserialize(topic: String?, data: ByteArray?): GRNKey {
        check(data != null) { "Key can not be null" }
        return with(
            YfGoodsReceivedNoteKey.parseFrom(
                data,
            ),
        ) {
            GRNKey(
                dcCode = dcCode,
                reference = reference,
            )
        }
    }
}

object YfGoodsReceivedNoteValueDeserializer : Deserializer<GRNValue> {

    override fun deserialize(topic: String?, data: ByteArray?): GRNValue {
        check(data != null) { "Value can not be null" }
        return with(
            YfGoodsReceivedNoteValue.parseFrom(
                data,
            ),
        ) {
            GRNValue(
                deliveriesList = getDeliveryList(deliveriesList),
            )
        }
    }

    private fun getDeliveryList(deliveriesList: List<YfGoodsReceivedNoteValue.PurchaseOrderDelivery>): List<GRNPurchaseOrderDelivery> =
        deliveriesList.map { purchaseOrderDelivery ->
            GRNPurchaseOrderDelivery(
                id = purchaseOrderDelivery.id,
                deliveryTime = getDeliveryDateTime(purchaseOrderDelivery),
                linesList = getPurchaseOrderDeliveryLine(purchaseOrderDelivery),
            )
        }

    private fun getPurchaseOrderDeliveryLine(purchaseOrderDelivery: YfGoodsReceivedNoteValue.PurchaseOrderDelivery?):
        List<GRNPurchaseOrderDeliveryLine> =
        purchaseOrderDelivery?.linesList?.map { purchaseOrderDeliveryLine ->
            GRNPurchaseOrderDeliveryLine(
                skuCode = purchaseOrderDeliveryLine.skuCode,
                state = mapPODeliveryLineState(purchaseOrderDeliveryLine.state),
                skuUom = purchaseOrderDeliveryLine.skuUom.toUOM(),
                palletizedQuantity = purchaseOrderDeliveryLine.palletizedQuantity.value,
                expirationDate = purchaseOrderDeliveryLine.expirationDate.toNullableLocalDate(),
            )
        } ?: emptyList()

    private fun mapPODeliveryLineState(state: YfDeliveryLineState?): GRNDeliveryLineState =
        when (state) {
            YfDeliveryLineState.DELIVERY_LINE_STATE_OPEN -> GRNDeliveryLineState.DELIVERY_LINE_STATE_OPEN
            YfDeliveryLineState.DELIVERY_LINE_STATE_RECEIVED -> GRNDeliveryLineState.DELIVERY_LINE_STATE_RECEIVED
            YfDeliveryLineState.DELIVERY_LINE_STATE_REJECTED -> GRNDeliveryLineState.DELIVERY_LINE_STATE_REJECTED
            YfDeliveryLineState.DELIVERY_LINE_STATE_CLOSED -> GRNDeliveryLineState.DELIVERY_LINE_STATE_CLOSED
            YfDeliveryLineState.DELIVERY_LINE_STATE_CANCELLED -> GRNDeliveryLineState.DELIVERY_LINE_STATE_CANCELLED
            YfDeliveryLineState.DELIVERY_LINE_STATE_EXPECTED -> GRNDeliveryLineState.DELIVERY_LINE_STATE_EXPECTED
            else -> GRNDeliveryLineState.DELIVERY_LINE_STATE_UNSPECIFIED
        }

    private fun getDeliveryDateTime(delivery: YfGoodsReceivedNoteValue.PurchaseOrderDelivery): OffsetDateTime =
        delivery.deliveryTime.toNullableOffsetDateTime() ?: getExpectedDeliveryDateTime(delivery)

    private fun getExpectedDeliveryDateTime(delivery: YfGoodsReceivedNoteValue.PurchaseOrderDelivery): OffsetDateTime {
        val startTime = delivery.expectedDeliveryStartTime.toNullableOffsetDateTime()
        requireNotNull(startTime) {
            "Couldn't find any valid delivery time from delivery for you foodz ${delivery.id}"
        }
        logger.warn("Broken date for delivery ${delivery.id}, using expectedDeliveryStartTime $startTime")
        return startTime
    }

    private fun UnitOfMeasure.toUOM(): SkuUOM =
        when (this) {
            UnitOfMeasure.UNIT_OF_MEASURE_UNSPECIFIED -> UOM_UNSPECIFIED
            UnitOfMeasure.UNIT_OF_MEASURE_UNIT -> SkuUOM.UOM_UNIT
            UnitOfMeasure.UNIT_OF_MEASURE_CASE -> SkuUOM.UOM_UNIT
            UnitOfMeasure.UNIT_OF_MEASURE_KG -> UOM_KG
            UnitOfMeasure.UNIT_OF_MEASURE_LBS -> UOM_LBS
            UnitOfMeasure.UNIT_OF_MEASURE_OZ -> UOM_OZ
            UnitOfMeasure.UNIT_OF_MEASURE_GAL -> UOM_GAL
            UnitOfMeasure.UNIT_OF_MEASURE_LITRE -> UOM_LITRE
            UnitOfMeasure.UNRECOGNIZED, UnitOfMeasure.UNIT_OF_MEASURE_OTHER -> UOM_UNRECOGNIZED
        }
}
