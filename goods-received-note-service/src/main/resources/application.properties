application.name=goods-received-note-service
parallelism=1

group.id=csku-inventory-forecast.goodsReceivedNote.v11
auto.offset.reset=earliest

# 5 min
max.poll.interval.ms=600000

# poll will either wait 5 seconds or for the 20KB or 500 records
fetch.min.bytes=20000
fetch.max.wait.ms=5000
max.poll.records=300

poll.interval_ms=20
poll.timeout=PT1S
process.timeout=PT60S
HF_STATSIG_SDK_KEY=statsig-sdk-key
