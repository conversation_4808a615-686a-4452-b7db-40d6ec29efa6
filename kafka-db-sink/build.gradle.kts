import com.google.cloud.tools.jib.gradle.JibTask

plugins {
    id("com.hellofresh.cif.application-conventions")
    hellofresh.`test-integration`
}

tasks.withType<JibTask>().configureEach {
    enabled = false
}

description = "Consumes data from Kafka topics and save them into database"
group = "$group.${project.name}"

dependencies {
    testIntegrationRuntimeOnly(libs.postgresql.driver)
    testIntegrationRuntimeOnly(libs.slf4j.simple)

    testIntegrationImplementation(projects.libTests)
    testIntegrationImplementation(libs.awaitility)
    testIntegrationImplementation(libs.flyway.core)
    testIntegrationImplementation(libs.kafka.clients)
    testIntegrationImplementation(libs.testcontainers.core)
    testIntegrationImplementation(libs.testcontainers.kafka)
    testIntegrationImplementation(libs.testcontainers.postgresql)
}
