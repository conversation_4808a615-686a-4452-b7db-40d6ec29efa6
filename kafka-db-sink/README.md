# How to start the kafka-db-sink locally ?

1. Run docker compose up broker kafdrop
2. Run docker compose up inventory-postgres db-migration
3. Create a topic “csku-inventory-forecast.intermediate.sku-specification.v1” in the broker 'http://localhost:19000'
4. Mirror the topic \
   docker exec --env-file kafka-live-su.env -it broker sh -c '/usr/src/kafka-mirror.sh "csku-inventory-forecast.intermediate.sku-specification.v1"'
5. docker compose up kafka-db-to-sink
6. Connect to localhost:5432 database with cif/123456, database = inventory
7. Sku_specification table should be populated with data from csku-inventory-forecast.intermediate.sku-specification.v1 topic.

