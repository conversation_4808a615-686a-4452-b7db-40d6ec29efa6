import org.gradle.api.tasks.testing.Test
import org.gradle.kotlin.dsl.register

plugins {
    java
}

val testFunctional by sourceSets.registering {
    compileClasspath += sourceSets.main.get().output
    runtimeClasspath += sourceSets.main.get().output
}

configurations.named("${testFunctional.name}Implementation") {
    extendsFrom(configurations.testImplementation.get())
}

val testFunctionalTask = tasks.register<Test>(testFunctional.name) {
    group = "verification"
    description = "Runs functional tests."
    shouldRunAfter(tasks.test)
    tasks["check"].dependsOn(this)
    testClassesDirs = testFunctional.get().output.classesDirs
    classpath = testFunctional.get().runtimeClasspath
}

tasks.check { dependsOn(testFunctionalTask) }
