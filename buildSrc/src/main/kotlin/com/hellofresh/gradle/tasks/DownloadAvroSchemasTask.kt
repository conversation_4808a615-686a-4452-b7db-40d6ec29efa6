package com.hellofresh.gradle.tasks

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import java.util.concurrent.Callable
import java.util.concurrent.Executors
import javax.inject.Inject
import kotlin.LazyThreadSafetyMode.NONE
import okhttp3.OkHttpClient
import okhttp3.Request
import org.gradle.api.DefaultTask
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.file.ProjectLayout
import org.gradle.api.model.ObjectFactory
import org.gradle.api.provider.Provider
import org.gradle.api.provider.ProviderFactory
import org.gradle.api.provider.SetProperty
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.OutputDirectory
import org.gradle.api.tasks.TaskAction
import org.gradle.kotlin.dsl.setProperty

@Suppress("MagicNumber", "UnstableApiUsage")
open class DownloadAvroSchemasTask @Inject constructor(
    objects: ObjectFactory,
    providers: ProviderFactory,
    layout: ProjectLayout
) : DefaultTask() {
    @get:Input
    val aivenApiBaseUrl: Provider<String> = providers.gradleProperty("aiven.api.baseUrl")
        .orElse("https://api.aiven.io/v1/project/hellofresh-live/service/kafka-live")

    @get:Input
    val aivenApiToken: Provider<String> = providers.gradleProperty("aiven.token")

    @get:Input
    val subjectNames: SetProperty<String> = objects.setProperty()

    @get:OutputDirectory
    val target: DirectoryProperty = objects.directoryProperty().convention(layout.projectDirectory.dir("src/main/avro"))

    private val json by lazy(NONE, ::jacksonObjectMapper)

    init {
        group = "source generation"
        description = "Downloads the latest Avro schemas for all external topics in this project."
    }

    @TaskAction
    fun taskAction() {
        val subjectNames = subjectNames.get()
        if (subjectNames.isEmpty()) return

        val target = target.get()
        val client = OkHttpClient()

        Executors.newWorkStealingPool(Runtime.getRuntime().availableProcessors()).invokeAll(
            subjectNames.map { subjectName ->
                Callable {
                    val latestVersion = requireNotNull(
                        client.get<VersionResponse>(
                            "/kafka/schema/subjects/$subjectName/versions"
                        )?.versions?.maxOrNull()
                    ) { "Could not find any versions for schema: $subjectName" }

                    val schema = requireNotNull(
                        client.get<SchemaResponse>(
                            "/kafka/schema/subjects/$subjectName/versions/$latestVersion/schema"
                        )?.schema
                    ) { "Could not fetch latest version $latestVersion of schema $subjectName" }

                    val namespace: String by schema
                    val name: String by schema
                    target.dir(namespace.replace('.', '/')).apply {
                        asFile.mkdirs()
                        json.writeValue(file("$name.avsc").asFile, schema)
                    }

                    logger.info("Successfully downloaded version {} of schema {}", latestVersion, subjectName)
                }
            }
        ).forEach { it.get() }
    }

    private inline fun <reified R> OkHttpClient.get(path: String): R? =
        get(path, jacksonTypeRef<R>())

    private fun <R> OkHttpClient.get(path: String, typeRef: TypeReference<R>): R? =
        Request.Builder()
            .header("Authorization", "aivenv1 ${aivenApiToken.get()}")
            .url("${aivenApiBaseUrl.get()}$path")
            .get()
            .build()
            .let(::newCall)
            .execute()
            .use {
                when (it.code) {
                    200 -> requireNotNull(
                        it.body
                    ) { "Aiven response body was empty: $it" }.run { json.readValue(byteStream(), typeRef) }
                    404 -> null
                    else -> error("Aiven request failed: $it")
                }
            }

    @JsonIgnoreProperties(ignoreUnknown = true)
    private data class VersionResponse(val versions: List<Int>)

    @JsonIgnoreProperties(ignoreUnknown = true)
    private data class SchemaResponse(val schema: Map<String, Any?>)
}
