package com.hellofresh.gradle.utils

private fun formatCamelCase(input: String, ignore: Char<PERSON><PERSON>y, upperCase: Boolean) =
    if (input.isEmpty()) {
        input
    } else {
        StringBuilder(input.length).also {
            var seenSeparator = upperCase
            var seenUpperCase = !upperCase

            input.forEach { c ->
                when (c) {
                    in ignore -> {
                        it.append(c)
                        seenSeparator = upperCase
                        seenUpperCase = !upperCase
                    }
                    in '0'..'9' -> {
                        it.append(c)
                        seenSeparator = false
                        seenUpperCase = false
                    }
                    in 'a'..'z' -> {
                        it.append(if (seenSeparator) c.uppercaseChar() else c)
                        seenSeparator = false
                        seenUpperCase = false
                    }
                    in 'A'..'Z' -> {
                        it.append(if (seenUpperCase) c.lowercaseChar() else c)
                        seenSeparator = false
                        seenUpperCase = true
                    }
                    else -> if (it.isNotEmpty()) {
                        seenSeparator = true
                        seenUpperCase = false
                    }
                }
            }
        }.toString()
    }

fun String.toLowerCamelCase(vararg ignore: Char): String =
    formatCamelCase(this, ignore, false)

fun String.toUpperCamelCase(vararg ignore: Char): String =
    formatCamelCase(this, ignore, true)

private fun formatCase(input: String, separator: Char, ignore: CharArray, upperCase: Boolean) =
    if (input.isEmpty()) {
        input
    } else {
        StringBuilder(input.length).also {
            var seenSeparator = upperCase

            input.forEach { c ->
                when (c) {
                    in ignore -> {
                        it.append(c)
                        seenSeparator = upperCase
                    }
                    in '0'..'9' -> {
                        it.append(c)
                        seenSeparator = false
                    }
                    in 'a'..'z' -> {
                        it.append(if (seenSeparator) c.uppercaseChar() else c)
                        seenSeparator = false
                    }
                    in 'A'..'Z' -> {
                        it.append(if (seenSeparator) c else separator).append(c)
                        seenSeparator = false
                    }
                    else -> if (it.isNotEmpty()) seenSeparator = true
                }
            }
        }.toString()
    }

private fun formatLowerCase(input: String, separator: Char, ignore: CharArray) =
    formatCase(input, separator, ignore, false)

fun String.toLowerDashCase(vararg ignore: Char): String =
    formatLowerCase(this, '-', ignore)

fun String.toLowerSnakeCase(vararg ignore: Char): String =
    formatLowerCase(this, '_', ignore)

private fun formatUpperCase(input: String, separator: Char, ignore: CharArray) =
    formatCase(input, separator, ignore, true)

fun String.toUpperDashCase(vararg ignore: Char): String =
    formatUpperCase(this, '-', ignore)

fun String.toUpperSnakeCase(vararg ignore: Char): String =
    formatUpperCase(this, '_', ignore)
