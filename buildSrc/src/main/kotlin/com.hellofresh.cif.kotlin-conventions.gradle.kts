import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.jvm.toolchain.JavaLanguageVersion
import org.jetbrains.kotlin.gradle.plugin.getKotlinPluginVersion
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val libs = extensions.getByType<VersionCatalogsExtension>().named("libs")

plugins {
    base
    // Apply the Kotlin JVM plugin to add support for Kotlin on the JVM.
    id("org.jetbrains.kotlin.jvm")
}

val javaVersion: String = libs.findVersion("java").get().requiredVersion

val kotlinVersion: String = libs.findVersion("kotlin").get().requiredVersion
val kotlinVersionMajor = kotlinVersion.substringBeforeLast(".")
val embeddedMajorAndMinorKotlinVersion = project.getKotlinPluginVersion().substringBeforeLast(".")
if (kotlinVersionMajor != embeddedMajorAndMinorKotlinVersion) {
    logger.warn(
        "Constant 'KOTLIN_VERSION' ($kotlinVersion) differs from embedded Kotlin version in Gradle (${project.getKotlinPluginVersion()})!\n" +
            "Constant 'KOTLIN_VERSION' should be ($embeddedMajorAndMinorKotlinVersion)."
    )
}

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(javaVersion))
    }
}

kotlin {
    jvmToolchain {
        // Intentionally left blank to combat issues with lazy configuration,
        // see https://youtrack.jetbrains.com/issue/KT-43095 for details.
    }
}

tasks.withType<KotlinCompile> {
    logger.lifecycle("Configuring $name with version ${project.getKotlinPluginVersion()} in project ${project.name}")
    kotlinOptions {
        jvmTarget = javaVersion
        freeCompilerArgs = listOf("-Xjsr305=strict", "-opt-in=kotlin.RequiresOptIn")
        allWarningsAsErrors = true
        languageVersion = kotlinVersionMajor
        apiVersion = kotlinVersionMajor
    }
}

dependencies {
    compileOnly(libs.findLibrary("jetbrains-annotations").get())
    implementation("org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion")
    // Align versions of all Kotlin components
    implementation(platform("org.jetbrains.kotlin:kotlin-bom"))
    // Use Kotlin Test
    implementation("org.jetbrains.kotlin:kotlin-test")
    implementation("org.jetbrains.kotlin:kotlin-test-junit5")
    // Junit 5
    testImplementation(kotlin("test-junit5"))
    testImplementation(platform(libs.findLibrary("junit-bom").get()))
    testImplementation("org.junit.jupiter:junit-jupiter")
    testRuntimeOnly(platform(libs.findLibrary("junit-bom").get()))
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    // JGit for Sonar
    implementation(libs.findLibrary("jgit").get())
}

tasks.withType<Test>().configureEach {
    useJUnitPlatform()
    testLogging {
        showExceptions = true
        exceptionFormat = TestExceptionFormat.FULL
        events("skipped", "failed")
    }
    systemProperty("hf.tier", "test")
}
