package com.hellofresh.cif.calculator.producer.filter

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.calculator.models.SupplierSkuPoDueInVal
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.CalculatorMode.LIVE_INVENTORY_PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.LIVE_INVENTORY_PRODUCTION
import com.hellofresh.cif.calculator.schema.Tables.LIVE_INVENTORY_CALCULATION
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.lib.kafka.serde.objectMapper
import java.math.BigDecimal.ZERO
import org.jooq.Record
import org.jooq.Table

class LiveInventoryCalculationRepository(
    metricsDSLContext: MetricsDSLContext,
    calculatorMode: CalculatorMode
) : BaseCalculationRepository(metricsDSLContext, calculatorMode) {

    init {
        require(calculatorMode.isLive()) { "calculator mode should by live $calculatorMode" }
    }

    override fun toCskuInventoryForecastKey(record: Record, table: Table<*>) =
        CskuInventoryForecastKey(
            cskuId = record.get(table.field(LIVE_INVENTORY_CALCULATION.CSKU_ID)),
            dcCode = record.get(table.field(LIVE_INVENTORY_CALCULATION.DC_CODE)),
            date = record.get(table.field(LIVE_INVENTORY_CALCULATION.DATE)),
        )

    override fun toCskuInventoryForecastValue(record: Record, table: Table<*>) =
        CskuInventoryForecastVal(
            expired = record.get(table.field(LIVE_INVENTORY_CALCULATION.EXPIRED)),
            openingStock = record.get(table.field(LIVE_INVENTORY_CALCULATION.OPENING_STOCK)),
            present = record.get(table.field(LIVE_INVENTORY_CALCULATION.PRESENT)),
            actualInbound = record.get(table.field(LIVE_INVENTORY_CALCULATION.ACTUAL_INBOUND)),
            actualInboundPurchaseOrders = mapToPoExcludeEmpty(
                record.get(table.field(LIVE_INVENTORY_CALCULATION.ACTUAL_INBOUND_PO)),
            ),
            expectedInbound = record.get(table.field(LIVE_INVENTORY_CALCULATION.EXPECTED_INBOUND)),
            expectedInboundPurchaseOrders = mapToPoExcludeEmpty(
                record.get(
                    table.field(LIVE_INVENTORY_CALCULATION.EXPECTED_INBOUND_PO),
                ),
            ),
            demanded = record.get(table.field(LIVE_INVENTORY_CALCULATION.DEMANDED)),
            closingStock = record.get(table.field(LIVE_INVENTORY_CALCULATION.CLOSING_STOCK)),
            dailyNeeds = record.get(table.field(LIVE_INVENTORY_CALCULATION.DAILY_NEEDS)),
            productionWeek = record.get(table.field(LIVE_INVENTORY_CALCULATION.PRODUCTION_WEEK)),
            productionWeekStartStock = ZERO,
            actualConsumption = record.get(table.field(LIVE_INVENTORY_CALCULATION.ACTUAL_CONSUMPTION)) ?: ZERO,
            safetyStock = record.get(table.field(LIVE_INVENTORY_CALCULATION.SAFETYSTOCK)),
            safetyStockNeeds = record.get(table.field(LIVE_INVENTORY_CALCULATION.SAFETYSTOCK_NEEDS)),
            stagingStock = record.get(table.field(LIVE_INVENTORY_CALCULATION.STAGING_STOCK)),
            storageStock = record.get(table.field(LIVE_INVENTORY_CALCULATION.STORAGE_STOCK)),
            netNeeds = record.get(table.field(LIVE_INVENTORY_CALCULATION.NET_NEEDS)) ?: ZERO,
            purchaseOrderDueInForSuppliers =
            record.get(table.field(LIVE_INVENTORY_CALCULATION.PURCHASE_ORDER_DUE_IN_FOR_SUPPLIERS))
                ?.let { jsonb ->
                    objectMapper.readValue<List<SupplierSkuPoDueInVal>?>(jsonb.data())?.sortedBy { it.hashCode() }
                },
            maxPurchaseOrderDueIn = record.get(table.field(LIVE_INVENTORY_CALCULATION.MAX_PURCHASE_ORDER_DUE_IN)),
            // TODO CPS CHECK WHY LIVE DOESNT HAVE UNUSABLE COLUMN
            unusableInventory = null,
            stockUpdate = null,
            uom = mapSkuUom(record.get(table.field(LIVE_INVENTORY_CALCULATION.UOM))!!),
        )

    override fun toCskuInventoryForecastDbValue(sourceValue: CskuInventoryForecastVal): CskuInventoryForecastVal =
        sourceValue.copy(
            productionWeekStartStock = ZERO,
            stockUpdate = null,
            purchaseOrderDueInForSuppliers = sourceValue.purchaseOrderDueInForSuppliers?.sortedBy { it.hashCode() },
            unusableInventory = null,
        )
}

fun createLiveCalculationRepository(metricsDSLContext: MetricsDSLContext) =
    LiveInventoryCalculationRepository(metricsDSLContext, LIVE_INVENTORY_PRODUCTION)

fun createPreProdLiveCalculationRepository(metricsDSLContext: MetricsDSLContext) =
    LiveInventoryCalculationRepository(metricsDSLContext, LIVE_INVENTORY_PRE_PRODUCTION)
