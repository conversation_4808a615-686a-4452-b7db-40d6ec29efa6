package com.hellofresh.cif.calculator.producer.filter

import com.google.common.base.Stopwatch
import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.CalculatorMode.LIVE_INVENTORY_PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.LIVE_INVENTORY_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.calculator.schema.Tables
import com.hellofresh.cif.calculator.schema.Tables.CALCULATION
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.DateRange
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import java.util.concurrent.TimeUnit.MILLISECONDS
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Record
import org.jooq.Table

const val DB_CALCULATIONS_SEEK_SIZE = 25000

interface CalculationRepository<K, V> {

    val calculatorMode: CalculatorMode

    fun load(dcCodes: Set<String>, daysInThePast: Long): Sequence<Pair<K, V>>
    fun fetchSkuDcWithCalculations(dates: DateRange, dcCode: Set<String>): Set<SkuDc>

    /**
     * Creates equivalent record as It was read from DB
     */
    fun toCskuInventoryForecastDbValue(sourceValue: V): V
}

data class SkuDc(val skuId: UUID, val dcCode: String)

abstract class BaseCalculationRepository(
    private val metricsDSLContext: MetricsDSLContext,
    final override val calculatorMode: CalculatorMode
) : CalculationRepository<CskuInventoryForecastKey, CskuInventoryForecastVal> {

    val queryTable: Table<*> = when (calculatorMode) {
        PRODUCTION -> CALCULATION
        PRE_PRODUCTION -> Tables.PRE_PRODUCTION_CALCULATION
        LIVE_INVENTORY_PRODUCTION -> Tables.LIVE_INVENTORY_CALCULATION
        LIVE_INVENTORY_PRE_PRODUCTION -> Tables.LIVE_INVENTORY_PRE_PRODUCTION_CALCULATION
    }

    override fun load(dcCodes: Set<String>, daysInThePast: Long): Sequence<Pair<CskuInventoryForecastKey, CskuInventoryForecastVal>> =
        sequence {
            val totalTime = Stopwatch.createStarted()
            var lastKey: CskuInventoryForecastKey? = null
            var moreResults = true
            while (moreResults) {
                val timer = Stopwatch.createStarted()
                metricsDSLContext.withTagName("load-by-dcs-${queryTable.name}")
                    .selectFrom(queryTable)
                    .where(
                        queryTable.field(CALCULATION.DATE)?.greaterThan(LocalDate.now().minusDays(daysInThePast)),
                        queryTable.field(CALCULATION.DC_CODE)?.`in`(dcCodes),
                    )
                    .orderBy(
                        queryTable.field(CALCULATION.CSKU_ID),
                        queryTable.field(CALCULATION.DC_CODE),
                        queryTable.field(CALCULATION.DATE),
                    )
                    .apply {
                        lastKey?.let { seek(lastKey?.cskuId, lastKey?.dcCode, lastKey?.date) }
                    }
                    .limit(DB_CALCULATIONS_SEEK_SIZE)
                    .fetch()
                    .also {
                        if (it.isEmpty()) {
                            moreResults = false
                        } else {
                            logger.info(
                                "Calculations seek: ${queryTable.name}: ${
                                    timer.stop().elapsed(
                                        MILLISECONDS,
                                    )
                                }ms - $dcCodes - Count:${it.count()}",
                            )
                            it.forEach { record ->
                                val key = toCskuInventoryForecastKey(record, queryTable)
                                yield(key to toCskuInventoryForecastValue(record, queryTable))
                                lastKey = key
                            }
                        }
                    }
            }
            logger.info(
                "Calculations loaded: ${queryTable.name}: ${totalTime.stop().elapsed(MILLISECONDS)}ms - $dcCodes}",
            )
        }

    override fun fetchSkuDcWithCalculations(
        dateRange: DateRange,
        dcCodes: Set<String>
    ): Set<SkuDc> =
        metricsDSLContext.withTagName("fetch-skuDcs-with-calc-${queryTable.name}")
            .selectDistinct(
                queryTable.field(CALCULATION.CSKU_ID),
                queryTable.field(CALCULATION.DC_CODE),

            ).from(queryTable)
            .where(
                queryTable.field(CALCULATION.DATE)?.between(dateRange.fromDate, dateRange.toDate),
                queryTable.field(CALCULATION.DC_CODE)?.`in`(dcCodes),
                queryTable.field(CALCULATION.PRESENT)?.gt(BigDecimal.ZERO)
                    ?.or(
                        queryTable.field(CALCULATION.DEMANDED)?.gt(BigDecimal.ZERO),
                    ),
            )
            .fetch()
            .let {
                it.map { record ->
                    SkuDc(
                        skuId = record.get(queryTable.field(CALCULATION.CSKU_ID)),
                        dcCode = record.get(queryTable.field(CALCULATION.DC_CODE)),
                    )
                }
            }.toSet()

    protected abstract fun toCskuInventoryForecastKey(record: Record, table: Table<*>): CskuInventoryForecastKey

    protected abstract fun toCskuInventoryForecastValue(record: Record, table: Table<*>): CskuInventoryForecastVal

    companion object : Logging {
        fun mapToPoExcludeEmpty(s: String?) = s?.split(",")?.map { it.trim() }
            ?.filter { it.isNotEmpty() }
            ?.toSet()
    }
}
