package com.hellofresh.cif.calculator.producer.filter

import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.lib.recordFunction
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.Timer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.logging.log4j.kotlin.Logging

/**
 * Read the calculation database into cache only once at the application startup. Later the
 * calculations are updated when written into Kafka.
 *
 * Thread safety:
 *  1. Only 1 thread will be able to fetch calculations and build the cache.
 *  2. Other threads will wait until the cache is build to filter/update.
 *  3. Once the cache is built, access to the cache will be lock free.
 */

private const val TTL_IN_HOURS = 48
private const val MAX_CACHE_SIZE = 2_500_000
const val DAYS_IN_THE_PAST = 8L

class DbRecordFilter<K, V>(
    private val meterRegistry: MeterRegistry,
    private val repo: CalculationRepository<K, V>,
    cacheSupplier: () -> InMemoryCache<K, V> = { inMemoryCache(meterRegistry, repo.calculatorMode) },
) : RecordFilter<K, V> {

    override val calculatorMode by repo::calculatorMode

    private val tags = listOf(Tag.of("topic", calculatorMode.name), Tag.of("name", "db-record-filter-cache"))

    private val failureMetrics = Counter.builder("cache_failure")
        .description("count of records failed to be written by the producer.")
        .tags(tags)

    private val loadDbRecordFilterCacheTimer = Timer.builder("task_duration")
        .description("Record the elapsed time of the execution of the task.")
        .tags(tags)
        .register(meterRegistry)

    // TODO: Add alert on the high miss ratio
    // TODO: Alert on caffeine eviction

    /**
     * In-memory store responsible for holding information about records
     * to enable comparison between a previously seen record and the record
     * being evaluated, hence allowing the caller to filter stale out records.
     * Using caffeine as in memory cache mechanism.
     */
    private val cacheStore: InMemoryCache<K, V> = cacheSupplier()

    override fun load(dcCodes: Set<String>) {
        recordFunction(loadDbRecordFilterCacheTimer) {
            kotlin.runCatching {
                repo.load(dcCodes, DAYS_IN_THE_PAST).forEach { (k, v) -> cacheStore.put(k, v) }
            }.onFailure {
                logger.error("Unable to fetch data from DB in $calculatorMode with dc code: $dcCodes", it)
                failureMetrics
                    .tag("error", it::class.simpleName ?: "Unknown")
                    .register(meterRegistry)
                    .increment()
            }
        }
    }

    override fun update(record: ProducerRecord<K, V>) {
        cacheStore.put(
            record.key(),
            repo.toCskuInventoryForecastDbValue(record.value()),
        )
    }

    override fun filter(records: List<ProducerRecord<K, V>>): List<ProducerRecord<K, V>> =
        records.filter { filter(it.key(), it.value()) }

    override fun filter(key: K, value: V): Boolean =
        cacheStore.isNotInCache(key, repo.toCskuInventoryForecastDbValue(value))

    companion object : Logging {

        fun <K, V> inMemoryCache(meterRegistry: MeterRegistry, calculatorMode: CalculatorMode): InMemoryCache<K, V> =
            CaffeineCache(meterRegistry, calculatorMode, ttlHour(), cacheSize())

        /**
         *  Gets time to live for each in memory cache entry
         *  if nothing is found then it takes default value
         *  as 48 hours which is 2 days
         * */
        private fun ttlHour() = ConfigurationLoader.getIntegerOrDefault("cache.ttl", TTL_IN_HOURS).toLong()

        /**
         *  Gets maximum number of entries for in memory cache
         *  if nothing is found then it takes default value
         *  as 2.5 million
         * */
        private fun cacheSize() = ConfigurationLoader.getIntegerOrDefault("cache.size", MAX_CACHE_SIZE).toLong()
    }
}
