package com.hellofresh.cif.calculator

import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.cif.calculator.models.CalculationInventory
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import java.math.BigDecimal
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class CskuInventoryForecastMapperTest {

    @Test
    fun `should correctly transform a list of DayCalculationResult`() {
        // given
        val inventoryExpiryDate = LocalDate.parse(
            "2024-01-18",
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
        )
        val locationType = LOCATION_TYPE_STAGING
        val dayCalculation = DayCalculationResult(
            SkuUOM.entries.random(),
            UUID.randomUUID(), "VE", LocalDate.now(),
            SkuQuantity.fromLong(
                0,
            ),
            SkuQuantity.fromLong(1), SkuQuantity.fromLong(2), SkuQuantity.fromLong(3),
            setOf(
                "A",
            ),
            SkuQuantity.fromLong(
                4,
            ),
            setOf(
                "B",
            ),
            SkuQuantity.fromLong(
                5,
            ),
            SkuQuantity.fromLong(
                6,
            ),
            SkuQuantity.fromLong(
                7,
            ),
            SkuQuantity.fromLong(
                8,
            ),
            "2022-W01", SkuQuantity.fromLong(9), SkuQuantity.fromLong(10), SkuQuantity.fromLong(11),
            netNeeds = SkuQuantity.fromLong(10),
            unusableInventory = listOf(
                CalculationInventory(
                    qty = SkuQuantity.fromLong(10),
                    expiryDate = inventoryExpiryDate,
                    locationType = locationType,
                ),
            ),
        )

        // when
        val result = CskuInventoryForecastMapper.toInventoryForecastMap(listOf(dayCalculation))

        // then
        assertEquals(1, result.size)
        val key = result.entries.first().key
        val value = result.entries.first().value
        assertEquals(dayCalculation.cskuId, key.cskuId)
        assertEquals(dayCalculation.dcCode, key.dcCode)
        assertEquals(dayCalculation.date, key.date)
        assertEquals(dayCalculation.unusable.getValue(), value.expired)
        assertEquals(dayCalculation.openingStock.getValue(), value.openingStock)
        assertEquals(dayCalculation.present.getValue(), value.present)
        assertEquals(dayCalculation.actualInbound.getValue(), value.actualInbound)
        assertEquals(dayCalculation.actualInboundPurchaseOrders, value.actualInboundPurchaseOrders)
        assertEquals(dayCalculation.expectedInbound.getValue(), value.expectedInbound)
        assertEquals(dayCalculation.expectedInboundPurchaseOrders, value.expectedInboundPurchaseOrders)
        assertEquals(dayCalculation.demanded.getValue(), value.demanded)
        assertEquals(dayCalculation.closingStock.getValue(), value.closingStock)
        assertEquals(dayCalculation.dailyNeeds.getValue(), value.dailyNeeds)
        assertEquals(dayCalculation.productionWeekStartStock.getValue(), value.productionWeekStartStock)
        assertEquals(dayCalculation.productionWeek, value.productionWeek)
        assertEquals(dayCalculation.actualConsumption.getValue(), value.actualConsumption)
        assertEquals(dayCalculation.stagingStock.getValue(), value.stagingStock)
        assertEquals(dayCalculation.storageStock.getValue(), value.storageStock)
        assertEquals(dayCalculation.netNeeds.getValue(), value.netNeeds)
        assertEquals(dayCalculation.uom, value.uom)

        assertEquals(
            listOf(
                ForecastInventory(
                    qty = BigDecimal.TEN,
                    expiryDate = inventoryExpiryDate,
                    locationType = locationType,
                ),
            ),
            value.unusableInventory,
        )
    }
}
