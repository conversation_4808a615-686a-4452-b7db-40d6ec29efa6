package com.hellofresh.cif.calculator

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.producer.filter.QueryableCache
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.restassured.RestAssured
import java.math.BigDecimal
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.random.Random
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals

class CacheQueryRouterTest {

    private var port: Int = 0
    fun mock(k1: CskuInventoryForecastKey, value: CskuInventoryForecastVal) {
        QueryableCache { k: CskuInventoryForecastKey -> if (k == k1) value else null }.also {
            CacheQueryRouter.prodCache = it
            CacheQueryRouter.preProdCache = it
        }
    }

    @BeforeTest fun setup() {
        port = Random(System.currentTimeMillis()).nextInt(10000, 20000)
        runStatusServer(SimpleMeterRegistry(), port, StartUpChecks)
    }

    @Test fun `gives 400 if skuId is missing`() {
        RestAssured.given()
            .port(port)
            .params(
                mapOf(
                    "mode" to "prod",
                    "dc" to "VE",
                    "date" to "2022-02-15",
                ),
            ).get("/state")
            .then()
            .statusCode(400)
    }

    @Test fun `gives the correct cache entry`() {
        val k = CskuInventoryForecastKey(
            cskuId = UUID.randomUUID(),
            dcCode = UUID.randomUUID().toString(),
            date = LocalDate.now(),
        )
        val v = CskuInventoryForecastVal(
            openingStock = BigDecimal(Random.nextLong()),
            expired = ZERO,
            present = ZERO,
            actualInbound = ZERO,
            actualInboundPurchaseOrders = setOf(),
            expectedInbound = ZERO,
            expectedInboundPurchaseOrders = setOf(),
            demanded = ZERO,
            closingStock = ZERO,
            dailyNeeds = ZERO,
            productionWeekStartStock = ZERO,
            productionWeek = "",
            actualConsumption = BigDecimal.ZERO,
            safetyStock = null,
            safetyStockNeeds = null,
            stockUpdate = null,
            netNeeds = ZERO,
            uom = UOM_UNIT,
        )

        mock(k, v)

        val response = RestAssured.given()
            .port(port)
            .params(
                mapOf(
                    "mode" to "prod",
                    "dcCode" to k.dcCode,
                    "skuId" to k.cskuId,
                    "date" to k.date.format(DateTimeFormatter.ISO_DATE),
                ),
            ).get("/state")
            .then()
            .statusCode(200)
            .extract().body().jsonPath()

        assertEquals(v, response.getObject("", CskuInventoryForecastVal::class.java))
    }
}
