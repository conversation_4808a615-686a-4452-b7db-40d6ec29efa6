package com.hellofresh.cif.calculator

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.producer.Producer

class NoopProducer(private val mode: CalculatorMode) : Producer<CskuInventoryForecastKey, CskuInventoryForecastVal> {
    lateinit var calculations: Map<CskuInventoryForecastKey, CskuInventoryForecastVal>

    override suspend fun <Source> produce(
        batch: List<Source>,
        mapSource: (Source) -> Pair<CskuInventoryForecastKey, CskuInventoryForecastVal>
    ) {
        this.calculations = batch.associate { mapSource(it) }
    }

    override fun calculatorMode() = mode
}
