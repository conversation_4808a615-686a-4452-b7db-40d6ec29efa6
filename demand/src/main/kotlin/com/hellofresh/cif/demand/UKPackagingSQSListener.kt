package com.hellofresh.cif.demand

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.demand.repository.DemandRepositoryImpl
import com.hellofresh.cif.demand.service.UKPackagingService
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.lib.KrontabScheduler
import com.hellofresh.cif.lib.MeteredJob
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.s3.S3EventMessageParser
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.skuinput.service.SkuInputService
import com.hellofresh.cif.sqs.SQSClientBuilder
import com.hellofresh.cif.sqs.SQSListener
import com.hellofresh.cif.sqs.SQSMessageProxy
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import software.amazon.awssdk.services.sqs.SqsClient

private const val WRITE_NUMBER_OF_THREADS = 2
private const val READ_NUMBER_OF_THREADS = 4

object UKPackagingSQSListener {
    private val sqsClient: SqsClient = SQSClientBuilder.getSqsClient(getAssumeRoleArn(), "uk-packaging-job-session")

    private fun jobTimeMinutes(): Int = ConfigurationLoader.getStringOrFail("job.time_minutes").toInt()
    private fun jobTimeSeconds(): Int? = ConfigurationLoader.getStringIfPresent("job.time_seconds")?.toInt()
    private fun sqsJobTimeSeconds(): Int =
        ConfigurationLoader.getStringOrDefault("sqs.listener.time_seconds", "30").toInt()

    private fun getAssumeRoleArn() = ConfigurationLoader.getStringOrFail("assume.role.arn")
    private fun getUKPackagingSqsUrl() = ConfigurationLoader.getStringOrFail("aws.sqs.url")

    private fun jobPeriod() =
        jobTimeSeconds()?.let { it to TimeUnit.SECONDS }
            ?: (jobTimeMinutes() to TimeUnit.MINUTES)

    fun launch(
        meterRegistry: HelloFreshMeterRegistry
    ) {
        val readWriteMetricsDSLContext = DBConfiguration.jooqMasterDslContext(
            WRITE_NUMBER_OF_THREADS,
            meterRegistry,
        )

        val readMetricsDSLContext = DBConfiguration.jooqReadOnlyDslContext(
            READ_NUMBER_OF_THREADS,
            meterRegistry,
        )

        val s3Importer = S3Importer(getAssumeRoleArn(), "gb-packaging-session")
        val s3EventMessageParser = S3EventMessageParser()
        val demandRepository = DemandRepositoryImpl(readWriteMetricsDSLContext)
        val dcRepository = DcRepositoryImpl(readMetricsDSLContext)
        val dcConfigService = DcConfigService(meterRegistry, dcRepository)
        val skuInputDataRepository = SkuInputDataRepositoryImpl(readMetricsDSLContext, dcConfigService)
        val skuInputService = SkuInputService(skuInputDataRepository)

        val ukPackagingService =
            UKPackagingService(s3Importer, demandRepository, skuInputService)

        val ukPackagingSqsService = SQSMessageProxy(
            ukPackagingService,
            s3EventMessageParser,
        )

        val ukPackagingSqsListener = SQSListener(
            ukPackagingSqsService,
            sqsClient,
            getUKPackagingSqsUrl(),
        )

        val meteredUKPackagingSqsListenerJob = MeteredJob(
            meterRegistry,
            "uk-packaging-notification-sqs-listener-job",
            ukPackagingSqsListener::run,
        )

        val (_, timeUnit) = jobPeriod()
        shutdownNeeded {
            KrontabScheduler(
                period = sqsJobTimeSeconds(),
                timeUnit = timeUnit,
                executor = Executors.newSingleThreadExecutor(),
            )
        }.schedule {
            meteredUKPackagingSqsListenerJob.execute()
        }
    }
}
