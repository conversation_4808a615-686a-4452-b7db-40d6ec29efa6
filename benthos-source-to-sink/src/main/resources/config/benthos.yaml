input:
  kafka:
    addresses:
      - ${HF_KAFKA_BOOTSTRAP_SERVERS}
    tls:
      enabled: ${TLS_ENABLED:true}
      root_cas_file: /tmp/ca.crt
    sasl:
      mechanism: ${HF_KAFKA_SASL_MECHANISM}
      user: ${KAFKA_USERNAME}
      password: ${HF_KAFKA_SASL_PASSWORD}
    topics: [ '${SOURCE_TOPIC}.v${SOURCE_TOPIC_VERSION}' ]
    consumer_group: csku-inventory-forecast.${APP_NAME}.v${APP_VERSION}
    client_id: ${APP_NAME}
    target_version: 2.6.0
    start_from_oldest: true
    checkpoint_limit: 1
    batching:
      count: 1500
      period: 100ms

metrics:
  mapping: 'root = env("METRICS_PREFIX").or("benthos") + "_" + this'
  prometheus: {}

http:
  address: "0.0.0.0:${BENTHOS_PORT}"

pipeline:
  processors:
    - resource: ${PROCESSOR_NAME}_processor
    - catch:
        - log:
            message: "Error while processing a message"
            level: ERROR
            fields_mapping: |
              root.payload = json()
              root.from_topic = meta("kafka_topic")
              root.error = error()
        - bloblang: 'root = deleted()'

output:
  resource: ${RESOURCE_NAME}_output

logger:
  level: '${LOG_LEVEL:INFO}'
  add_timestamp: true
  format: json
  static_fields:
    "@service": ${RESOURCE_NAME}_service
