import InfraPreparation.createKafkaProducer
import InfraPreparation.startBenthos
import InfraPreparation.startKafkaAndCreateTopics
import InfraPreparation.startPostgresAndRunMigrations
import com.fasterxml.jackson.databind.ObjectMapper
import java.sql.ResultSet
import java.time.Duration
import java.time.LocalDate
import java.util.UUID
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.awaitility.Awaitility
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.KafkaContainer
import org.testcontainers.containers.PostgreSQLContainer

@Suppress("UnnecessaryAbstractClass")
abstract class AbstractDbSinkTest(
    val componentName: String,
    private val inputTopicName: String,
    private val inputTopicVersion: String
) {
    val objectMapper = ObjectMapper().findAndRegisterModules()
    lateinit var producer: KafkaProducer<String, String?>
    lateinit var postgres: PostgreSQLContainer<*>
    private lateinit var kafka: KafkaContainer
    private lateinit var benthos: GenericContainer<*>

    @BeforeEach
    fun initInfra() {
        postgres = startPostgresAndRunMigrations()
        kafka = startKafkaAndCreateTopics(listOf("$inputTopicName.v$inputTopicVersion"))
        producer = createKafkaProducer(kafka)
        benthos = startBenthos(
            mapOf(
                "DB_HOST" to postgres.networkAliases.first(),
                "DB_SCHEMA" to "${postgres.databaseName}?sslmode=disable",
                "SOURCE_TOPIC" to inputTopicName,
                "SOURCE_TOPIC_VERSION" to inputTopicVersion,
                "PROCESSOR_NAME" to "common",
                "RESOURCE_NAME" to componentName,
                "HF_KAFKA_SASL_MECHANISM" to "none",
            ),
        )
    }

    @AfterEach
    fun shutdownInfrastructure() {
        benthos.stop()
    }

    protected fun <K, V> publishRecordToKafka(key: K, value: V) {
        val topicKey = objectMapper.writeValueAsString(key)
        val topicValue = objectMapper.writeValueAsString(value)
        val kafkaRecord = ProducerRecord("$inputTopicName.v$inputTopicVersion", topicKey, topicValue)
        producer.send(kafkaRecord)
    }

    inline fun <reified T> assertSkuBasedRecordResult(
        tableName: String,
        skuId: UUID,
        expectedDcCode: String,
        expectedDate: LocalDate,
        expectedValueObject: T
    ) {
        val statement = postgres.createConnection("").createStatement()
        lateinit var resultSet: ResultSet
        Awaitility
            .await()
            .atMost(Duration.ofSeconds(10))
            .until {
                resultSet = statement.executeQuery("select * from $tableName where sku_id='$skuId'")
                resultSet.next()
            }.let {
                val dbId = resultSet.getString("sku_id")
                val dcCode = resultSet.getString("dc_code")
                val date = resultSet.getDate("date")
                val jsonValue = resultSet.getString("value")
                val inventoryFromJson = objectMapper.readValue(jsonValue, T::class.java)
                assertEquals(skuId.toString(), dbId.toString())
                assertEquals(expectedDcCode, dcCode)
                assertEquals(expectedDate, date.toLocalDate())
                assertEquals(expectedValueObject, inventoryFromJson)
            }
    }
}
