import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfigurationTopicValue
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import java.sql.ResultSet
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.TUESDAY
import java.time.Duration
import java.time.LocalTime
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import org.awaitility.Awaitility
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class DistributionCenterDbSinkTest :
    AbstractDbSinkTest("distribution_center", "csku-inventory-forecast.intermediate.distribution-center", "4") {
    @Test
    fun `should store in DB entries from the expected inbound topic`() {
        // given
        val dcKey = "VE"
        val localTime = LocalTime.now().truncatedTo(ChronoUnit.SECONDS).toString()
        val dcConfig =
            DistributionCenterConfigurationTopicValue(
                MONDAY,
                TUESDAY,
                "dach",
                ZoneId.of("UTC"),
                null,
                true,
                true,
                localTime,
                WmsSystem.WMS_SYSTEM_WMS_LITE,
                localTime
            )

        // when
        publishRecordToKafka(dcKey, dcConfig)

        // then
        val statement = postgres.createConnection("").createStatement()
        lateinit var resultSet: ResultSet
        Awaitility
            .await()
            .atMost(Duration.ofSeconds(10))
            .until {
                resultSet = statement.executeQuery("select * from dc_config where dc_code='$dcKey'")
                resultSet.next()
            }.let {
                val market = resultSet.getString("market")
                val prodStart = resultSet.getString("production_start")
                val cleardown = resultSet.getString("cleardown")
                val hasCleardown = resultSet.getBoolean("has_cleardown")
                val zoneId = resultSet.getString("zone_id")
                val enabled = resultSet.getBoolean("enabled")
                val scheduledClearDownTime = resultSet.getString("scheduled_cleardown_time")
                val poCutoffTime = resultSet.getString("po_cutoff_time")
                assertEquals(dcConfig.market, market)
                assertEquals(dcConfig.productionStart.name, prodStart)
                assertEquals(dcConfig.cleardown?.name, cleardown)
                assertEquals(dcConfig.zoneId.id, zoneId)
                assertEquals(dcConfig.enabled, enabled)
                assertEquals(dcConfig.hasCleardown, hasCleardown)
                assertEquals(dcConfig.scheduledClearDownTime, scheduledClearDownTime)
                assertEquals(dcConfig.poCutoffTime, poCutoffTime)
            }
    }
}
