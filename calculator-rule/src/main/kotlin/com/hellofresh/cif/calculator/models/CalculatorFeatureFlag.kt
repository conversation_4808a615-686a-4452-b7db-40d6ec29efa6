package com.hellofresh.cif.calculator.models

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.featureflags.AUTOMATED_DC_LIVE_RULES_FLAG_KEY
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag
import com.hellofresh.cif.featureflags.FeatureFlag.ApplyUnusableMovementsStock
import com.hellofresh.cif.featureflags.FeatureFlag.AutomatedDcLiveRules
import com.hellofresh.cif.featureflags.FeatureFlag.Live2Rule
import com.hellofresh.cif.featureflags.LIVE_2_RULES_FLAG_KEY
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.lib.memoize
import org.apache.logging.log4j.kotlin.Logging

internal class CalculatorFeatureFlag(
    calculatorMode: CalculatorMode,
    statsigFeatureFlagClient: StatsigFeatureFlagClient
) {

    private val automatedDcLiveRulesFlagFunction =
        createLiveFlagFunction(
            AUTOMATED_DC_LIVE_RULES_FLAG_KEY,
            calculatorMode,
            statsigFeatureFlagClient,
        )
    private val live2RulesFlagFunction =
        createLiveFlagFunction(LIVE_2_RULES_FLAG_KEY, calculatorMode, statsigFeatureFlagClient)
    private val applyUnusableMovementsFlagFunction =
        applyUnusableMovementsFlagFunction(statsigFeatureFlagClient)

    fun isAutomatedDcLiveRulesFlag(dcConfiguration: DistributionCenterConfiguration?) = automatedDcLiveRulesFlagFunction(
        dcConfiguration,
    )

    fun isLive2RulesFlagFunction(dcConfiguration: DistributionCenterConfiguration?) = live2RulesFlagFunction(
        dcConfiguration,
    )

    fun isUnusableMovementsFlagFunction(dcCode: String) = applyUnusableMovementsFlagFunction(dcCode)

    companion object : Logging {
        private fun applyUnusableMovementsFlagFunction(
            statsigFeatureFlagClient: StatsigFeatureFlagClient
        ): (String) -> Boolean = { dcCode: String ->
            applyUnusableMovementsFeatureFlag(statsigFeatureFlagClient, dcCode)
        }.memoize()

        private fun applyUnusableMovementsFeatureFlag(
            statsigFeatureFlagClient: StatsigFeatureFlagClient,
            dcCode: String
        ): Boolean = statsigFeatureFlagClient.isEnabledFor(
            ApplyUnusableMovementsStock(setOf(ContextData(DC, dcCode))),
        )

        private fun createLiveFlagFunction(
            featureFlagKey: String,
            mode: CalculatorMode,
            statsigFeatureFlagClient: StatsigFeatureFlagClient
        ): (DistributionCenterConfiguration?) -> Boolean {
            val flagFunction = { dc: DistributionCenterConfiguration ->
                if (mode.isLive()) {
                    val context = setOf(ContextData(DC, dc.dcCode), ContextData(MARKET, dc.market))
                    val featureFlag: FeatureFlag? = when (featureFlagKey) {
                        AUTOMATED_DC_LIVE_RULES_FLAG_KEY -> AutomatedDcLiveRules(context)
                        LIVE_2_RULES_FLAG_KEY -> Live2Rule(context)
                        else -> null
                    }
                    featureFlag?.let {
                        statsigFeatureFlagClient.isEnabledFor(it)
                    } ?: kotlin.run {
                        logger.error("Feature Flag not mapped for $featureFlagKey")
                        false
                    }
                } else {
                    false
                }
            }.memoize()

            return { dc: DistributionCenterConfiguration? -> dc?.let { flagFunction(dc) } ?: false }
        }
    }
}
