package com.hellofresh.cif.calculator.calculations.rules

import com.hellofresh.cif.calculator.models.CalculationData

// The rules here strictly ordered, the calculations could
// differ when you change the order.
private fun calculationRules(
    data: CalculationData,
) = listOf(
    // set the data for calculations
    `apply demands`(data),
    setStockUpdates(data.stockUpdates),
    setSafetyStock(data),
    `inventory partitioned by usable`(data, { inventory }, { inventory = it }, { unusableInventory = it }),
    `apply opening stock unusable movements`(data),

    `when cleardown day consider yesterday's demand for preprod`(data),
    `when cleardown day add received missing inbounds to inventory stocks`(data),

    `opening stock is equal to inventory`,

    `add inbounds to inventory stocks`(data),
    `apply inbounds unusable movements`(data),

    `apply stock updates if present`,

    // we start to serve (apply demand)
    `serve soon to be expired inventory first`({ inventory }, { inventory = it }),

    `remaining inventory is carried over to the next day`({ inventory }, { inventory = it }),

    `apply closing stock`,

    `net needs is consumption plus safety stock minus opening_stock`(data),

).fold(`empty rule`) { a, b -> a + b }

private fun liveCleardownInventoryCalculationRules(data: CalculationData) = listOf(
    // set the data for calculations
    `apply demands`(data),
    setSafetyStock(data),

    (
        `is older than today and cleardown day`(data) then (
            `use storage from cleardown inventory`(data.inventory) +
                `use staging from cleardown inventory`(data.inventory)
            )
        )(data.dcConfig),
    (
        `is older than today and not cleardown day`(data) then (
            `use previous day storage`(data.inventory) +
                `use previous day staging from source`(data.inventory) +
                `calculate staging stock`
            )
        )(data.dcConfig),
    (
        `is today and cleardown day`(data) then (
            `use storage from cleardown inventory`(data.inventory) +
                `use staging from cleardown inventory`(data.inventory)
            )
        )(data.dcConfig),

    (
        `is today and not cleardown`(data) then (
            `use storage from inventory`(data.inventory) +
                `use staging from inventory`(data.inventory) +
                `calculate staging stock`
            )
        )(data.dcConfig),
    (
        `date newer than today` then (
            `use previous day closing storage stock` +
                `use previous day closing staging stock`
            )
        )(data.dcConfig),

    `items are expired the days in advance specified by acceptableCodeLife or have unusable state staging`(data),
    `items are expired the days in advance specified by acceptableCodeLife or have unusable state storage`(data),

    `live opening stock is equal to staging and storage`,

    `serve soon to be expired inventory first`({ stagingInventory }, { stagingInventory = it }),

    `add inbounds to live cleardown storage inventory stock`(data),
    (
        `is calculated for today` or `date newer than today` then
            `serve soon to be expired inventory first`({ storageInventory }, { storageInventory = it })
        )(data.dcConfig),

    (`date older than today` then `closing stock uses only staging`)(data.dcConfig),
    (`is calculated for today` or `date newer than today` then `apply closing stock live`)(data.dcConfig),

    `net needs is consumption plus safety stock minus opening_stock`(data),

).fold(`empty rule`) { a, b -> a + b }

private fun live2InventoryCalculationRules(data: CalculationData) = listOf(

    setSafetyStock(data),

    // START OPENING STOCK
    (
        `date older than today` then (
            `use previous day live storage`(data.inventory) +
                `use previous day live staging`(data.inventory)
            )
        )(
        data.dcConfig,
    ),

    (
        `is calculated for today` then (
            `use live storage`(data.inventory) +
                `use live staging`(data.inventory)
            )
        )(data.dcConfig),

    (
        `date newer than today` then (
            `use previous day closing storage stock` +
                `use previous day closing staging stock`
            )
        )(data.dcConfig),

    `inventory partitioned by usable`(
        data,
        { storageInventory },
        { storageInventory = it },
        { unusableStorageInventory = it },
    ),
    `inventory partitioned by usable`(
        data,
        { stagingInventory },
        { stagingInventory = it },
        { unusableStagingInventory = it },
    ),

    `live opening stock is equal to staging and storage`,
    // END OPENING STOCK

    // START CLOSING STOCK
    `add inbounds to live storage inventory stock`(data),

    `apply live demands`(data),
    (
        `date older than today` then
            (
                `use live storage`(data.inventory) +
                    `use live staging`(data.inventory) +
                    `inventory partitioned by usable`(data, { storageInventory }, { storageInventory = it }, { }) +
                    `inventory partitioned by usable`(data, { stagingInventory }, { stagingInventory = it }, { })
                )
        )(data.dcConfig),
    (
        `is calculated for today` or `date newer than today` then
            (
                `serve soon to be expired inventory first`({ stagingInventory }, { stagingInventory = it }) +
                    `serve soon to be expired inventory first`({ storageInventory }, { storageInventory = it })
                )
        )(data.dcConfig),

    `apply closing stock live`,
    // END CLOSING STOCK

    `net needs is consumption plus safety stock minus opening_stock`(data),
).fold(`empty rule`) { a, b -> a + b }

private fun automatedDcLiveInventoryCalculationRules(data: CalculationData) = listOf(

    setSafetyStock(data),

    (`date older than today` or `is calculated for today` then `use previous day inventory`(data.inventory))(
        data.dcConfig,
    ),
    (`date newer than today` then `use previous day closing stock`)(data.dcConfig),

    `inventory partitioned by usable`(data, { inventory }, { inventory = it }, { unusableInventory = it }),

    `opening stock is equal to inventory`,

    `add inbounds to live inventory stock`(data),
    `apply automated live demands`(data),
    (
        `date older than today` then
            (
                `use current day inventory`(data.inventory) +
                    `inventory partitioned by usable`(data, { inventory }, { inventory = it }, {})
                )
        )(data.dcConfig),
    (
        `is calculated for today` or `date newer than today` then
            (
                `serve soon to be expired inventory first`({ inventory }, { inventory = it })
                )
        )(data.dcConfig),

    `apply closing stock`,

    `net needs is consumption plus safety stock minus opening_stock`(data),
).fold(`empty rule`) { a, b -> a + b }

internal fun selectCalculationsRules(
    data: CalculationData,
    automatedDcLiveRules: Boolean = false,
    live2Rules: Boolean = false,
) =
    if (data.mode.isLive()) {
        if (automatedDcLiveRules) {
            automatedDcLiveInventoryCalculationRules(data)
        } else if (live2Rules) {
            live2InventoryCalculationRules(data)
        } else {
            liveCleardownInventoryCalculationRules(data)
        }
    } else {
        calculationRules(data)
    }
