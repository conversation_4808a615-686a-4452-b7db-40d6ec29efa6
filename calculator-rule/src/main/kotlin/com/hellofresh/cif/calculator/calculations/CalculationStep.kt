package com.hellofresh.cif.calculator.calculations

import com.hellofresh.cif.calculator.calculations.rules.Rule
import com.hellofresh.cif.calculator.models.CalculationData
import com.hellofresh.cif.calculator.models.CalculationKey
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit

internal data class CalculationStep(
    val values: Map<CalculationKey, DayCalculation>,
    val dayCalculation: DayCalculation
) {
    fun apply(rules: List<Rule>): CalculationStep =
        rules.fold(this) { acc, rule -> rule(acc) }

    fun apply(vararg rule: Rule): CalculationStep = this.apply(rule.toList())

    fun calculate() = dayCalculation.calculate()

    fun previousDay() = values[dayCalculation.key.previousDay()]
}

internal fun List<CalculationStep>.enrich(inputData: CalculationData) =
    this.map { step ->
        step.dayCalculation.apply {
            if (closingStock.isNegative()) {
                val suppliersSkuLeadTime = inputData.supplierSku[key.cskuId] ?: return@apply
                val today = LocalDate.now(inputData.dcConfig[key.dcCode]?.zoneId ?: ZoneOffset.UTC)
                purchaseOrderDueInForSuppliers = suppliersSkuLeadTime
                    .mapNotNull { supplierSkuDetail ->
                        val purchaseOrderDueIn = supplierSkuDetail.getMaxLeadTimeByDate(today)?.let { leadTime ->
                            ChronoUnit.DAYS.between(
                                today,
                                key.date,
                            ).toInt() - leadTime
                        }

                        purchaseOrderDueIn?.let {
                            SupplierSkuPoDueIn(
                                supplierId = supplierSkuDetail.supplierId,
                                poDueIn = it,
                            )
                        }
                    }
                maxPurchaseOrderDueIn = purchaseOrderDueInForSuppliers?.minOfOrNull { it.poDueIn }
            }
        }
    }
