package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.inventorySnapshots
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.TimeRange
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.CleardownData
import com.hellofresh.inventory.models.CleardownMode.SCHEDULED
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.sku.models.SkuSpecification
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

internal class CalculatorClientTest {
    private val skuSpecification = SkuSpecification(
        skuCode = "PHF-00-00000-00",
        name = "a name",
        category = "PHF",
        coolingType = "type",
        packaging = "packaging",
        acceptableCodeLife = 0,
        market = "dach",
        fumigationAllowed = null,
    )

    private val calculatorClient =
        CalculatorClient(StatsigTestFeatureFlagClient(emptySet()))
    private val dcCode = "VE"
    private val demandValue = SkuQuantity.fromLong(5)
    private val today = LocalDate.now(UTC)
    private val distributionCenterConfiguration =
        DistributionCenterConfiguration.default(
            dcCode,
        ).copy(productionStart = today.dayOfWeek, cleardown = today.dayOfWeek, zoneId = UTC)
    private val dcConfigs = mapOf(dcCode to distributionCenterConfiguration)
    private val calculationKeySku1 = CalculationKey(UUID.randomUUID(), dcCode, today)
    private val calculationKeySku2 = CalculationKey(UUID.randomUUID(), dcCode, today)

    @Test
    fun `calculation must not stop even if there are missing sku specifications`() {
        val demandList = listOf(
            Demand(
                calculationKeySku1.cskuId,
                dcCode,
                LocalDate.now(UTC),
                SkuQuantity.fromLong(1),
            ),
            Demand(
                calculationKeySku2.cskuId,
                dcCode,
                LocalDate.now(UTC),
                SkuQuantity.fromLong(2),
            ),
        )
        val distributionCenterConfiguration = DistributionCenterConfiguration.default(dcCode)
        val dcConfig = mapOf(dcCode to distributionCenterConfiguration)
        val runDailyCalculations = calculatorClient.runDailyCalculations(
            InputData(
                mode = PRODUCTION,
                skuDcCandidates = setOf(
                    SkuDcCandidate(
                        calculationKeySku1.cskuId,
                        skuSpecification,
                        distributionCenterConfiguration,
                    ),
                ),
                inventory = inventorySnapshots(dcConfig, emptyList()),
                purchaseOrderInbounds = PurchaseOrderInbounds(emptyList()),
                demands = Demands(demandList),
                stockUpdates = emptyMap(),
                safetyStocks = emptyMap(),
                supplierSku = emptyMap(),
                preproductionCleardownDcs = emptySet(),
            ),
        )
        assertEquals(1, runDailyCalculations.map { it.cskuId }.distinct().count())
        assertTrue { runDailyCalculations.map { it.cskuId }.contains(calculationKeySku1.cskuId) }
    }

    @Test
    fun `calculation must return safety stocks results if present in given input data`() {
        val safetyStockValue = 100L
        val expectedInboundValue = 50L
        val expectedClosingStock = expectedInboundValue - demandValue.getValue().toLong()

        val runDailyCalculations = testCalculation(
            expectedInboundValue,
            safetyStockValue,
        )
        assertEquals(2, runDailyCalculations.map { it.cskuId }.distinct().count())
        val dayCalculationResult =
            runDailyCalculations.first { it.cskuId == calculationKeySku1.cskuId && it.date == calculationKeySku1.date }
        assertEquals(safetyStockValue, dayCalculationResult.safetyStock?.getValue()?.toLong())
        assertEquals(
            expectedClosingStock - safetyStockValue,
            dayCalculationResult.safetyStockNeeds?.getValue()?.toLong(),
        )
        assertTrue(
            runDailyCalculations.filter { it.cskuId == calculationKeySku2.cskuId }
                .all { it.safetyStock == null && it.safetyStockNeeds == null },
        )
    }

    @Test
    fun `calculation must return safety stocks needs results as zero if stock greater than safety stock`() {
        val safetyStockValue = 50L
        val expectedInboundValue = 100L

        val runDailyCalculations = testCalculation(
            expectedInboundValue,
            safetyStockValue,
        )

        val dayCalculationResult =
            runDailyCalculations.first { it.cskuId == calculationKeySku1.cskuId && it.date == calculationKeySku1.date }
        assertEquals(0, dayCalculationResult.safetyStockNeeds?.getValue()?.toLong())
    }

    @Test
    fun `safety stock needs cannot be more than safety stocks even if closing stock is negative`() {
        val safetyStockValue = 4L
        val expectedInboundValue = 1L

        val runDailyCalculations = testCalculation(
            expectedInboundValue,
            safetyStockValue,
        )

        val dayCalculationResult =
            runDailyCalculations.first { it.cskuId == calculationKeySku1.cskuId && it.date == calculationKeySku1.date }
        assertEquals(-safetyStockValue, dayCalculationResult.safetyStockNeeds?.getValue()?.toLong())
    }

    @ParameterizedTest
    @EnumSource(CalculatorMode::class)
    fun `calculations for all modes have opening stock inventory from cleardown inventory snapshot`(
        calculatorMode: CalculatorMode
    ) {
        val distributionCenterConfiguration = DistributionCenterConfiguration.default(dcCode).copy(hasCleardown = true)
        val cleardownTime = LocalDateTime.now(distributionCenterConfiguration.zoneId).minusDays(1)
        val inventorySnapshot = InventorySnapshot(
            dcCode,
            UUID.randomUUID(),
            LocalDateTime.now(),
            listOf(
                SkuInventory(
                    calculationKeySku1.cskuId,
                    listOf(
                        Inventory(
                            qty = SkuQuantity.fromBigDecimal(BigDecimal(75)),
                            expiryDate = null,
                            location = Location("", LOCATION_TYPE_STORAGE, null),
                        ),
                        Inventory(
                            qty = SkuQuantity.fromBigDecimal(BigDecimal(25)),
                            expiryDate = null,
                            location = Location("", LOCATION_TYPE_STAGING, null),
                        ),
                    ),
                ),
            ),
        )
        val runDailyCalculations = calculatorClient.runDailyCalculations(
            InputData(
                mode = calculatorMode,
                skuDcCandidates = setOf(
                    SkuDcCandidate(
                        calculationKeySku1.cskuId,
                        skuSpecification,
                        distributionCenterConfiguration,
                    ),
                ),
                inventory = InventorySnapshots(
                    emptyList(),
                    emptyList(),
                    listOf(CleardownData(dcCode, cleardownTime, SCHEDULED, inventorySnapshot)),
                ),
                purchaseOrderInbounds = PurchaseOrderInbounds(emptyList()),
                stockUpdates = emptyMap(),
                demands = Demands(
                    listOf(
                        Demand(
                            calculationKeySku1.cskuId,
                            calculationKeySku1.dcCode,
                            calculationKeySku1.date,
                            SkuQuantity.fromLong(1),
                        ),
                    ),
                ),
                safetyStocks = emptyMap(),
                supplierSku = emptyMap(),
                preproductionCleardownDcs = emptySet(),
            ),
        )
        assertEquals(
            inventorySnapshot.skus.flatMap { it.inventory }.sumOf { it.qty.getValue() }.toLong(),
            runDailyCalculations.minByOrNull { it.date }?.openingStock?.getValue()?.toLong(),
        )
    }

    @ParameterizedTest
    @EnumSource(CalculatorMode::class)
    fun `calculations for all modes must start from given inventory cleardown time when dc has cleardown`(
        calculatorMode: CalculatorMode
    ) {
        val distributionCenterConfiguration = DistributionCenterConfiguration.default(dcCode).copy(hasCleardown = true)
        val cleardownTime = LocalDateTime.now(
            distributionCenterConfiguration.zoneId,
        ).minusDays(calculatorMode.ordinal.toLong())
        val runDailyCalculations = calculatorClient.runDailyCalculations(
            InputData(
                mode = calculatorMode,
                skuDcCandidates = setOf(
                    SkuDcCandidate(
                        calculationKeySku1.cskuId,
                        skuSpecification,
                        distributionCenterConfiguration,
                    ),
                ),
                inventory = InventorySnapshots(
                    emptyList(),
                    emptyList(),
                    listOf(CleardownData(dcCode, cleardownTime, SCHEDULED, null)),
                ),
                purchaseOrderInbounds = PurchaseOrderInbounds(emptyList()),
                stockUpdates = emptyMap(),
                demands = Demands(
                    listOf(
                        Demand(
                            calculationKeySku1.cskuId,
                            calculationKeySku1.dcCode,
                            calculationKeySku1.date,
                            SkuQuantity.fromLong(1),
                        ),
                    ),
                ),
                safetyStocks = emptyMap(),
                supplierSku = emptyMap(),
                preproductionCleardownDcs = emptySet(),
            ),
        )
        assertEquals(cleardownTime.toLocalDate(), runDailyCalculations.minByOrNull { it.date }?.date)
    }

    @ParameterizedTest
    @EnumSource(CalculatorMode::class)
    fun `calculations for all modes must start at latest cleardown date when dc has cleardown`(
        calculatorMode: CalculatorMode
    ) {
        val distributionCenterConfiguration = DistributionCenterConfiguration.default(dcCode).copy(hasCleardown = true)
        val dcConfig = mapOf(dcCode to distributionCenterConfiguration)
        val runDailyCalculations = calculatorClient.runDailyCalculations(
            InputData(
                mode = calculatorMode,
                skuDcCandidates = setOf(
                    SkuDcCandidate(
                        calculationKeySku1.cskuId,
                        skuSpecification,
                        distributionCenterConfiguration,
                    ),
                ),
                inventory = inventorySnapshots(dcConfig, emptyList()),
                purchaseOrderInbounds = PurchaseOrderInbounds(emptyList()),
                stockUpdates = emptyMap(),
                demands = Demands(
                    listOf(
                        Demand(
                            calculationKeySku1.cskuId,
                            calculationKeySku1.dcCode,
                            calculationKeySku1.date,
                            SkuQuantity.fromLong(1),
                        ),
                    ),
                ),
                safetyStocks = emptyMap(),
                supplierSku = emptyMap(),
                preproductionCleardownDcs = emptySet(),
            ),
        )
        assertEquals(dcConfig.values.first().getLatestCleardown(), runDailyCalculations.minByOrNull { it.date }?.date)
    }

    @ParameterizedTest
    @EnumSource(CalculatorMode::class)
    fun `calculations for all modes must start from today when dc doesn't have cleardown`(
        calculatorMode: CalculatorMode
    ) {
        val distributionCenterConfiguration = DistributionCenterConfiguration.default(dcCode).copy(hasCleardown = false)
        val dcConfig = mapOf(dcCode to distributionCenterConfiguration)
        val runDailyCalculations = calculatorClient.runDailyCalculations(
            InputData(
                mode = calculatorMode,
                skuDcCandidates = setOf(
                    SkuDcCandidate(
                        calculationKeySku1.cskuId,
                        skuSpecification,
                        distributionCenterConfiguration,
                    ),
                ),
                inventory = inventorySnapshots(dcConfig, emptyList()),
                purchaseOrderInbounds = PurchaseOrderInbounds(emptyList()),
                stockUpdates = emptyMap(),
                demands = Demands(
                    listOf(
                        Demand(
                            calculationKeySku1.cskuId,
                            calculationKeySku1.dcCode,
                            calculationKeySku1.date,
                            SkuQuantity.fromLong(1),
                        ),
                    ),
                ),
                safetyStocks = emptyMap(),
                supplierSku = emptyMap(),
                preproductionCleardownDcs = emptySet(),
            ),
        )
        val today = LocalDate.now(dcConfig[dcCode]?.zoneId)
        assertEquals(today, runDailyCalculations.minByOrNull { it.date }?.date)
    }

    @Test
    fun `calculations should not stop if any sku has errors (different UnitOfMeasure)`() {
        val distributionCenterConfiguration = DistributionCenterConfiguration.default(dcCode).copy(hasCleardown = false)
        val dcConfig = mapOf(dcCode to distributionCenterConfiguration)

        val demand1 = Demand(
            calculationKeySku1.cskuId,
            calculationKeySku1.dcCode,
            calculationKeySku1.date,
            SkuQuantity.fromLong(1, SkuUOM.entries.random()),
        )
        val demand2 = Demand(
            calculationKeySku2.cskuId,
            calculationKeySku2.dcCode,
            calculationKeySku2.date,
            SkuQuantity.fromLong(1, SkuUOM.entries.random()),
        )
        val inventory1 = Inventory(
            qty = SkuQuantity.fromLong(75, SkuUOM.entries.first { it != demand1.forecastedQty.unitOfMeasure }),
            expiryDate = null,
            location = Location("", LOCATION_TYPE_STORAGE, null),
        )
        val inventory2 = Inventory(
            qty = SkuQuantity.fromLong(75, demand2.forecastedQty.unitOfMeasure),
            expiryDate = null,
            location = Location("", LOCATION_TYPE_STORAGE, null),
        )
        val inventorySnapshot = InventorySnapshot(
            dcCode,
            UUID.randomUUID(),
            today.atStartOfDay(),
            listOf(
                SkuInventory(calculationKeySku1.cskuId, listOf(inventory1)),
                SkuInventory(calculationKeySku2.cskuId, listOf(inventory2)),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(
            InputData(
                mode = PRODUCTION,
                skuDcCandidates = setOf(
                    SkuDcCandidate(
                        calculationKeySku1.cskuId,
                        skuSpecification.copy(uom = demand1.forecastedQty.unitOfMeasure),
                        distributionCenterConfiguration,
                    ),
                    SkuDcCandidate(
                        calculationKeySku2.cskuId,
                        skuSpecification.copy(uom = demand2.forecastedQty.unitOfMeasure),
                        distributionCenterConfiguration,
                    ),
                ),
                inventory = inventorySnapshots(dcConfig, listOf(inventorySnapshot)),
                purchaseOrderInbounds = PurchaseOrderInbounds(emptyList()),
                stockUpdates = emptyMap(),
                demands = Demands(listOf(demand1, demand2)),
                safetyStocks = emptyMap(),
                supplierSku = emptyMap(),
                preproductionCleardownDcs = emptySet(),
            ),
        )
        assertTrue(runDailyCalculations.none { it.cskuId == demand1.skuId && it.dcCode == demand1.dcCode })
        assertTrue(runDailyCalculations.all { it.cskuId == demand2.skuId && it.dcCode == demand2.dcCode })
    }

    @Test
    fun `calculations are just produced for given sku candidates)`() {
        val distributionCenterConfiguration = DistributionCenterConfiguration.default(dcCode).copy(hasCleardown = false)
        val dcConfig = mapOf(dcCode to distributionCenterConfiguration)

        val inventory1 = Inventory(
            qty = SkuQuantity.fromLong(75, skuSpecification.uom),
            expiryDate = null,
            location = Location("", LOCATION_TYPE_STORAGE, null),
        )
        val inventory2 = Inventory(
            qty = SkuQuantity.fromLong(75, skuSpecification.uom),
            expiryDate = null,
            location = Location("", LOCATION_TYPE_STORAGE, null),
        )
        val inventorySnapshot = InventorySnapshot(
            dcCode,
            UUID.randomUUID(),
            today.atStartOfDay(),
            listOf(
                SkuInventory(calculationKeySku1.cskuId, listOf(inventory1)),
                SkuInventory(calculationKeySku2.cskuId, listOf(inventory2)),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(
            InputData(
                mode = PRODUCTION,
                skuDcCandidates = setOf(
                    SkuDcCandidate(
                        calculationKeySku1.cskuId,
                        skuSpecification,
                        distributionCenterConfiguration,
                    ),
                ),
                inventory = inventorySnapshots(dcConfig, listOf(inventorySnapshot)),
                purchaseOrderInbounds = PurchaseOrderInbounds(emptyList()),
                stockUpdates = emptyMap(),
                demands = Demands(emptyList()),
                safetyStocks = emptyMap(),
                supplierSku = emptyMap(),
                preproductionCleardownDcs = emptySet(),
            ),
        )
        assertTrue(
            runDailyCalculations.all {
                it.cskuId == calculationKeySku1.cskuId && it.dcCode == distributionCenterConfiguration.dcCode
            },
        )
    }

    private fun testCalculation(
        expectedInboundValue: Long,
        safetyStockValue: Long
    ) = calculatorClient.runDailyCalculations(
        InputData(
            mode = PRODUCTION,
            skuDcCandidates = setOf(
                SkuDcCandidate(
                    calculationKeySku1.cskuId,
                    skuSpecification,
                    distributionCenterConfiguration,
                ),
                SkuDcCandidate(
                    calculationKeySku2.cskuId,
                    skuSpecification,
                    distributionCenterConfiguration,
                ),
            ),
            inventory = inventorySnapshots(dcConfigs, emptyList()),
            purchaseOrderInbounds = PurchaseOrderInbounds(
                listOf(
                    PurchaseOrder(
                        "",
                        "",
                        null,
                        dcCode,
                        TimeRange(
                            calculationKeySku1.date.atStartOfDay(UTC),
                            calculationKeySku1.date.atStartOfDay(UTC).plusMinutes(1),
                        ),
                        null,
                        listOf(
                            PurchaseOrderSku(
                                calculationKeySku1.cskuId,
                                SkuQuantity.fromLong(expectedInboundValue),
                                emptyList(),
                            ),
                        ),
                        poStatus = APPROVED,
                    ),
                ),
            ),
            safetyStocks = mapOf(
                SafetyStockKey(
                    calculationKeySku1.dcCode,
                    dcConfigs[calculationKeySku1.dcCode]!!.let { DcWeek(it.getLatestProductionStart(), it.productionStart) },
                    calculationKeySku1.cskuId,
                ) to safetyStockValue,
            ),
            demands = Demands(
                listOf(
                    Demand(
                        calculationKeySku1.cskuId,
                        calculationKeySku1.dcCode,
                        calculationKeySku1.date,
                        demandValue,
                    ),
                    Demand(
                        calculationKeySku2.cskuId,
                        calculationKeySku2.dcCode,
                        calculationKeySku2.date,
                        SkuQuantity.fromLong(2),
                    ),
                ),
            ),
            stockUpdates = emptyMap(),
            supplierSku = emptyMap(),
            preproductionCleardownDcs = emptySet(),
        ),
    )
}
