plugins {
    id("com.hellofresh.cif.common-conventions")
    hellofresh.`test-fixtures`
}

description = "Calculator rule"
group = "$group.${project.name}"

dependencies {
    api(projects.purchaseOrder.purchaseOrderModels)
    api(projects.demandModels)
    api(projects.inventoryModels)
    api(projects.distributionCenterModels)
    api(projects.skuModels)
    api(projects.lib.featureflags)
    implementation(projects.lib.logging)
    implementation(projects.safetyStock.safetyStockLib)

    testImplementation(testFixtures(projects.distributionCenterModels))
    testImplementation(testFixtures(projects.inventoryModels))
    testImplementation(testFixtures(projects.lib.featureflags))
    testImplementation(libs.mockk)
    testImplementation(projects.calculatorModels)

    testFixturesImplementation(projects.inventoryModels)
    modules {
        module("com.hellofresh:logging") {
            replacedBy("com.hellofresh.cif.lib:logging")
        }
    }
}
