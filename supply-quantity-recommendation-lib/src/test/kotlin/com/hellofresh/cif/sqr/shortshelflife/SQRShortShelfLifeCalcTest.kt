package com.hellofresh.cif.sqr.shortshelflife

import com.hellofresh.cif.models.SkuQuantity
import java.math.BigDecimal
import java.util.stream.Stream
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class SQRShortShelfLifeCalcTest {

    @ParameterizedTest
    @MethodSource("shortShelfLifeInputProvider")
    @Suppress("LongParameterList")
    fun `should verify the apply formula functionality`(
        openingStock: SkuQuantity,
        consumption: SkuQuantity,
        stockUpdates: SkuQuantity?,
        bufferPercentageParam: BigDecimal,
        bufferAdditionalParam: BigDecimal,
        expected: BigDecimal
    ) {
        val result = SQRShortShelfLifeCalc.applyFormula(
            openingStock,
            consumption,
            stockUpdates,
            bufferPercentageParam,
            bufferAdditionalParam,
        )
        kotlin.test.assertEquals(expected, result.getValue())
    }

    companion object {
        @JvmStatic
        fun shortShelfLifeInputProvider(): Stream<Arguments> = Stream.of(
            // Case WITHOUT stockUpdate
            Arguments.of(
                SkuQuantity.fromLong(20L),
                SkuQuantity.fromLong(100L),
                SkuQuantity.fromLong(0L),
                BigDecimal("10"), // 10%
                BigDecimal("10"), // Additional buffer
                BigDecimal("100"),
            ),
            // Case with stockUpdate, bufferPercentage and bufferAdditional
            Arguments.of(
                SkuQuantity.fromLong(10L),
                SkuQuantity.fromLong(100L),
                SkuQuantity.fromLong(100L),
                BigDecimal("10"), // 10%
                BigDecimal("10"), // Additional buffer
                BigDecimal("10"),
            ),

            // Case WITHOUT bufferPercentage and bufferAdditional
            Arguments.of(
                SkuQuantity.fromLong(10L),
                SkuQuantity.fromLong(50L),
                SkuQuantity.fromLong(100),
                BigDecimal.ZERO, // No buffer percentage
                BigDecimal.ZERO, // No additional buffer
                BigDecimal("0"),
            ),

            // Case WITHOUT bufferPercentage
            Arguments.of(
                SkuQuantity.fromLong(10L),
                SkuQuantity.fromLong(100L),
                SkuQuantity.fromLong(100L),
                BigDecimal.ZERO,
                BigDecimal("10"),
                BigDecimal("0"),
            ),
            // Case UNIT are rounded down
            Arguments.of(
                SkuQuantity.fromLong(8L),
                SkuQuantity.fromLong(106L),
                SkuQuantity.fromLong(0L),
                BigDecimal("1"),
                BigDecimal("2"),
                BigDecimal("101"),
            ),
            // Case UNIT are rounded up
            Arguments.of(
                SkuQuantity.fromLong(8L),
                SkuQuantity.fromLong(50L),
                SkuQuantity.fromLong(0L),
                BigDecimal("1"),
                BigDecimal("8"),
                BigDecimal("50"),
            ),
            // Handling the negative SQR value - opening stock
            Arguments.of(
                SkuQuantity.fromLong(0L),
                SkuQuantity.fromLong(0L),
                SkuQuantity.fromLong(100L),
                BigDecimal("0"),
                BigDecimal("0"),
                BigDecimal("0"),
            ),
            // Handling the negative SQR value - stock update
            Arguments.of(
                SkuQuantity.fromLong(100L),
                SkuQuantity.fromLong(0L),
                SkuQuantity.fromLong(0L),
                BigDecimal("0"),
                BigDecimal("0"),
                BigDecimal("0"),
            ),
        )
    }
}
