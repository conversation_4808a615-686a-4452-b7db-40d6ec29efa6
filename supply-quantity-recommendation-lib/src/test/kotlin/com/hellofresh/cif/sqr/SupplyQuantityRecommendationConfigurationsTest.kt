package com.hellofresh.cif.sqr

import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.sqr.SQRConfiguration.Companion.DEFAULT_MULTI_WEEK_ENABLED
import com.hellofresh.cif.sqr.SQRConfiguration.Companion.DEFAULT_RECOMMENDATION_ENABLED
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

private const val DC = "DC"

class SupplyQuantityRecommendationConfigurationsTest {

    @Test
    fun `returns matching sqr configuration for sku and week`() {
        val skuId = UUID.randomUUID()
        val productionWeek = ProductionWeek(LocalDate.now(ZoneOffset.UTC).plusWeeks(1), DayOfWeek.MONDAY)
        val expectedRecommendationEnabled = false
        val expectedMultiWeekEnabled = true
        val sqrConfigurations = SQRConfigurations(
            listOf(
                SQRConfiguration(
                    DC,
                    skuId,
                    productionWeek,
                    expectedRecommendationEnabled,
                    expectedMultiWeekEnabled,
                ),
                SQRConfiguration(
                    DC,
                    skuId,
                    productionWeek.plusWeeks(1),
                    false,
                    false,
                ),
            ),
        )
        val sqrConfiguration = sqrConfigurations.getConfiguration(DC, skuId, productionWeek)

        assertEquals(expectedRecommendationEnabled, sqrConfiguration.recommendationEnabled)
        assertEquals(expectedMultiWeekEnabled, sqrConfiguration.multiWeekEnabled)
    }

    @Test
    fun `returns default sqr configuration for non existing sku`() {
        val sqrConfiguration =
            SQRConfigurations(
                emptyList(),
            ).getConfiguration(
                DC,
                UUID.randomUUID(),
                ProductionWeek(LocalDate.now(ZoneOffset.UTC).plusWeeks(1), DayOfWeek.MONDAY),
            )
        assertEquals(DEFAULT_RECOMMENDATION_ENABLED, sqrConfiguration.recommendationEnabled)
        assertEquals(DEFAULT_MULTI_WEEK_ENABLED, sqrConfiguration.multiWeekEnabled)
    }

    @Test
    fun `returns matching sqr configuration for sku and previous week`() {
        val skuId = UUID.randomUUID()
        val productionWeek = ProductionWeek(LocalDate.now(ZoneOffset.UTC).plusWeeks(1), DayOfWeek.MONDAY)
        val expectedRecommendationEnabled = false
        val expectedMultiWeekEnabled = true
        val sqrConfiguration = SQRConfigurations(
            listOf(
                SQRConfiguration(
                    DC,
                    skuId,
                    productionWeek.minusWeeks(1),
                    expectedRecommendationEnabled,
                    expectedMultiWeekEnabled,
                ),
                SQRConfiguration(
                    DC,
                    skuId,
                    productionWeek.plusWeeks(3),
                    true,
                    false,
                ),
            ),
        ).getConfiguration(DC, skuId, productionWeek)

        assertEquals(expectedRecommendationEnabled, sqrConfiguration.recommendationEnabled)
        assertEquals(expectedMultiWeekEnabled, sqrConfiguration.multiWeekEnabled)
    }

    @Test
    fun `returns default sqr configuration if there is no week match (current and past weeks)`() {
        val skuId = UUID.randomUUID()
        val productionWeek = ProductionWeek(LocalDate.now(ZoneOffset.UTC), DayOfWeek.MONDAY)

        val sqrConfiguration = SQRConfigurations(
            listOf(
                SQRConfiguration(
                    DC,
                    skuId,
                    productionWeek.plusWeeks(3),
                    !DEFAULT_RECOMMENDATION_ENABLED,
                    !DEFAULT_MULTI_WEEK_ENABLED,
                ),
            ),
        ).getConfiguration(DC, skuId, productionWeek)

        assertEquals(DEFAULT_RECOMMENDATION_ENABLED, sqrConfiguration.recommendationEnabled)
        assertEquals(DEFAULT_MULTI_WEEK_ENABLED, sqrConfiguration.multiWeekEnabled)
    }

    @Test
    fun `contains recommendations when any week has recommendation enabled`() {
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()
        val productionWeek = ProductionWeek(LocalDate.now(ZoneOffset.UTC), DayOfWeek.MONDAY)

        val sqrConfigurations = SQRConfigurations(
            listOf(
                SQRConfiguration(
                    DC,
                    skuId1,
                    productionWeek.plusWeeks(3),
                    true,
                    DEFAULT_MULTI_WEEK_ENABLED,
                ),
                SQRConfiguration(
                    DC,
                    skuId1,
                    productionWeek.plusWeeks(1),
                    false,
                    DEFAULT_MULTI_WEEK_ENABLED,
                ),
                SQRConfiguration(
                    DC,
                    skuId2,
                    productionWeek.plusWeeks(3),
                    false,
                    DEFAULT_MULTI_WEEK_ENABLED,
                ),
            ),
        )

        assertTrue(sqrConfigurations.hasAnyRecommendationEnabled(DC, skuId1))
        assertFalse(sqrConfigurations.hasAnyRecommendationEnabled(DC, skuId2))
    }

    @Test
    fun `returns all skus with recommendations enabled when any week has recommendation`() {
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()
        val skuId3 = UUID.randomUUID()
        val productionWeek = ProductionWeek(LocalDate.now(ZoneOffset.UTC), DayOfWeek.MONDAY)

        val sqrConfigurations = SQRConfigurations(
            listOf(
                SQRConfiguration(
                    DC,
                    skuId1,
                    productionWeek.plusWeeks(3),
                    true,
                    DEFAULT_MULTI_WEEK_ENABLED,
                ),
                SQRConfiguration(
                    DC,
                    skuId2,
                    productionWeek.plusWeeks(1),
                    true,
                    DEFAULT_MULTI_WEEK_ENABLED,
                ),
                SQRConfiguration(
                    DC,
                    skuId3,
                    productionWeek.plusWeeks(3),
                    false,
                    DEFAULT_MULTI_WEEK_ENABLED,
                ),
            ),
        )

        assertEquals(setOf(skuId1, skuId2), sqrConfigurations.getRecommendationEnabledSkus(DC))
    }
}
