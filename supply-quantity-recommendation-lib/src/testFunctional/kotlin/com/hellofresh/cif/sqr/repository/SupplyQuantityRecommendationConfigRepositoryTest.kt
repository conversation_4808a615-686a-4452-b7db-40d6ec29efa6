package com.hellofresh.cif.sqr.repository

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.sqr.FunctionalTest
import com.hellofresh.cif.sqr.SQRConfiguration.Companion.DEFAULT_MULTI_WEEK_ENABLED
import com.hellofresh.cif.sqr.SQRConfiguration.Companion.DEFAULT_RECOMMENDATION_ENABLED
import java.time.DayOfWeek.TUESDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class SupplyQuantityRecommendationConfigRepositoryTest : FunctionalTest() {

    @Test
    fun `sqr configurations are fetched from latest production date`() {
        val distributionCenterConfiguration1 = DistributionCenterConfiguration.Companion.default()
            .copy(
                productionStart = TUESDAY,
                zoneId = UTC,
            )
        val distributionCenterConfiguration2 = DistributionCenterConfiguration.Companion.default().copy(
            dcCode = "DC",
            productionStart = WEDNESDAY,
            zoneId = UTC,
        )

        val today = LocalDate.now(UTC)
        val skuId1 = UUID.randomUUID()
        val sku1sqr1 =
            insertSQRConfiguration(
                distributionCenterConfiguration1,
                today,
                skuId1,
                !DEFAULT_RECOMMENDATION_ENABLED,
                !DEFAULT_MULTI_WEEK_ENABLED
            )
        val sku1sqr2 =
            insertSQRConfiguration(
                distributionCenterConfiguration1,
                today.plusWeeks(1),
                skuId1,
                Random.nextBoolean(),
                Random.nextBoolean()
            )

        val skuId2 = UUID.randomUUID()
        val sku2sqr1 =
            insertSQRConfiguration(
                distributionCenterConfiguration2,
                distributionCenterConfiguration2.getLatestProductionStart(),
                skuId2,
                Random.nextBoolean(),
                Random.nextBoolean(),
            )

        insertSQRConfiguration(DistributionCenterConfiguration.Companion.default("XX"), today, skuId1, false, true)

        val sqrConfigurations =
            runBlocking {
                supplyQuantityRecommendationConfigRepository.fetchSupplyQuantityRecommendationConfigurations(
                    setOf(
                        distributionCenterConfiguration1,
                        distributionCenterConfiguration2,
                    ),
                )
            }

        assertEquals(
            sku1sqr1.recommendationEnabled,
            sqrConfigurations.getConfiguration(
                sku1sqr1.dcCode,
                sku1sqr1.skuId,
                ProductionWeek(
                    sku1sqr1.week,
                    distributionCenterConfiguration1.productionStart,
                    distributionCenterConfiguration1.zoneId,
                ),
            ).recommendationEnabled,
        )

        assertEquals(
            sku1sqr2.recommendationEnabled,
            sqrConfigurations.getConfiguration(
                sku1sqr2.dcCode,
                sku1sqr2.skuId,
                ProductionWeek(
                    sku1sqr2.week,
                    distributionCenterConfiguration1.productionStart,
                    distributionCenterConfiguration1.zoneId,
                ),
            ).recommendationEnabled,
        )

        assertEquals(
            sku2sqr1.recommendationEnabled,
            sqrConfigurations.getConfiguration(
                sku2sqr1.dcCode,
                sku2sqr1.skuId,
                ProductionWeek(
                    sku2sqr1.week,
                    distributionCenterConfiguration2.productionStart,
                    distributionCenterConfiguration2.zoneId,
                ),
            ).recommendationEnabled,
        )
    }

    @Test
    fun `future sqr configurations are fetched where there is no past weeks configurations`() {
        val distributionCenterConfiguration = DistributionCenterConfiguration.Companion.default()
            .copy(
                productionStart = TUESDAY,
                zoneId = UTC,
            )

        val today = LocalDate.now(UTC)
        val skuId = UUID.randomUUID()
        val skusqr =
            insertSQRConfiguration(
                distributionCenterConfiguration,
                today.plusWeeks(4),
                skuId,
                !DEFAULT_RECOMMENDATION_ENABLED,
                !DEFAULT_MULTI_WEEK_ENABLED
            )

        val sqrConfigurations =
            runBlocking {
                supplyQuantityRecommendationConfigRepository.fetchSupplyQuantityRecommendationConfigurations(
                    setOf(distributionCenterConfiguration),
                )
            }

        assertEquals(
            skusqr.recommendationEnabled,
            sqrConfigurations.getConfiguration(
                skusqr.dcCode,
                skusqr.skuId,
                ProductionWeek(
                    skusqr.week,
                    distributionCenterConfiguration.productionStart,
                    distributionCenterConfiguration.zoneId,
                ),
            ).recommendationEnabled,
        )
    }
}
