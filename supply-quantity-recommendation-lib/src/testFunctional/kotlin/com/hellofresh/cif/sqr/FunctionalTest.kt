package com.hellofresh.cif.sqr

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationConfigRepository
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import com.hellofresh.cif.sqrlib.schema.Tables.SQR_SHORT_SHELF_LIFE_CONF
import com.hellofresh.cif.sqrlib.schema.Tables.SUPPLY_QUANTITY_RECOMMENDATION_CONF
import com.hellofresh.cif.sqrlib.schema.tables.records.SupplyQuantityRecommendationConfRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {

    @AfterEach
    fun afterEach() {
        dsl.deleteFrom(SUPPLY_QUANTITY_RECOMMENDATION_CONF).execute()
        dsl.deleteFrom(SQR_SHORT_SHELF_LIFE_CONF).execute()
    }

    @SuppressWarnings("LongParameterList")
    fun insertSQRConfiguration(
        distributionCenterConfiguration: DistributionCenterConfiguration,
        date: LocalDate,
        skuId: UUID,
        recommended: Boolean,
        multiWeek: Boolean
    ) = insertSQRConfiguration(
        distributionCenterConfiguration.dcCode,
        distributionCenterConfiguration.productionStart,
        date,
        skuId,
        recommended,
        multiWeek,
    )

    @SuppressWarnings("LongParameterList")
    fun insertSQRConfiguration(
        dcCode: String,
        productionStart: DayOfWeek,
        date: LocalDate,
        skuId: UUID,
        recommended: Boolean,
        multiWeek: Boolean
    ) =
        SupplyQuantityRecommendationConfRecord().apply {
            this.dcCode = dcCode
            this.skuId = skuId
            this.week = DcWeek(date, productionStart).toString()
            this.recommendationEnabled = recommended
            this.multiWeekEnabled = multiWeek
        }.also {
            dsl.batchInsert(it).execute()
        }

    companion object {
        lateinit var dsl: MetricsDSLContext
        lateinit var supplyQuantityRecommendationConfigRepository: SupplyQuantityRecommendationConfigRepository
        lateinit var sqrShortShelfLifeConfRepository: SQRShortShelfLifeConfRepository

        private val dataSource = getMigratedDataSource(nestedFolderCount = 1)
        val meterRegistry = SimpleMeterRegistry()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newFixedThreadPool(2))
                }
            dsl = DSL.using(dbConfiguration).withMetrics(meterRegistry)

            supplyQuantityRecommendationConfigRepository = SupplyQuantityRecommendationConfigRepository(dsl)
            sqrShortShelfLifeConfRepository = SQRShortShelfLifeConfRepository(dsl)
        }
    }
}
