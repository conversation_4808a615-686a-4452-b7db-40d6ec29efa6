plugins {
    id("com.hellofresh.cif.common-conventions")
    hellofresh.`test-integration`
}

description = "Manages the inventory database objects"
group = "$group.inventory-db"

dependencies {

    testIntegrationImplementation(libs.flyway.core)
    testIntegrationImplementation(libs.hikaricp)
    testIntegrationImplementation(libs.postgresql.driver)
    testIntegrationImplementation(libs.testcontainers.core)
    testIntegrationImplementation(libs.testcontainers.postgresql) {
        exclude("junit:junit")
    }
    testIntegrationImplementation(libs.testcontainers.junit)
    testIntegrationImplementation(libs.slf4j.api)
    testIntegrationImplementation(libs.slf4j.simple)
}
