package com.hellofresh.cif.demand

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.demand.lib.schema.Tables.DEMAND
import com.hellofresh.cif.demand.lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.demand.lib.schema.enums.Uom
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.Context.PACKAGING
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.UsableActualConsumptionDemandDomain
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.lib.memoize
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.demand.models.ActualConsumption
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.demand.models.Demand
import java.math.BigDecimal
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.JSONB
import org.jooq.Record4
import org.jooq.Record6
import org.jooq.Result
import org.jooq.impl.DSL.noCondition

private val objectMapper = ObjectMapper().findAndRegisterModules().setPropertyNamingStrategy(SnakeCaseStrategy())

@Suppress("DestructuringDeclarationWithTooManyEntries")
class DemandRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
    private val statisgFeatureFlagClient: StatsigFeatureFlagClient,
) : DemandRepository {

    private val fetchDemandsByDcCodes = "fetch-demands-by-dc-codes"
    private val fetchDemandConsumptionDetailsByDcCodes = "fetch-demand-consumption-details-by-dc-codes"

    private val actualConsumptionDemandRepository = ActualConsumptionRepository(
        metricsDSLContext,
        statisgFeatureFlagClient,
    )

    override suspend fun findDemands(dcCodes: Set<String>, dateRange: DateRange, skuId: UUID?) =
        withContext(Dispatchers.IO) {
            // Sku only here
            val demandAsync = async { getDemands(dcCodes, dateRange, skuId) }
            // Sku only here
            val actualConsumptionAsync = async {
                actualConsumptionDemandRepository.getActualConsumptions(dcCodes, dateRange, skuId)
            }

            val demand = demandAsync.await()
            val actualConsumption = actualConsumptionAsync.await()
            val isUsableFlagFunction = isUsableFlagFunction()

            (demand.keys + actualConsumption.keys).toSet()
                .map { key ->
                    val actualConsumptionUom = (actualConsumption[key]?.unitOfMeasure ?: demand[key]!!.unitOfMeasure)
                    val demandUom = (demand[key]?.unitOfMeasure ?: actualConsumption[key]!!.unitOfMeasure)

                    Demand(
                        skuId = key.skuId,
                        dcCode = key.dcCode,
                        date = key.date,
                        forecastedQty = demand[key] ?: SkuQuantity.fromBigDecimal(ZERO, demandUom),
                        actualConsumption = ActualConsumption(
                            actualConsumption[key] ?: SkuQuantity.fromBigDecimal(ZERO, actualConsumptionUom),
                            isUsableFlagFunction(Pair(key.dcCode, key.packaging)),
                        ),
                    )
                }
        }

    override suspend fun findDemandConsumptionDetails(
        dcCodes: Set<String>,
        dateRange: DateRange,
        skuId: UUID?
    ): Map<com.hellofresh.demand.models.DemandKey, ConsumptionDetails> =
        withContext(Dispatchers.IO) {
            async { getDemandConsumptionDetails(dcCodes, dateRange, skuId) }.await()
        }

    private fun isUsableFlagFunction(): (Pair<String, String?>) -> Boolean = { input: Pair<String, String?> ->
        isUsableFeatureFlag(input)
    }.memoize()

    private fun isUsableFeatureFlag(input: Pair<String, String?>): Boolean {
        val contextSet = input.second?.let { packaging ->
            setOf(ContextData(DC, input.first), ContextData(PACKAGING, packaging.lowercase()))
        } ?: setOf(ContextData(DC, input.first))
        return statisgFeatureFlagClient.isEnabledFor(UsableActualConsumptionDemandDomain(contextSet))
    }

    private suspend fun getDemands(
        dcCodes: Set<String>,
        dateRange: DateRange,
        skuId: UUID? = null
    ) = metricsDSLContext.withTagName(fetchDemandsByDcCodes)
        .select(
            DEMAND.SKU_ID,
            DEMAND.DC_CODE,
            DEMAND.DATE,
            DEMAND.QUANTITY,
            SKU_SPECIFICATION_VIEW.PACKAGING,
            DEMAND.UOM,
        )
        .from(DEMAND)
        .join(SKU_SPECIFICATION_VIEW)
        .on(SKU_SPECIFICATION_VIEW.ID.eq(DEMAND.SKU_ID))
        .where(
            DEMAND.DC_CODE.`in`(dcCodes)
                .and(DEMAND.DATE.ge(dateRange.fromDate))
                .and(DEMAND.DATE.le(dateRange.toDate))
                .run { if (skuId != null) and(DEMAND.SKU_ID.eq(skuId)) else and(noCondition()) },
        )
        .fetchAsync()
        .thenApply { records -> buildDemand(records) }.await()

    private suspend fun getDemandConsumptionDetails(
        dcCodes: Set<String>,
        dateRange: DateRange,
        skuId: UUID? = null
    ) = metricsDSLContext.withTagName(fetchDemandConsumptionDetailsByDcCodes)
        .select(
            DEMAND.SKU_ID,
            DEMAND.DC_CODE,
            DEMAND.DATE,
            DEMAND.CONSUMPTION_DETAILS,
        )
        .from(DEMAND)
        .where(
            DEMAND.DC_CODE.`in`(dcCodes)
                .and(DEMAND.DATE.ge(dateRange.fromDate))
                .and(DEMAND.DATE.le(dateRange.toDate))
                .run { if (skuId != null) and(DEMAND.SKU_ID.eq(skuId)) else and(noCondition()) },
        )
        .fetchAsync()
        .thenApply { records -> buildDemandConsumptionDetails(records) }.await()

    private fun buildDemand(demandRecords: Result<Record6<UUID, String, LocalDate, BigDecimal, String, Uom>>) =
        demandRecords.map { record ->
            val (skuId, dcCode, date, quantity, packaging) = record
            val demandKey = DemandKey(skuId, packaging, dcCode, date)
            demandKey to SkuQuantity.fromBigDecimal(quantity, SkuUOM.valueOf(record.value6().name))
        }.toMap()

    private fun buildDemandConsumptionDetails(records: Result<Record4<UUID, String, LocalDate, JSONB?>>) =
        records.mapNotNull { record ->
            val (skuId, dcCode, date, consumptionDetails) = record
            consumptionDetails?.let {
                val demandConsumptionDetailKey = com.hellofresh.demand.models.DemandKey(skuId, dcCode, date)
                demandConsumptionDetailKey to objectMapper.readValue<ConsumptionDetails>(consumptionDetails.data())
            }
        }.toMap()

    companion object : Logging {

        fun mapUomToSkuUOM(uom: Uom): SkuUOM =
            when (uom) {
                Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
                Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
                Uom.UOM_KG -> SkuUOM.UOM_KG
                Uom.UOM_LBS -> SkuUOM.UOM_LBS
                Uom.UOM_GAL -> SkuUOM.UOM_GAL
                Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
                Uom.UOM_OZ -> SkuUOM.UOM_OZ
                Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
            }
    }
}

internal data class DemandKey(
    val skuId: UUID,
    val packaging: String,
    val dcCode: String,
    val date: LocalDate,
)
