package com.hellofresh.cif.demand

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.demand.DemandRepositoryImpl.Companion.mapUomToSkuUOM
import com.hellofresh.cif.demand.lib.schema.Tables.ACTUAL_CONSUMPTION_INVENTORY_ACTIVITY
import com.hellofresh.cif.demand.lib.schema.Tables.ACTUAL_CONSUMPTION_VIEW
import com.hellofresh.cif.demand.lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.demand.lib.schema.enums.Uom
import com.hellofresh.cif.featureflags.Context
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.AutomatedDcLiveRules
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.sumOf
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Record6
import org.jooq.Result
import org.jooq.impl.DSL.noCondition

private const val YOUFOODZ_DC_CODE = "YC"

class ActualConsumptionRepository(
    private val metricsDSLContext: MetricsDSLContext,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
) {

    private val fetchActualConsumptionByDcCodes = "fetch-actual-consumption-by-dc-codes"

    internal suspend fun getActualConsumptions(
        dcCodes: Set<String>,
        dateRange: DateRange,
        skuId: UUID? = null,
    ): Map<DemandKey, SkuQuantity> =

        dcCodes.partition {
            statsigFeatureFlagClient.isEnabledFor(
                AutomatedDcLiveRules(
                    setOf(ContextData(Context.DC, it)),
                ),
            ) && it != YOUFOODZ_DC_CODE // TODO CPS MOVE THIS TO FLAG OR CONFIG
        }.let { (automatedDcs, defaultDcs) ->
            val actualConsumptions = if (defaultDcs.isNotEmpty()) {
                fetchActualConsumptions(
                    defaultDcs.toSet(),
                    dateRange,
                    skuId,
                )
            } else {
                emptyMap()
            }

            val actualConsumptionsInventoryActivities =
                if (automatedDcs.isNotEmpty()) {
                    fetchActualConsumptionsInventoryActivities(
                        automatedDcs.toSet(),
                        dateRange,
                        skuId,
                    )
                } else {
                    emptyMap()
                }

            actualConsumptions + actualConsumptionsInventoryActivities
        }

    private suspend fun fetchActualConsumptions(
        dcCodes: Set<String>,
        dateRange: DateRange,
        skuId: UUID? = null,
    ): Map<DemandKey, SkuQuantity> =
        with(ACTUAL_CONSUMPTION_VIEW) {
            metricsDSLContext.withTagName(fetchActualConsumptionByDcCodes)
                .select(
                    DC_CODE,
                    SKU_ID,
                    DATE,
                    QUANTITY,
                    SKU_SPECIFICATION_VIEW.PACKAGING,
                    UOM,
                )
                .from(ACTUAL_CONSUMPTION_VIEW)
                .join(SKU_SPECIFICATION_VIEW)
                .on(SKU_SPECIFICATION_VIEW.ID.eq(ACTUAL_CONSUMPTION_VIEW.SKU_ID))
                .where(
                    DC_CODE.`in`(dcCodes)
                        .and(DATE.ge(dateRange.fromDate))
                        .and(DATE.le(dateRange.toDate))
                        .run { if (skuId != null) and(SKU_ID.eq(skuId)) else and(noCondition()) },
                )
                .fetchAsync()
                .thenApply { records ->
                    buildActualConsumptions(records)
                }.await()
        }

    private suspend fun fetchActualConsumptionsInventoryActivities(
        dcCodes: Set<String>,
        dateRange: DateRange,
        skuId: UUID? = null,
    ): Map<DemandKey, SkuQuantity> =
        with(ACTUAL_CONSUMPTION_INVENTORY_ACTIVITY) {
            metricsDSLContext.withTagName("fetch-actual-consumption-inventory-activities")
                .select(
                    DC_CODE,
                    SKU_ID,
                    DATE,
                    QUANTITY,
                    SKU_SPECIFICATION_VIEW.PACKAGING,
                    UOM,
                )
                .from(ACTUAL_CONSUMPTION_INVENTORY_ACTIVITY)
                .join(SKU_SPECIFICATION_VIEW)
                .on(SKU_SPECIFICATION_VIEW.ID.eq(SKU_ID))
                .where(
                    DC_CODE.`in`(dcCodes)
                        .and(DATE.ge(dateRange.fromDate))
                        .and(DATE.le(dateRange.toDate))
                        .run { if (skuId != null) and(SKU_ID.eq(skuId)) else and(noCondition()) },
                )
                .fetchAsync()
                .thenApply { records ->
                    records.groupBy {
                        DemandKey(
                            it[SKU_ID],
                            it[SKU_SPECIFICATION_VIEW.PACKAGING],
                            it[DC_CODE],
                            it[DATE],
                        )
                    }.mapValues { (_, entries) ->
                        entries.sumOf {
                            SkuQuantity.fromBigDecimal(it[QUANTITY], mapUomToSkuUOM(it[UOM]))
                        }
                    }.toMap()
                }.await()
        }

    private fun buildActualConsumptions(
        actualConsumptionRecords: Result<Record6<String, UUID, LocalDate, BigDecimal, String, Uom>>
    ) =
        actualConsumptionRecords.map {
            val demandKey = DemandKey(
                it[ACTUAL_CONSUMPTION_VIEW.SKU_ID],
                it[SKU_SPECIFICATION_VIEW.PACKAGING],
                it[ACTUAL_CONSUMPTION_VIEW.DC_CODE],
                it[ACTUAL_CONSUMPTION_VIEW.DATE],
            )
            demandKey to SkuQuantity.fromBigDecimal(it[ACTUAL_CONSUMPTION_VIEW.QUANTITY], mapUomToSkuUOM(it[ACTUAL_CONSUMPTION_VIEW.UOM]))
        }.toMap()

    companion object : Logging
}
