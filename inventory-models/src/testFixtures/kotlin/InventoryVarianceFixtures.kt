package com.hellofresh.inventory.models

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.variance.DailyInventoryVarianceData
import com.hellofresh.inventory.models.variance.InventoryVariance
import java.time.LocalDate
import java.util.UUID

object InventoryVarianceFixtures {

    val default = InventoryVariance(
        skuId = UUID.fromString("ed2550e0-b29e-4207-9328-b21658c85c3c"),
        dcCode = "DC-Code-Test",
        dcWeek = "DC-Week-Test",
        cleardownVariance = SkuQuantity.fromLong(100L),
        liveVariance = SkuQuantity.fromLong(200L),
        dailyInventoryVarianceData = listOf(
            DailyInventoryVarianceData(
                LocalDate.of(2023, 7, 12),
                SkuQuantity.fromLong(101),
                SkuQuantity.fromLong(102),
                SkuQuantity.fromLong(102)
            ),
            DailyInventoryVarianceData(
                LocalDate.of(2023, 7, 12).plusDays(1),
                SkuQuantity.fromLong(201),
                SkuQuantity.fromLong(202),
                SkuQuantity.fromLong(202)
            ),
        ),
    )
}
