package com.hellofresh.inventory.models

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import java.math.BigDecimal
import java.math.RoundingMode.HALF_UP
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID

fun Inventory.Companion.default() = Inventory(
    qty = SkuQuantity.fromBigDecimal(BigDecimal.ZERO),
    expiryDate = LocalDate.MAX.minusDays(10),
    poReference = "PTN100-BH3-E1",
    location = Location("", LOCATION_TYPE_STAGING, null),
)

fun InventorySnapshot.Companion.default(
    skuId: UUID = UUID.randomUUID(),
    expiryDate: LocalDate = LocalDate.now(UTC).plusDays(10),
    locationId: String = ""
) = InventorySnapshot(
    dcCode = "dc-code-fixture",
    snapshotTime = LocalDateTime.now(UTC),
    snapshotId = UUID.randomUUID(),
    skus = listOf(
        SkuInventory(
            skuId = skuId,
            inventory = listOf(
                Inventory(
                    qty = SkuQuantity.fromBigDecimal(BigDecimal(100)),
                    expiryDate = expiryDate,
                    location = Location(locationId, LOCATION_TYPE_STAGING, "movTmId"),
                    poReference = "PTN100-BH3-E1",
                ),
            ),
        ),
    ),
)

fun InventoryMovement.Companion.default() = InventoryMovement(
    activityId = UUID.randomUUID(),
    activityTime = OffsetDateTime.now(UTC),
    publishedTime = OffsetDateTime.now(UTC).plusMinutes(1),
    dcCode = "dc-code-fixture",
    skuId = UUID.randomUUID(),
    expirationDate = LocalDate.now(UTC).plusWeeks(1),
    typeId = UUID.randomUUID().toString(),
    quantity = SkuQuantity.fromBigDecimal(BigDecimal(100), UOM_UNIT),
    originLocationId = UUID.randomUUID().toString(),
    originLocationType = LocationType.LOCATION_TYPE_STORAGE,
    destinationLocationId = UUID.randomUUID().toString(),
    destinationLocationType = LOCATION_TYPE_STAGING,
    transportModuleId = "movTmId",
    remainingQuantity = SkuQuantity.fromBigDecimal(BigDecimal(50), UOM_UNIT),
    poNumber = UUID.randomUUID().toString(),

)

fun InventoryAdjustment.Companion.default() =
    InventoryAdjustment(
        activityId = UUID.randomUUID(),
        activityTime = OffsetDateTime.now(UTC),
        publishedTime = OffsetDateTime.now(UTC).plusMinutes(1),
        dcCode = "dc-code-fixture",
        skuId = UUID.randomUUID(),
        expirationDate = LocalDate.now(UTC).plusWeeks(1),
        typeId = UUID.randomUUID().toString(),
        quantity = SkuQuantity.fromBigDecimal(BigDecimal(101).setScale(5, HALF_UP), UOM_UNIT),
        locationId = UUID.randomUUID().toString(),
        locationType = LocationType.LOCATION_TYPE_PRODUCTION,
        transportModuleId = "adjTmId",
        remainingQuantity = SkuQuantity.fromBigDecimal(BigDecimal(55).setScale(5, HALF_UP), UOM_UNIT),
        poNumber = UUID.randomUUID().toString(),
    )
