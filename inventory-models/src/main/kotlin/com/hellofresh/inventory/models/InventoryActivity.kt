package com.hellofresh.inventory.models

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonFormat.Shape.STRING
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.hellofresh.cif.models.SkuQuantity
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID

sealed interface InventoryActivity {
    val activityId: UUID
    val activityTime: OffsetDateTime
    val dcCode: String
    val skuId: UUID
    val expirationDate: LocalDate?
    val publishedTime: OffsetDateTime
    val typeId: String
    val remainingQuantity: SkuQuantity
    val transportModuleId: String?
    val poNumber: String?
}

data class InventoryMovement(
    override val activityId: UUID,
    override val activityTime: OffsetDateTime,
    override val publishedTime: OffsetDateTime,
    override val dcCode: String,
    override val skuId: UUID,
    override val expirationDate: LocalDate?,
    override val typeId: String,
    override val remainingQuantity: SkuQuantity,
    override val transportModuleId: String?,
    override val poNumber: String?,
    val quantity: SkuQuantity,
    val originLocationId: String,
    val originLocationType: LocationType,
    val destinationLocationId: String,
    val destinationLocationType: LocationType,
) : InventoryActivity {

    val originInventoryLocation = InventoryLocation(
        Location(originLocationId, originLocationType, transportModuleId),
        expirationDate
    )

    val destinationInventoryLocation = InventoryLocation(
        Location(destinationLocationId, destinationLocationType, transportModuleId),
        expirationDate
    )

    companion object
}

data class InventoryAdjustment(
    override val activityId: UUID,
    override val activityTime: OffsetDateTime,
    override val publishedTime: OffsetDateTime,
    override val dcCode: String,
    override val skuId: UUID,
    override val expirationDate: LocalDate?,
    override val typeId: String,
    override val remainingQuantity: SkuQuantity,
    override val transportModuleId: String?,
    override val poNumber: String?,
    val quantity: SkuQuantity,
    val locationId: String,
    val locationType: LocationType,
) : InventoryActivity {

    val inventoryLocation = InventoryLocation(Location(locationId, locationType, transportModuleId), expirationDate)

    companion object
}

enum class InventoryActivityType {
    ADJ,
    MOV
}

enum class InventoryAdjustmentTypeId {
    RCV, // Received
    REJ, // Stock Rejected
    USP, // ULA Split Palet
    PCK, // Picks
    CLR, // Used - Cleardown
    UPD; // Used - Production

    fun isClearDownAdjustmentType() = this.name == CLR.name || this.name == UPD.name
}

enum class InventoryMovementsTypeId {
    MIN, // Move to Inventory
    MQU, // Move to Quarantine
    MPR, // Move to Production
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class InventoryMovementValue(
    val quantity: SkuQuantity,
    @JsonFormat(shape = STRING, pattern = "uuuu-MM-dd")
    val expirationDate: LocalDate?,
    val originLocationId: String,
    val originLocationType: LocationType,
    val destinationLocationId: String,
    val destinationLocationType: LocationType,
    val remainingQuantity: SkuQuantity,
    val transportModuleId: String?,
    val poNumber: String?
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class InventoryAdjustmentValue(
    val quantity: SkuQuantity,
    @JsonFormat(shape = STRING, pattern = "uuuu-MM-dd")
    val expirationDate: LocalDate?,
    val locationId: String,
    val locationType: LocationType,
    val remainingQuantity: SkuQuantity,
    val transportModuleId: String?,
    val poNumber: String?
)
