package com.hellofresh.inventory.models

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonFormat.Shape.STRING
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.hellofresh.cif.models.SkuQuantity
import java.time.LocalDate
import java.util.UUID

/**
 * [InventoryKey] uniquely identifies a daily inventory record. This key is also
 * used for compaction (if compaction is enabled), and also partitioning (which
 * components of the key are used for partitioning is not dictated by the key
 * itself, but by the chosen [TopicPartitioner] implementation).
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class InventoryKey(
    val cskuId: UUID,
    val dcCode: String,
    @JsonFormat(shape = STRING, pattern = "uuuu-MM-dd")
    val date: LocalDate,
)

/**
 * [InventoryValue] is the value of a daily inventory record that can be
 * associated with a specific [InventoryKey]. This value holds data that
 * represents an inventory snapshot for a specific day.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class InventoryValue(val inventory: List<Inventory>) {
    constructor(inventoryEntry: Inventory) : this(listOf(inventoryEntry))

    fun total() = this.inventory.sumOf { it.qty.getValue() }
}

/**
 * [Inventory] holds the quantity and expiration date in which a SKU will be
 * no longer good to be consumed.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class Inventory(
    val qty: SkuQuantity,
    @JsonFormat(shape = STRING, pattern = "uuuu-MM-dd")
    val expiryDate: LocalDate?,
    val location: Location,
    val poReference: String? = null
) {

    @JsonIgnore
    val inventoryLocation = InventoryLocation(location, expiryDate)

    @JsonIgnore
    fun isStaging() = location.type.isStaging()

    @JsonIgnore
    fun isStorage() = !isStaging()

    companion object
}
