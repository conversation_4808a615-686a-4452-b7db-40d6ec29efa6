package com.hellofresh.cif.distributionCenterLib

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.lib.Scheduler
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.unmockkAll
import java.time.Duration
import kotlin.test.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.assertThrows
import org.testcontainers.shaded.org.awaitility.Awaitility

class DcConfigServiceTest {

    private val repo = mockk<DcRepository>(relaxed = true)
    private val scheduler = mockk<Scheduler>(relaxed = true)

    @BeforeEach
    fun clear() {
        unmockkAll()
    }

    @Test fun `repositoy returns error`() {
        val dcConfig = DcConfigService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
        )
        coEvery { repo.fetchDcConfigurations() }.throws(IllegalStateException("Invalid ZoneID"))
        assertThrows<IllegalStateException> { dcConfig.dcConfigurations }
    }

    @Test fun `fetch on demand is truly on demand`() {
        val dcConfig = DcConfigService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
            scheduler = { },
        )

        coEvery { repo.fetchDcConfigurations() } returns listOf(DistributionCenterConfiguration.default())
        dcConfig.dcConfigurations
        dcConfig.fetchOnDemand()
        dcConfig.dcConfigurations
        coVerify(exactly = 2) { repo.fetchDcConfigurations() }
    }

    @Test fun `scheduler repo update is executed`() {
        val dcConfigService = DcConfigService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
            jobTimePeriodSeconds = 2,
        )
        val expectedLastUpdateDcCode = "EXPECTED_LAST_DC"

        coEvery { repo.fetchDcConfigurations() } returns listOf(DistributionCenterConfiguration.default()) andThen listOf(
            DistributionCenterConfiguration.default().copy(dcCode = "EXPECTED_LAST_DC"),
        )
        Awaitility
            .await()
            .pollInterval(Duration.ofSeconds(1))
            .atMost(Duration.ofSeconds(5))
            .until {
                dcConfigService.dcConfigurations[expectedLastUpdateDcCode] != null
            }
    }

    @Test fun `multiple get calls on dcConfigurations trigger demand retrieval only once`() {
        coEvery { repo.fetchDcConfigurations() } returns listOf(DistributionCenterConfiguration.default())
        val dcConfig = DcConfigService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
            scheduler = scheduler,
        )

        dcConfig.dcConfigurations
        dcConfig.dcConfigurations
        dcConfig.dcConfigurations

        coVerify(exactly = 1) { repo.fetchDcConfigurations() }
    }
}
