package com.hellofresh.safetystock.repo.buffer

import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.safetystock.repo.buffer.SafetyStockBuffer.Companion.DEFAULT_BUFFER_PERCENTAGE
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class SafetyStockBuffersTest {
    private val dcCode = "dcCode"

    @Test
    fun `returns matching safety stock buffer for sku and week`() {
        val skuId = UUID.randomUUID()
        val productionWeek = ProductionWeek(LocalDate.now(ZoneOffset.UTC).plusWeeks(1), DayOfWeek.MONDAY)
        val bufferPercentage = BigDecimal.TEN
        val safetyStockBuffers = SafetyStockBuffers(
            listOf(
                SafetyStockBuffer(
                    dcCode,
                    skuId,
                    productionWeek,
                    bufferPercentage,
                ),
                SafetyStockBuffer(
                    dcCode,
                    skuId,
                    productionWeek.plusWeeks(1),
                    DEFAULT_BUFFER_PERCENTAGE,
                ),
            ),
        )
        val safetyStockBuffer = safetyStockBuffers.getBuffer(dcCode, skuId, productionWeek)

        assertEquals(bufferPercentage, safetyStockBuffer.bufferPercentage)
    }

    @Test
    fun `returns default safetyStock buffer for non existing sku`() {
        val safetyStockBuffer =
            SafetyStockBuffers(
                emptyList(),
            ).getBuffer(
                dcCode,
                UUID.randomUUID(),
                ProductionWeek(LocalDate.now(ZoneOffset.UTC).plusWeeks(1), DayOfWeek.MONDAY),
            )
        assertEquals(DEFAULT_BUFFER_PERCENTAGE, safetyStockBuffer.bufferPercentage)
    }

    @Test
    fun `returns matching safetyStock buffer for sku and previous week`() {
        val skuId = UUID.randomUUID()
        val productionWeek = ProductionWeek(LocalDate.now(ZoneOffset.UTC).plusWeeks(1), DayOfWeek.MONDAY)
        val buffer = BigDecimal.TEN
        val safetyStockBuffer = SafetyStockBuffers(
            listOf(
                SafetyStockBuffer(
                    dcCode,
                    skuId,
                    productionWeek.minusWeeks(1),
                    buffer,
                ),
                SafetyStockBuffer(
                    dcCode,
                    skuId,
                    productionWeek.plusWeeks(3),
                    BigDecimal.TEN,
                ),
            ),
        ).getBuffer(dcCode, skuId, productionWeek)

        assertEquals(buffer, safetyStockBuffer.bufferPercentage)
    }

    @Test
    fun `returns default safetyStock buffer if there is no week match (current and past weeks)`() {
        val skuId = UUID.randomUUID()
        val productionWeek = ProductionWeek(LocalDate.now(ZoneOffset.UTC), DayOfWeek.MONDAY)

        val safetyStockBuffer = SafetyStockBuffers(
            listOf(
                SafetyStockBuffer(
                    dcCode,
                    skuId,
                    productionWeek.plusWeeks(3),
                    DEFAULT_BUFFER_PERCENTAGE.plus(BigDecimal.TWO),
                ),
            ),
        ).getBuffer(dcCode, skuId, productionWeek)

        assertEquals(DEFAULT_BUFFER_PERCENTAGE, safetyStockBuffer.bufferPercentage)
    }
}
