package com.hellofresh.cif.safetyStock.job

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.safety.stock.job.schema.Tables
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStocksRecord
import com.hellofresh.cif.safetyStock.service.TestPrepare
import com.hellofresh.cif.safetystock.Configuration
import com.hellofresh.cif.safetystock.SafetyStockConfiguration
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository.Companion.objectMapper
import com.hellofresh.safetystock.job.SafetyStockJob
import com.hellofresh.safetystock.service.SafetyStockService
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.math.BigDecimal.ZERO
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach

class SafetyStockJobTest : TestPrepare() {
    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
    private val safetyStockService = SafetyStockService(dsl, dsl, statsigFeatureFlagClient)
    private val dcConfigServiceMock = mockk<DcConfigService>(relaxed = true)
    private val safetyStockJob = SafetyStockJob(
        safetyStockService,
        dcConfigServiceMock,
        safetyStockRepository,
        safetyStockConfigurationRepository,
    )

    @BeforeEach
    internal fun setUp() {
        coEvery {
            dcConfigServiceMock.dcConfigurations
        } returns mapOf(
            defaultDcCode to defaultDcConfig.copy(market = "US"),
        )
    }

    @Test
    fun `safety stock is calculated for enabled markets`() {
        // given
        insertSkuSpecifications(
            mapOf(
                defaultSafetyStockSku.copy(
                    second = defaultSafetyStockSku.second,
                ),
            ),
        )
        insertDefaultDemand()
        insertSafetyStockBuffer(
            defaultDcCode,
            defaultDcConfig.getCurrentWeek().weekString,
            defaultSafetyStockSku.first,
            BigDecimal("0.2")
        )

        // When
        runBlocking { safetyStockJob.run() }

        // then
        val safetyStocksRecords = dsl.selectFrom(Tables.SAFETY_STOCKS).fetch()
        assertEquals(3, safetyStocksRecords.size)
        assertSafetyStock(4005L, safetyStocksRecords[0])
        assertSafetyStock(1291L, safetyStocksRecords[1])
        assertSafetyStock(1874L, safetyStocksRecords[2])
    }

    @Test
    fun `safety stock is also produced for non calculated values`() {
        // given
        insertSkuSpecifications(
            mapOf(
                defaultSafetyStockSku.copy(
                    second = defaultSafetyStockSku.second,
                ),
            ),
        )
        insertDefaultDemand()
        insertSafetyStockBuffer(
            defaultDcConfig.dcCode,
            defaultDcConfig.getCurrentWeek().weekString,
            defaultSafetyStockSku.first,
            BigDecimal("0.2")
        )

        val oldRecord = SafetyStocksRecord().apply {
            dcCode = defaultDcConfig.dcCode
            week = DcWeek(defaultDcConfig.getLatestProductionStart().plusWeeks(3), defaultDcConfig.productionStart).toString()
            skuId = defaultSku.first
            safetyStock = 123424L
            configuration = JSONB.jsonb(objectMapper.writeValueAsString(Configuration(BigDecimal.TEN, SkuRiskRating.MEDIUM, ZERO)))
        }
        dsl.insertInto(Tables.SAFETY_STOCKS).set(oldRecord).execute()

        // When
        runBlocking { safetyStockJob.run() }

        // then
        val safetyStocksRecords = dsl.selectFrom(Tables.SAFETY_STOCKS).fetch().sortedBy { it.week }
        assertEquals(4, safetyStocksRecords.size)
        assertSafetyStock(4005L, safetyStocksRecords[0])
        assertSafetyStock(1291L, safetyStocksRecords[1])
        assertSafetyStock(1874L, safetyStocksRecords[2])

        assertEquals(oldRecord.week, safetyStocksRecords[3].week)
        assertEquals(oldRecord.dcCode, safetyStocksRecords[3].dcCode)
        assertEquals(oldRecord.skuId, safetyStocksRecords[3].skuId)
        assertEquals(0, safetyStocksRecords[3].safetyStock)
        val configuration = objectMapper.readValue<Configuration>(safetyStocksRecords[3].configuration.data())
        assertEquals(SafetyStockConfiguration.DEFAULT_RISK_MULTIPLIER, configuration.riskMultiplier)
    }

    private fun assertSafetyStock(safetyStock: Long, safetyStocksRecord: SafetyStocksRecord) {
        assertEquals("DC", safetyStocksRecord.dcCode)
        assertEquals(safetyStock, safetyStocksRecord.safetyStock)
        val configuration = objectMapper.readValue<Configuration>(safetyStocksRecord.configuration.data())
        assertEquals(BigDecimal.ONE, configuration.riskMultiplier)
    }
}
