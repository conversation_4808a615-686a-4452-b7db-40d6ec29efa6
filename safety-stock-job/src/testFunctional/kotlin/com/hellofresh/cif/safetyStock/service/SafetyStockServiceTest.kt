package com.hellofresh.cif.safetyStock.service

import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.SafetyStockMultiplierFormula
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.safety.stock.job.schema.enums.SkuRiskRating
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStockImportRecord
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStockMultiplierRecord
import com.hellofresh.cif.safetystock.SafetyStockConfiguration.Companion.DEFAULT_SKU_RISK_RATING
import com.hellofresh.cif.safetystock.SafetyStockConfigurations
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.sqrlib.schema.tables.records.SupplyQuantityRecommendationConfRecord
import com.hellofresh.safetystock.service.DACH_MARKET
import com.hellofresh.safetystock.service.SafetyStockService
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import kotlin.test.Test
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SafetyStockServiceTest : TestPrepare() {
    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
    private val safetyStockService = SafetyStockService(
        dsl,
        dsl,
        statsigFeatureFlagClient,
    )

    @ParameterizedTest
    @CsvSource(
        "US,true,4005",
        "EU,true,20305",
        "ZZ,false,-1",
    )
    fun `safety stock is calculated for enabled markets`(market: String, valid: Boolean, expectedSafetyStock: Long) {
        // given
        insertSkuSpecifications(
            mapOf(
                defaultSafetyStockSku.copy(
                    second = defaultSafetyStockSku.second,
                ),
            ),
        )

        val dcConfigs = setOf(defaultDcConfig.copy(market = market))

        insertSupplierDetails(defaultSku.first, listOf(leadTime to minimumAllowedMLOR), market)
        insertDefaultDemand()
        insertSafetyStockBuffer(
            defaultDcConfig.dcCode,
            defaultDcConfig.getCurrentWeek().weekString,
            defaultSku.first,
            BigDecimal("0.2"),
        )

        // When
        val skuSafetyStock = runBlocking {
            safetyStockService.fetchSkuSafetyStock(
                dcConfigs,
                SafetyStockConfigurations(emptyList()),
            )
        }

        // then
        if (valid) {
            with(ProductionWeek(defaultDcConfig.getLatestProductionStart(), defaultDcConfig.productionStart)) {
                assertEquals(
                    expectedSafetyStock,
                    skuSafetyStock.first { it.toKey() == SafetyStockKey(defaultDcCode, dcWeek, defaultSku.first) }.value,
                )
            }
        } else {
            assertTrue(skuSafetyStock.isEmpty())
        }
    }

    @ParameterizedTest
    @CsvSource(
        "true",
        "false",
    )
    fun `safety stock multiplier formula is used when flag is enabled`(flagEnabled: Boolean) {
        val formulaMarket = "MM"
        val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(
            if (flagEnabled) {
                setOf(
                    SafetyStockMultiplierFormula(setOf(ContextData(MARKET, formulaMarket))),
                )
            } else {
                emptySet()
            },
        )
        val safetyStockService = SafetyStockService(
            dsl,
            dsl,
            statsigFeatureFlagClient,
        )

        val dcConfigs = setOf(defaultDcConfig.copy(market = formulaMarket))
        // given
        insertSkuSpecifications(mapOf(defaultSafetyStockSku))

        dsl.batchInsert(
            SafetyStockMultiplierRecord().apply {
                this.dcCode = defaultDcCode
                this.skuId = defaultSafetyStockSku.first
                this.skuRiskRating = SkuRiskRating.valueOf(DEFAULT_SKU_RISK_RATING.name)
                this.targetWeek = 1
                this.weekCover = 1
                this.targetWeekMultiplier = ONE
                this.weekCoverMultiplier = ONE
            },
        ).execute()
        val currentWeek = ProductionWeek(defaultDcConfig.getLatestProductionStart(), defaultDcConfig.productionStart)
        val targetWeek = currentWeek.plusWeeks(1)
        val expectedSafetyStock = 105230L
        insertDemand(
            listOf(
                createDemand(0, currentWeek.date),
                createDemand(expectedSafetyStock, targetWeek.date),
            ),
        )

        // When
        val skuSafetyStock = runBlocking {
            safetyStockService.fetchSkuSafetyStock(
                dcConfigs,
                SafetyStockConfigurations(emptyList()),
            )
        }

        // then
        if (flagEnabled) {
            with(targetWeek) {
                assertEquals(
                    expectedSafetyStock,
                    skuSafetyStock.first { it.toKey() == SafetyStockKey(defaultDcCode, dcWeek, defaultSku.first) }.value,
                )
            }
        } else {
            assertTrue(skuSafetyStock.isEmpty())
        }
    }

    @Test
    fun `for dach market safety stock imports are also consider`() {
        val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(
            setOf(
                SafetyStockMultiplierFormula(setOf(ContextData(MARKET, DACH_MARKET))),
            ),
        )
        val safetyStockService = SafetyStockService(
            dsl,
            dsl,
            statsigFeatureFlagClient,
        )

        val dcConfigs = setOf(defaultDcConfig.copy(market = DACH_MARKET))
        // given

        val currentWeek = defaultDcConfig.getCurrentWeek()
        dsl.batchInsert(
            SafetyStockImportRecord().apply {
                this.dcCode = defaultDcCode
                this.skuId = defaultSafetyStockSku.first
                this.week = currentWeek.weekString
                this.safetyStock = 1500
            },
            SupplyQuantityRecommendationConfRecord().apply {
                this.dcCode = defaultDcCode
                this.skuId = defaultSafetyStockSku.first
                this.week = currentWeek.weekString
                this.recommendationEnabled = true
                this.multiWeekEnabled = false
            },
        ).execute()

        // When
        val skuSafetyStock = runBlocking {
            safetyStockService.fetchSkuSafetyStock(
                dcConfigs,
                SafetyStockConfigurations(emptyList()),
            )
        }
        // then
        assertEquals(1, skuSafetyStock.size)
        assertEquals(
            1500,
            skuSafetyStock.first { it.toKey() == SafetyStockKey(defaultDcCode, currentWeek.dcWeek, defaultSku.first) }.value
        )
    }
}
