package com.hellofresh.cif.safetyStock.safetymultiplier.service

import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.safety.stock.job.schema.Tables.SAFETY_STOCK_BUFFER
import com.hellofresh.cif.safetyStock.service.TestPrepare
import io.mockk.coEvery
import java.io.ByteArrayInputStream
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

private const val BUCKET_NAME = "hf-group-limesync-gsheet-local-nonsensitive"

class S3USSafetyStockBufferServiceTest : TestPrepare() {

    @Test
    fun `Should import us Safety Stock Parquet File`() {
        val key = "calculated_buffers/test_file.parquet"
        val fileContentInByteArray = readFileContent(key)

        coEvery {
            s3Importer.fetchObjectContent(BUCKET_NAME, key)
        } returns ByteArrayInputStream(fileContentInByteArray)

        insertSkuSpecification(
            listOf(
                TestSkuItem("PRO-10-135756-4", "US"),
                TestSkuItem("PRO-10-135757-4", "US"),
                TestSkuItem("PRO-10-135758-4", "US"),
                TestSkuItem("PRO-10-135759-4", "US"),
                TestSkuItem("BAK-10-10427-1", "US"),
                TestSkuItem("BAK-10-10722-1", "US"),
                TestSkuItem("PRO-10-10395-1", "US"),
            ),
        )

        runBlocking { s3UsSafetyStockBufferService.process(S3File(BUCKET_NAME, key)) }

        val records = dsl.selectFrom(SAFETY_STOCK_BUFFER).fetch()

        assertEquals(7, records.size)
        val week10 = records.filter { it.week == "2025-W10" }
        assertEquals(1, week10.size)
        assertEquals(0.15, week10[0].buffer.toDouble())
    }
}
