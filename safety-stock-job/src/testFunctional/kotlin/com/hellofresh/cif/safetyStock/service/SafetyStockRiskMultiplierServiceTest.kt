package com.hellofresh.cif.safetyStock.service

import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safety.stock.job.schema.enums.SkuRiskRating
import com.hellofresh.cif.safety.stock.job.schema.enums.SkuRiskRating.HIGH
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStockMultiplierRecord
import com.hellofresh.cif.safetystock.SafetyStockConfiguration
import com.hellofresh.cif.safetystock.SafetyStockConfiguration.Companion.DEFAULT_RISK_MULTIPLIER
import com.hellofresh.cif.safetystock.SafetyStockConfiguration.Companion.DEFAULT_SKU_RISK_RATING
import com.hellofresh.cif.safetystock.SafetyStockConfigurations
import com.hellofresh.safetystock.safetymultiplier.repo.SafetyStockMultiplierRepositoryImpl.Companion.toSkuRiskRating
import com.hellofresh.safetystock.service.SafetyStockRiskFormulaService
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SafetyStockRiskMultiplierServiceTest : TestPrepare() {

    @ParameterizedTest
    @CsvSource(
        "0, 1,       1,     1, 1905230",
        "0, 1.2, 0.163, 0.294, 672165",
        "1, 1,       1,     1, 2991468",
        "1, 1,   0.163, 0.294, 1190044",
        "1, 1.2, 0.163, 0.294, 1428052",
        "6, 1,       1,     1, 1816640",
        "6, 1.2,   0.1,   0.1, 1010361",
        "6, 1.2, 0.113, 0.214, 1361885",
        "7, 1,       1,     1, 7988760",
        "7, 1,   0.113, 0.114, 1862142",
        "7, 1.2, 0.163, 0.294, 4465324",
    )
    fun `calculates multiplier formula`(
        targetWeekNumber: Long,
        externalRiskMultiplier: BigDecimal,
        targetWeekMultiplier: BigDecimal,
        weekCoverMultiplier: BigDecimal,
        expectedSafetyStock: Long,
    ) {
        val skuRiskMultiplier = HIGH

        insertSkuSpecifications(mapOf(defaultSafetyStockSku))
        val dcConfigs = setOf(defaultDcConfig)

        dsl.batchInsert(
            (0..7L).map { tw ->
                SafetyStockMultiplierRecord().apply {
                    this.dcCode = defaultDcCode
                    this.skuId = defaultSafetyStockSku.first
                    this.skuRiskRating = SkuRiskRating.entries.first { it != skuRiskMultiplier }
                    this.targetWeek = tw
                    this.weekCover = 1
                    this.targetWeekMultiplier = BigDecimal.ZERO
                    this.weekCoverMultiplier = BigDecimal.ZERO
                }
            } + listOf(
                SafetyStockMultiplierRecord().apply {
                    this.dcCode = defaultDcCode
                    this.skuId = defaultSafetyStockSku.first
                    this.skuRiskRating = skuRiskMultiplier
                    this.targetWeek = targetWeekNumber
                    this.weekCover = 1
                    this.targetWeekMultiplier = targetWeekMultiplier
                    this.weekCoverMultiplier = weekCoverMultiplier
                },
            ),
        ).execute()

        val currentWeek = ProductionWeek(defaultDcConfig.getLatestProductionStart(), defaultDcConfig.productionStart)
        val targetWeek = currentWeek.plusWeeks(targetWeekNumber)
        insertDemand(
            listOf(
                createDemand(1905230, defaultDcConfig.getLatestProductionStart()),
                createDemand(2991468, defaultDcConfig.getLatestProductionStart().plusWeeks(1)),
                createDemand(122936, defaultDcConfig.getLatestProductionStart().plusWeeks(2)),
                createDemand(413740, defaultDcConfig.getLatestProductionStart().plusWeeks(3)),
                createDemand(539389, defaultDcConfig.getLatestProductionStart().plusWeeks(4)),
                createDemand(630278, defaultDcConfig.getLatestProductionStart().plusWeeks(5)),
                createDemand(1815000, defaultDcConfig.getLatestProductionStart().plusWeeks(6)),
                createDemand(1640, defaultDcConfig.getLatestProductionStart().plusWeeks(6).plusDays(1)),
                createDemand(7988760, defaultDcConfig.getLatestProductionStart().plusWeeks(7)),
            ),
        )

        val safetyStockConfigurations = SafetyStockConfigurations(
            listOf(
                SafetyStockConfiguration(
                    defaultDcCode,
                    defaultSafetyStockSku.first,
                    targetWeek,
                    externalRiskMultiplier,
                    toSkuRiskRating(skuRiskMultiplier),
                ),
            ),
        )

        val safetyStocks = runBlocking {
            SafetyStockRiskFormulaService(safetyStockDemandRepository, safetyStockMultiplierRepository)
                .calculateSafetyStocks(dcConfigs, safetyStockConfigurations)
        }

        with(
            safetyStocks.first {
                it.dcCode == defaultDcConfig.dcCode && it.skuId == defaultSafetyStockSku.first &&
                    it.week == targetWeek.weekString
            },
        ) {
            assertEquals(expectedSafetyStock, this.value)
            assertEquals(externalRiskMultiplier, this.configuration.riskMultiplier)
            assertEquals(toSkuRiskRating(skuRiskMultiplier), this.configuration.skuRiskRating)
        }
    }

    @Test
    fun `safety stock is calculated for current week(tw=0)`() {
        insertSkuSpecifications(mapOf(defaultSafetyStockSku))
        val today = LocalDate.now(defaultDcConfig.zoneId)
        val dcConfig = defaultDcConfig.copy(productionStart = today.minusDays(1).dayOfWeek)

        dsl.batchInsert(
            SafetyStockMultiplierRecord().apply {
                this.dcCode = defaultDcCode
                this.skuId = defaultSafetyStockSku.first
                this.skuRiskRating = SkuRiskRating.valueOf(DEFAULT_SKU_RISK_RATING.name)
                this.targetWeek = 0
                this.weekCover = 1
                this.targetWeekMultiplier = TEN
                this.weekCoverMultiplier = TEN
            },
        ).execute()

        val currentWeek = ProductionWeek(dcConfig.getLatestProductionStart(), dcConfig.productionStart)
        val todayDemand = 105230L
        val expectedSafetyStock = 205229L
        insertDemand(
            listOf(
                createDemand(99999, dcConfig.getLatestProductionStart()),
                createDemand(todayDemand, today),
            ),
        )

        val safetyStocks = runBlocking {
            SafetyStockRiskFormulaService(safetyStockDemandRepository, safetyStockMultiplierRepository)
                .calculateSafetyStocks(setOf(dcConfig), SafetyStockConfigurations(listOf()))
        }

        with(
            safetyStocks.first {
                it.dcCode == dcConfig.dcCode && it.skuId == defaultSafetyStockSku.first &&
                    it.week == currentWeek.weekString
            },
        ) {
            assertEquals(expectedSafetyStock.toBigDecimal().toLong(), this.value)
            assertEquals(DEFAULT_RISK_MULTIPLIER, this.configuration.riskMultiplier)
            assertEquals(DEFAULT_SKU_RISK_RATING, this.configuration.skuRiskRating)
        }
    }

    @Test
    fun ` demand for target week exclude past days`() {
        insertSkuSpecifications(mapOf(defaultSafetyStockSku))
        val today = LocalDate.now(UTC)
        val dcConfig = defaultDcConfig.copy(
            zoneId = UTC,
            productionStart = today.minusDays(2).dayOfWeek,
        )

        dsl.batchInsert(
            SafetyStockMultiplierRecord().apply {
                this.dcCode = dcConfig.dcCode
                this.skuId = defaultSafetyStockSku.first
                this.skuRiskRating = SkuRiskRating.valueOf(DEFAULT_SKU_RISK_RATING.name)
                this.targetWeek = 1
                this.weekCover = 1
                this.targetWeekMultiplier = ONE
                this.weekCoverMultiplier = ONE
            },
        ).execute()

        val currentWeek = ProductionWeek(dcConfig.getLatestProductionStart(), dcConfig.productionStart)
        val targetWeek = currentWeek.plusWeeks(1)
        val demandToday = 105230L
        val demandTomorrow = 105230L
        val demandNextWeek = 121468L
        insertDemand(
            listOf(
                createDemand(99999, dcConfig.getLatestProductionStart()),
                createDemand(demandToday, today),
                createDemand(demandTomorrow, today.plusDays(1)),
                createDemand(demandNextWeek, today.plusWeeks(1)),
            ),
        )

        val safetyStocks = runBlocking {
            SafetyStockRiskFormulaService(safetyStockDemandRepository, safetyStockMultiplierRepository)
                .calculateSafetyStocks(
                    setOf(dcConfig),
                    SafetyStockConfigurations(
                        listOf(
                            SafetyStockConfiguration(
                                dcConfig.dcCode,
                                defaultSafetyStockSku.first,
                                targetWeek,
                                ONE,
                                DEFAULT_SKU_RISK_RATING,
                            ),
                        ),
                    ),
                )
        }

        with(
            safetyStocks.first {
                it.dcCode == dcConfig.dcCode && it.skuId == defaultSafetyStockSku.first &&
                    it.week == targetWeek.weekString
            },
        ) {
            assertEquals(demandNextWeek, this.value)
        }
    }
}
