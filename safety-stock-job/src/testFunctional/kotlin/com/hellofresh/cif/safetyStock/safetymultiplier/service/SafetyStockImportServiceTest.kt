package com.hellofresh.cif.safetyStock.safetymultiplier.service

import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.safety.stock.job.schema.Tables.SAFETY_STOCK_IMPORT
import com.hellofresh.cif.safetyStock.service.TestPrepare
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.sku_specification_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.safetystock.safetymultiplier.service.SafetyStockImportService
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import java.io.ByteArrayInputStream
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.jooq.Record
import org.junit.jupiter.api.Test

class SafetyStockImportServiceTest : TestPrepare() {

    private val testBucketName = "testBucketNotification"

    private val safetyStockImportService = SafetyStockImportService(
        s3Importer,
        DcConfigService(SimpleMeterRegistry(), repo = dcConfigRepository),
        SkuSpecificationService(SimpleMeterRegistry(), skuSpecificationsRepo),
        safetyStockImportRepository,
    )

    @Test
    fun `should import safety stock import data`() {
        persistDcConfig(dcCode = "BV", market = "GB")

        val key = "ip_safety_stock/safety_stock/safety_stock_calculated.csv"
        val fileContentInByteArray = readFileContent(key)
        coEvery {
            s3Importer.fetchObjectContent(testBucketName, key)
        } returns ByteArrayInputStream(fileContentInByteArray)

        insertSkuSpecification(
            listOf(
                TestSkuItem("PRO-00-90000-1", "DACH"),
                TestSkuItem("PRO-00-90000-2", "DACH"),
                TestSkuItem("PRO-00-90000-3", "DACH"),
                TestSkuItem("PRO-00-90000-4", "GB"),
                TestSkuItem("PRO-00-90000-5", "GB"),
            ),
        )

        runBlocking { safetyStockImportService.process(S3File(testBucketName, key)) }

        val records = dsl.select()
            .from(SAFETY_STOCK_IMPORT)
            .join(SKU_SPECIFICATION_VIEW)
            .on(SAFETY_STOCK_IMPORT.SKU_ID.eq(SKU_SPECIFICATION_VIEW.ID))
            .fetch()

        assertEquals(5, records.size)

        records.assertSafetyStockImport(1234, "VE", "DACH", "2025-W10", "PRO-00-90000-1")
        records.assertSafetyStockImport(1235, "VE", "DACH", "2025-W11", "PRO-00-90000-2")
        records.assertSafetyStockImport(1236, "VE", "DACH", "2025-W12", "PRO-00-90000-3")
        records.assertSafetyStockImport(1237, "BV", "GB", "2025-W13", "PRO-00-90000-4")
        records.assertSafetyStockImport(1238, "BV", "GB", "2025-W14", "PRO-00-90000-5")
    }

    private fun List<Record>.assertSafetyStockImport(
        safetyStock: Long,
        dcCode: String,
        market: String,
        week: String,
        skuCode: String
    ) {
        assertEquals(
            safetyStock,
            this.first {
                it[SAFETY_STOCK_IMPORT.DC_CODE] == dcCode &&
                    it[SKU_SPECIFICATION_VIEW.MARKET] == market &&
                    it[SKU_SPECIFICATION_VIEW.CODE] == skuCode &&
                    it[SAFETY_STOCK_IMPORT.WEEK] == week
            }[SAFETY_STOCK_IMPORT.SAFETY_STOCK],
        )
    }
}
