package com.hellofresh.cif.safetyStock.safetymultiplier.repo

import com.hellofresh.cif.safety.stock.job.schema.Tables
import com.hellofresh.cif.safety.stock.job.schema.enums.SkuRiskRating
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStockMultiplierRecord
import com.hellofresh.cif.safetyStock.service.TestPrepare
import com.hellofresh.cif.safetystock.model.SafetyStockMultiplier
import com.hellofresh.cif.safetystock.model.SafetyStockMultipliers.Companion.DEFAULT_TARGET_WEED_RISK_MULTIPLIER
import com.hellofresh.cif.safetystock.model.SafetyStockMultipliers.Companion.DEFAULT_WEEK_COVER_RISK_MULTIPLIER
import com.hellofresh.safetystock.safetymultiplier.repo.SafetyStockMultiplierRepositoryImpl.Companion.toSkuRiskRating
import java.util.Random
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import random

class SafetyStockMultiplierRepositoryTest : TestPrepare() {

    @Test
    fun `should persist safety stocks multiplier successfully`() {
        val safetyStockMultiplier1 = SafetyStockMultiplier.random()
        val safetyStockMultiplier2 = SafetyStockMultiplier.random(dcCode = "VE")

        runBlocking {
            safetyStockMultiplierRepository.upsertSafetyStockMultiplier(
                listOf(safetyStockMultiplier1, safetyStockMultiplier2),
            )
        }
        val records = dsl.selectFrom(Tables.SAFETY_STOCK_MULTIPLIER).fetch()
        assertEquals(2, records.size)
        val safetyStockMultipliers = listOf(safetyStockMultiplier1, safetyStockMultiplier2)
        safetyStockMultipliers.forEach { safetyStock ->
            val record = records.first { it.skuId == safetyStock.skuId }
            assertSafetyStockMultiplier(safetyStock, record)
        }
    }

    @Test
    fun `fetches safety stock multiplier by dc codes`() {
        val record1 = randomMultiplierRecord()
        val record2 = randomMultiplierRecord()
        dsl.batchInsert(record1, record2).execute()

        val results =
            runBlocking {
                safetyStockMultiplierRepository.fetchSafetyStockMultiplier(
                    setOf(record1.dcCode, record2.dcCode),
                )
            }

        with(
            results.getSafetyStockMultiplier(
                record1.dcCode,
                record1.skuId,
                toSkuRiskRating(record1.skuRiskRating),
                record1.targetWeek,
                record1.weekCover,
            ),
        ) {
            assertEquals(record1.targetWeekMultiplier, this.targetWeekMultiplier)
            assertEquals(record1.weekCoverMultiplier, this.weekCoverMultiplier)
        }

        with(
            results.getSafetyStockMultiplier(
                record2.dcCode,
                record2.skuId,
                toSkuRiskRating(record2.skuRiskRating),
                record2.targetWeek,
                record2.weekCover,
            ),
        ) {
            assertEquals(record2.targetWeekMultiplier, this.targetWeekMultiplier)
            assertEquals(record2.weekCoverMultiplier, this.weekCoverMultiplier)
        }

        with(
            results.getSafetyStockMultiplier(
                record1.dcCode,
                UUID.randomUUID(),
                toSkuRiskRating(record1.skuRiskRating),
                record1.targetWeek,
                record1.weekCover,
            ),
        ) {
            assertEquals(DEFAULT_TARGET_WEED_RISK_MULTIPLIER, this.targetWeekMultiplier)
            assertEquals(DEFAULT_WEEK_COVER_RISK_MULTIPLIER, this.weekCoverMultiplier)
        }
    }

    @Test
    fun `fetches safety stock multiplier by dc codes and week cover`() {
        val record1 = randomMultiplierRecord()
        val record2 = randomMultiplierRecord().apply { this.dcCode = record1.dcCode }
        dsl.batchInsert(record1, record2).execute()

        val results =
            runBlocking {
                safetyStockMultiplierRepository.fetchSafetyStockMultiplier(
                    setOf(record2.dcCode),
                    record2.weekCover,
                )
            }

        with(
            results.getSafetyStockMultiplier(
                record1.dcCode,
                UUID.randomUUID(),
                toSkuRiskRating(record1.skuRiskRating),
                record1.targetWeek,
                record1.weekCover,
            ),
        ) {
            assertEquals(DEFAULT_TARGET_WEED_RISK_MULTIPLIER, this.targetWeekMultiplier)
            assertEquals(DEFAULT_WEEK_COVER_RISK_MULTIPLIER, this.weekCoverMultiplier)
        }

        with(
            results.getSafetyStockMultiplier(
                record2.dcCode,
                record2.skuId,
                toSkuRiskRating(record2.skuRiskRating),
                record2.targetWeek,
                record2.weekCover,
            ),
        ) {
            assertEquals(record2.targetWeekMultiplier, this.targetWeekMultiplier)
            assertEquals(record2.weekCoverMultiplier, this.weekCoverMultiplier)
        }
    }

    private fun randomMultiplierRecord() =
        SafetyStockMultiplierRecord().apply {
            this.dcCode = UUID.randomUUID().toString()
            this.skuId = UUID.randomUUID()
            this.skuRiskRating = SkuRiskRating.entries.random()
            this.targetWeek = Random().nextLong()
            this.weekCover = Random().nextLong()
            this.targetWeekMultiplier = Random().nextDouble().toBigDecimal()
            this.weekCoverMultiplier = Random().nextDouble().toBigDecimal()
        }

    private fun assertSafetyStockMultiplier(
        safetyStockMultiplier: SafetyStockMultiplier,
        record: SafetyStockMultiplierRecord
    ) {
        assertEquals(safetyStockMultiplier.dcCode, record.dcCode)
        assertEquals(safetyStockMultiplier.skuId, record.skuId)
        assertEquals(safetyStockMultiplier.skuRiskRating.name, record.skuRiskRating.name)
        assertEquals(safetyStockMultiplier.targetWeek, record.targetWeek)
        assertEquals(safetyStockMultiplier.weekCover, record.weekCover)
        assertEquals(safetyStockMultiplier.targetWeekMultiplier, record.targetWeekMultiplier)
        assertEquals(safetyStockMultiplier.weekCoverMultiplier, record.weekCoverMultiplier)
    }
}
