package com.hellofresh.safetystock.service

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.SafetyStockConfigurations
import com.hellofresh.cif.safetystock.model.SafetyStockMultiplier
import com.hellofresh.cif.safetystock.model.SafetyStockMultipliers
import com.hellofresh.safetystock.repo.SafetyStockDemandRepository
import com.hellofresh.safetystock.repo.SafetyStockSkuDemand
import com.hellofresh.safetystock.safetymultiplier.repo.SafetyStockMultiplierRepository
import java.math.BigDecimal
import java.math.BigDecimal.ZERO
import java.math.RoundingMode.UP
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging

const val WEEKS_COVERED = 1L

class SafetyStockRiskFormulaService(
    private val safetyStockDemandRepository: SafetyStockDemandRepository,
    private val safetyStockMultiplierRepository: SafetyStockMultiplierRepository
) : SafetyStockBase() {

    suspend fun calculateSafetyStocks(
        dcConfigs: Set<DistributionCenterConfiguration>,
        safetyStockConfigurations: SafetyStockConfigurations
    ): List<SafetyStock> {
        if (dcConfigs.isEmpty()) return emptyList()

        val dcConfigsMap = dcConfigs.associateBy { it.dcCode }

        val safetyStockDemands = safetyStockDemandRepository.fetchSafetyStockSkuDemand(
            dcConfigsMap.minOf { it.value.getLatestProductionStart() },
            dcConfigsMap.keys,
        )
        val safetyStockMultipliers = safetyStockMultiplierRepository.fetchSafetyStockMultiplier(
            dcConfigsMap.keys,
        )

        return safetyStockDemands.groupBy { it.dcCode to it.skuId }
            .flatMap { (key, skuDemands) ->
                val (dcCode, skuId) = key
                dcConfigsMap[dcCode]?.let { dc ->
                    calculateSafetyStocksForSku(dc, skuId, skuDemands, safetyStockConfigurations, safetyStockMultipliers)
                } ?: emptyList()
            }
    }

    private fun calculateSafetyStocksForSku(
        dc: DistributionCenterConfiguration,
        skuId: UUID,
        skuDemands: List<SafetyStockSkuDemand>,
        safetyStockConfigurations: SafetyStockConfigurations,
        safetyStockMultipliers: SafetyStockMultipliers
    ): List<SafetyStock> = run {
        val productionStartDate = dc.getLatestProductionStart()
        val currentWeek = ProductionWeek(productionStartDate, dc.productionStart)
        val skuDemandsByWeek = skuDemands.groupBy { ProductionWeek(it.date, dc.productionStart) }
            .mapValues { (_, demands) ->
                demands
                    .filter { it.date >= productionStartDate }
                    .sumOf { it.quantity }
            }

        skuDemandsByWeek.keys.maxOrNull()
            ?.let { maxDemandWeek ->

                val dcWeekTo = maxOf(maxDemandWeek, currentWeek)

                (currentWeek..dcWeekTo)
                    .mapIndexedNotNull { targetWeekNumber, targetWeek ->
                        val configuration = safetyStockConfigurations.getConfiguration(dc.dcCode, skuId, targetWeek)

                        val multipliers = safetyStockMultipliers.getSafetyStockMultiplier(
                            dc.dcCode,
                            skuId,
                            configuration.skuRiskRating,
                            targetWeekNumber.toLong(),
                            WEEKS_COVERED,
                        )

                        val demandTwQty = (currentWeek..<targetWeek).sumOf { skuDemandsByWeek[it] ?: ZERO }
                        val demandWcQty = (targetWeek..<targetWeek.plusWeeks(WEEKS_COVERED.toInt())).sumOf { skuDemandsByWeek[it] ?: ZERO }

                        val skuSafetyStock = if (demandWcQty == ZERO) {
                            ZERO
                        } else {
                            calculateSkuSafetyStock(
                                demandTwQty,
                                demandWcQty,
                                multipliers,
                                configuration.riskMultiplier,
                            )
                        }

                        SafetyStock(
                            dc.dcCode, skuId, targetWeek.weekString, skuSafetyStock.setScale(0, UP).toLong(),
                            configuration.riskMultiplier, configuration.skuRiskRating,
                        )
                    }
            } ?: emptyList()
    }

    private fun calculateSkuSafetyStock(
        demandTwQty: BigDecimal,
        demandWcQty: BigDecimal,
        multipliers: SafetyStockMultiplier,
        riskMultiplier: BigDecimal
    ): BigDecimal {
        val safetyStockQty = (
            demandTwQty.multiply(multipliers.targetWeekMultiplier)
            )
            .plus(
                demandWcQty.multiply(multipliers.weekCoverMultiplier),
            )
            .multiply(riskMultiplier)

        return capSafetyStock(
            safetyStockQty.toLong(),
            demandWcQty.toLong()
        ).toBigDecimal()
    }

    companion object : Logging
}
