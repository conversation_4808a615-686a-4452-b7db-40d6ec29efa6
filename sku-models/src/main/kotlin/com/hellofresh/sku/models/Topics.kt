package com.hellofresh.sku.models

import com.hellofresh.cif.lib.kafka.serde.serde
import com.hellofresh.topic.Topic
import com.hellofresh.topicConfig.TopicConfig.Companion.getTopicVersion

private const val TOPIC_NAME = "csku-inventory-forecast.intermediate.sku-specification"
val SKU_SPECIFICATION_TOPIC = "$TOPIC_NAME.v${getTopicVersion(TOPIC_NAME)}"

/**
 * [Topic] model of  `csku-inventory-forecast.intermediate.sku-specification`. Might
 *  be removed when we remove kafka streams as this is needed only for the
 *  streaming applications
 */
val skuSpecificationTopic = Topic<SkuId, SkuSpecification>(
    prefix = TOPIC_NAME,
    version = getTopicVersion(TOPIC_NAME),
    keySerdeSupplier = { SkuId },
    valSerdeSupplier = ::serde,
)
