package com.hellofresh.sku.models

import java.time.LocalDate
import java.util.UUID

data class SupplierSkuDetail(
    val supplierId: UUID,
    val supplierName: String,
    val mlor: Int,
    val leadTimes: List<LeadTime>
) {
    fun getMaxLeadTimeByDate(date: LocalDate) =
        leadTimes.filter {
            isValidSupplierSkuDetail(it, date)
        }.maxByOrNull { it.leadTime }?.leadTime
    private fun isValidSupplierSkuDetail(leadTime: LeadTime, date: LocalDate) =
        date >= leadTime.startDate && date <= leadTime.endDate
}
data class LeadTime(
    val leadTime: Int,
    val startDate: LocalDate,
    val endDate: LocalDate,
)
