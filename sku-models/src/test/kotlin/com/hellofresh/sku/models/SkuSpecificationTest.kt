package com.hellofresh.sku.models

import com.hellofresh.sku.models.SkuSpecification.Companion.daysBeforeExpiry
import kotlin.test.Test
import kotlin.test.assertEquals

class SkuSpecificationTest {
    @Test
    fun `should have daysBeforeExpiry defaulting to 5 days if acceptableCodeLife is less or equal to 0`() {
        assertEquals(DEFAULT_MAX_DAYS_BEFORE_EXPIRY, daysBeforeExpiry(0))
        assertEquals(DEFAULT_MAX_DAYS_BEFORE_EXPIRY, daysBeforeExpiry(-1))
    }

    @Test
    fun `should have daysBeforeExpiry reflect the sku acceptableCodeLife if greater than 0`() {
        (1..30).forEach {
            assertEquals(it.toLong(), daysBeforeExpiry(it))
        }
    }
}
