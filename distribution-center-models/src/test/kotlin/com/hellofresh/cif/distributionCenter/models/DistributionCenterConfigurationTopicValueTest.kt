package com.hellofresh.cif.distributionCenter.models

import java.time.DayOfWeek.THURSDAY
import kotlin.test.Test

class DistributionCenterConfigurationTopicValueTest {
    private val BENELUXFR = "BENELUXFR"

    @Test fun `parses DcCode and DistributionCenterConfigurationTopicValue( correctly`() {
        ParseTest(
            actualKey = """KP""",
            actualVal = """
                {
                  "market": "$BENELUXFR",
                  "production_start": "THURSDAY",
                  "cleardown": "THURSDAY",
                  "zone_id": "Europe/Berlin",
                  "enabled": false,
                  "has_cleardown": true,
                  "global_dc": "GL",
                  "wms_type": "UNRECOGNIZED",
                  "po_cutoff_time": "23:59:59"
                }
                 """,
            expectedKey = "KP",
            expectedValue = DistributionCenterConfigurationTopicValue(
                productionStart = THURSDAY,
                cleardown = THURSDAY,
                zoneId = EuBerlinZoneId,
                market = BENELUXFR,
                enabled = false,
                hasCleardown = true,
                globalDc = "GL",
                wmsType = WmsSystem.UNRECOGNIZED,
                poCutoffTime = "23:59:59",
            ),
            exception = false,
        ).runTest<String, DistributionCenterConfigurationTopicValue>()
    }

    @Test fun `parses DistributionCenterConfigurationTopicValue with default optional fields `() {
        ParseTest(
            actualKey = """KP""",
            actualVal = """
                {
                  "market": "$BENELUXFR",
                  "production_start": "THURSDAY",
                  "cleardown": "THURSDAY",
                  "zone_id": "Europe/Berlin",
                  "enabled": true,
                  "wms_type": "UNRECOGNIZED",
                  "po_cutoff_time": "23:59:59"
                }
                 """,
            expectedKey = "KP",
            expectedValue = DistributionCenterConfigurationTopicValue(
                productionStart = THURSDAY,
                cleardown = THURSDAY,
                zoneId = EuBerlinZoneId,
                market = BENELUXFR,
                enabled = true,
                hasCleardown = false,
                globalDc = null,
                wmsType = WmsSystem.UNRECOGNIZED,
                poCutoffTime = "23:59:59",
            ),
            exception = false,
        ).runTest<String, DistributionCenterConfigurationTopicValue>()
    }
}
