package com.hellofresh.cif.distributionCenter.models

import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneId

class ProductionWeek(
    val date: LocalDate,
    val productionStartDay: DayOfWeek,
) : Comparable<ProductionWeek> {

    val dcWeek: DcWeek = DcWeek(date, productionStartDay)

    val weekString: String
        get() = dcWeek.value

    constructor(week: String, productionStart: DayOfWeek, zoneId: ZoneId) : this(
        DcWeek(week).getStartDateInDcWeek(productionStart, zoneId),
        productionStart,
    )

    fun plusWeeks(weeks: Long) = ProductionWeek(date.plusWeeks(weeks), productionStartDay)
    fun plusWeeks(weeks: Int) = plusWeeks(weeks.toLong())
    fun minusWeeks(weeks: Long) = ProductionWeek(date.minusWeeks(weeks), productionStartDay)
    fun minusWeeks(weeks: Int) = minusWeeks(weeks.toLong())

    override fun compareTo(other: ProductionWeek): Int =
        this.dcWeek.year.compareTo(other.dcWeek.year).let { yearResult ->
            if (yearResult == 0) {
                this.dcWeek.week.compareTo(other.dcWeek.week)
            } else {
                yearResult
            }
        }

    operator fun rangeTo(productionWeek: ProductionWeek) = DcConfigWeekRange(this, productionWeek)

    operator fun rangeUntil(productionWeek: ProductionWeek) = DcConfigWeekOpenRange(this, productionWeek)

    override fun toString() = "ProductionWeek[$dcWeek, $date, $productionStartDay]"

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as ProductionWeek
        if (productionStartDay != other.productionStartDay) return false
        if (dcWeek != other.dcWeek) return false
        return true
    }

    override fun hashCode(): Int {
        var result = productionStartDay.hashCode()
        result = 31 * result + dcWeek.hashCode()
        return result
    }
}

class DcConfigWeekRange(
    override val start: ProductionWeek,
    override val endInclusive: ProductionWeek
) :
    Iterable<ProductionWeek>,
    ClosedRange<ProductionWeek> {

    override fun iterator(): Iterator<ProductionWeek> = DcWeekIterator(start, endInclusive, true)

    override fun toString(): String = "$start..$endInclusive"
}

class DcConfigWeekOpenRange(
    override val start: ProductionWeek,
    override val endExclusive: ProductionWeek
) :
    Iterable<ProductionWeek>,
    OpenEndRange<ProductionWeek> {

    override fun iterator(): Iterator<ProductionWeek> = DcWeekIterator(start, endExclusive, false)

    override fun toString(): String = "$start..$endExclusive"
}

private class DcWeekIterator(val start: ProductionWeek, val end: ProductionWeek, val endInclusive: Boolean) : Iterator<ProductionWeek> {

    private var index = 0

    override fun hasNext(): Boolean = with(computeNext(index)) {
        if (endInclusive) {
            this <= end
        } else {
            this < end
        }
    }

    override fun next(): ProductionWeek {
        if (!hasNext()) throw NoSuchElementException()
        return computeNext(index++)
    }

    private fun computeNext(index: Int) = start.plusWeeks(index)
}
