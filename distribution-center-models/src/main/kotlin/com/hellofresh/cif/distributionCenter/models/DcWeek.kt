package com.hellofresh.cif.distributionCenter.models

import com.fasterxml.jackson.annotation.JsonValue
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.WeekFields

data class DcWeek(@get:JsonValue val value: String) {
    val week: Int = value.split("-W")[1].toInt()
    val year: Int = value.split("-")[0].toInt()

    override fun toString() = value
    fun getStartDateInDcWeek(productionStart: DayOfWeek, zoneId: ZoneId): LocalDate =
        localDateFromYearWeek(this, zoneId).minusWeeks(1).with(productionStart)

    fun getLastDateInDcWeek(productionStart: DayOfWeek, zoneId: ZoneId): LocalDate =
        localDateFromYearWeek(this, zoneId).minusWeeks(1).with(productionStart)
            .plusDays(LAST_DC_WEEK_DAY)
    private fun localDateFromYearWeek(dcWeek: DcWeek, zoneId: ZoneId): LocalDate =
        LocalDate.now(zoneId)
            .withYear(dcWeek.year)
            .with(WeekFields.ISO.weekOfYear(), dcWeek.week.toLong())

    companion object {
        private const val LAST_DC_WEEK_DAY = 6L
        operator fun invoke(date: LocalDate, productionStart: DayOfWeek): DcWeek {
            val actualWeekDate = if (date.dayOfWeek < productionStart) {
                date
            } else {
                date.plusWeeks(1)
            }
            val weekFields = WeekFields.ISO
            val week = actualWeekDate[weekFields.weekOfWeekBasedYear()]
            val year = actualWeekDate[weekFields.weekBasedYear()]
            return DcWeek("$year-W${"%02d".format(week)}")
        }
    }
}
