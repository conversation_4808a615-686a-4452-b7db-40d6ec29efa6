package com.hellofresh.cif.purchaseorder.advanceshippingnotice

import com.hellofresh.cif.advanceshippingnotice.AdvanceShippingNoticeService
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.purchaseorder.PurchaseOrderAsnProtoMessageBuilder
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn
import io.micrometer.core.instrument.MeterRegistry
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.Duration
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.kafka.common.record.TimestampType.CREATE_TIME
import org.junit.jupiter.api.Test

class AdvanceShippingNoticeProcessorTest {
    @Test
    fun `should be able to process advance shipping notice topic records`() {
        // given
        val meterRegistryMock = mockk<MeterRegistry>(relaxed = true)
        val advanceShippingNoticeServiceMock = mockk<AdvanceShippingNoticeService>(relaxed = true)
        val mockKafkaConsumer = mockk<KafkaConsumer<String, PurchaseOrderAsn>>(relaxed = true)
        val processor = CoroutinesProcessor<String, PurchaseOrderAsn>(
            PollConfig(
                kotlin.time.Duration.parse("PT1S"),
                20L,
                kotlin.time.Duration.parse("PT15S"),
            ),
            mockKafkaConsumer,
            meterRegistryMock,
            advanceShippingNoticeServiceMock::processRecords,
            IgnoreAndContinueProcessing(
                meterRegistryMock,
                "asn_processor_write_failure"
            )
        )

        val purchaseOrderAsnProto = PurchaseOrderAsnProtoMessageBuilder.createPurchaseOrderAsnProto()
        val consumerRecord = createConsumerRecord(purchaseOrderAsnProto)
        val slot = slot<
            ConsumerRecords<
                String,
                PurchaseOrderAsn,
                >,
            >()

        coEvery {
            advanceShippingNoticeServiceMock.processRecords(capture(slot))
        } returns Unit

        every {
            mockKafkaConsumer.poll(any<Duration>())
        } returns ConsumerRecords(mapOf(mockk<TopicPartition>() to mutableListOf(consumerRecord))) andThenAnswer {
            processor.close()
            ConsumerRecords.empty()
        }

        // when
        runBlocking { processor.run() }

        // then
        verify { mockKafkaConsumer.poll(any<Duration>()) }
        verify { mockKafkaConsumer.commitAsync() }

        val advanceShippingNoticeRecords = slot.captured
        assertEquals(purchaseOrderAsnProto.asnId, advanceShippingNoticeRecords.first().key())
        advanceShippingNoticeRecords.first().value().apply {
            assertEquals(purchaseOrderNumber, purchaseOrderNumber)
            assertEquals(purchaseOrderId, purchaseOrderId)
            assertEquals(purchaseOrderAsnProto.shipment.distributionCenter, shipment.distributionCenter)
            assertEquals(purchaseOrderAsnProto.shipment.plannedDeliveryTime, shipment.plannedDeliveryTime)
            assertEquals(purchaseOrderAsnProto.supplier.id, supplier.id)
            assertEquals(purchaseOrderAsnProto.getItems(0).skuCode, itemsList.first().skuCode)
            assertEquals(
                purchaseOrderAsnProto.getItems(0).shippedQuantity.casePackaging.size,
                itemsList.first().shippedQuantity.casePackaging.size,
            )
        }
    }

    private fun createConsumerRecord(
        value: PurchaseOrderAsn?
    ): ConsumerRecord<String, PurchaseOrderAsn> =
        ConsumerRecord(
            "advanceShippingNoticeTopicMock",
            1,
            1L,
            1676991214923L,
            CREATE_TIME,
            8,
            234,
            value?.asnId,
            value,
            RecordHeaders(),
            null,
        )
}
