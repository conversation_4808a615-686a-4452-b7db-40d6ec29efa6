application.name=purchase-order-service
parallelism=3

### Kafka Consumer properties
group.id=csku-inventory-forecast.purchase-order.v2
group.asn.id=csku-inventory-forecast.advance-shipping-notice.v1
auto.offset.reset=earliest

group.id.yf=csku-inventory-forecast.purchase-order-yf.v1

# 5 min
max.poll.interval.ms=600000
# poll will either wait 5 seconds or for the 20KB or 500 records
fetch.min.bytes=20000
fetch.max.wait.ms=5000
max.poll.records=500

### Processor config
poll.interval_ms=20
poll.timeout=PT1S
process.timeout=PT60S

HF_STATSIG_SDK_KEY=statsig-sdk-key
