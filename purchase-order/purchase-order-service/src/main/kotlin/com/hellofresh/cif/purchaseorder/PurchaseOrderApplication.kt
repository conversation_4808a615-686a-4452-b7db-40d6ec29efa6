package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.advanceshippingnotice.AdvanceShippingNoticeService
import com.hellofresh.cif.advanceshippingnotice.repo.AdvanceShippingNoticeRepositoryImpl
import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategyType.LOG_ERROR_IGNORE
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.kafka.serde.ProtoDeserializer
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.purchaseorder.db.PurchaseOrderRepositoryImpl
import com.hellofresh.cif.purchaseorder.deserializer.PurchaseOrderValueTopicDeserializer
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder
import com.hellofresh.cif.purchaseorder.deserializer.YfPurchaseOrderValueTopicDeserializer
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn
import io.micrometer.core.instrument.MeterRegistry
import kotlin.time.Duration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.common.serialization.Deserializer
import org.apache.kafka.common.serialization.StringDeserializer

private const val STATUS_SERVER_HTTP_PORT = 8081
private const val PO_TOPIC = "public.supply.procurement.purchase-order.v1"
private const val YF_PO_TOPIC = "public.ye.supply.procurement.purchase-order.v2"
private const val PURCHASE_ORDER_ADVANCE_SHIPPING_NOTICE_TOPIC = "public.supply.advance-shipping-notice.v2"

@Suppress("LongMethod")
suspend fun main() {
    val meterRegistry = createMeterRegistry()
    val parallelism = ConfigurationLoader.getIntegerOrDefault("parallelism", 1)
    val readMetricsDSLContext = DBConfiguration.jooqReadOnlyDslContext(parallelism, meterRegistry)

    StatusServer.run(
        meterRegistry,
        STATUS_SERVER_HTTP_PORT
    )
    val pollTimeout = Duration.parse(ConfigurationLoader.getStringOrFail("poll.timeout"))
    val pollInterval = ConfigurationLoader.getIntegerOrFail("poll.interval_ms").toLong()
    val processTimeout = Duration.parse(ConfigurationLoader.getStringOrFail("process.timeout"))
    val baseKafkaConfig = ConfigurationLoader.loadKafkaConsumerConfigurations()
    val kafkaConfig = baseKafkaConfig + mapOf(
        ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false",
    )
    val kafkaConfigForAsn: Map<String, String> = baseKafkaConfig + mapOf(
        ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false",
        ConsumerConfig.GROUP_ID_CONFIG to ConfigurationLoader.getStringOrDefault("group.asn.id", ""),
    )
    val readWriteMetricsDSLContext = DBConfiguration.jooqMasterDslContext(parallelism, meterRegistry)
    val purchaseOrderRepository = PurchaseOrderRepositoryImpl(readWriteMetricsDSLContext)
    val statsigFeatureFlagClient = statsigFeatureFlagClient()

    val dcConfigService = DcConfigService(meterRegistry, DcRepositoryImpl(readMetricsDSLContext))
    val purchaseOrderService = PurchaseOrderService(
        purchaseOrderRepository,
        SkuSpecificationService(meterRegistry),
        statsigFeatureFlagClient,
    )
    val processorConfig = ProcessorConfig(pollTimeout, pollInterval, processTimeout)
    val advanceShippingNoticeService = AdvanceShippingNoticeService(
        AdvanceShippingNoticeRepositoryImpl(readWriteMetricsDSLContext),
        dcConfigService,
        SkuSpecificationService(meterRegistry),
    )

    withContext(Dispatchers.IO) {
        repeat(parallelism) {
            launch {
                runPurchaseOrderService(
                    processorConfig = processorConfig,
                    kafkaConfig = kafkaConfig,
                    meterRegistry = meterRegistry,
                    purchaseOrderService = purchaseOrderService,
                    topic = PO_TOPIC,
                    deserializer = PurchaseOrderValueTopicDeserializer(dcConfigService),
                )
            }
        }
        repeat(parallelism) {
            launch {
                runPurchaseOrderService(
                    processorConfig = processorConfig,
                    kafkaConfig = baseKafkaConfig + mapOf(
                        ConsumerConfig.GROUP_ID_CONFIG to ConfigurationLoader.getStringOrFail("group.id.yf"),
                    ),
                    meterRegistry = meterRegistry,
                    purchaseOrderService = purchaseOrderService,
                    topic = YF_PO_TOPIC,
                    deserializer = YfPurchaseOrderValueTopicDeserializer(dcConfigService),
                )
            }
        }
        repeat(parallelism) {
            launch {
                runASNService(
                    processorConfig = ProcessorConfig(pollTimeout, pollInterval, processTimeout),
                    kafkaConfig = kafkaConfigForAsn,
                    meterRegistry = meterRegistry,
                    advanceShippingNoticeService = advanceShippingNoticeService,
                )
            }
        }
    }
}

private fun statsigFeatureFlagClient() = StatsigFactory.build(
    ::shutdownHook,
    sdkKey = ConfigurationLoader.getStringOrFail("HF_STATSIG_SDK_KEY"),
    userId = ConfigurationLoader.getStringOrFail("application.name"),
    isOffline = ConfigurationLoader.getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
    hfTier = ConfigurationLoader.getStringOrFail("HF_TIER"),
)

@Suppress("LongParameterList")
private suspend fun runPurchaseOrderService(
    processorConfig: ProcessorConfig,
    kafkaConfig: Map<String, String>,
    meterRegistry: HelloFreshMeterRegistry,
    purchaseOrderService: PurchaseOrderService,
    topic: String,
    deserializer: Deserializer<UpstreamPurchaseOrder>
) {
    createPOProcessor(
        processorConfig,
        kafkaConfig,
        meterRegistry,
        purchaseOrderService,
        topic,
        deserializer,
    ).run()
}

@Suppress("LongParameterList")
private fun createPOProcessor(
    processorConfig: ProcessorConfig,
    kafkaConfiguration: Map<String, String>,
    meterRegistry: MeterRegistry,
    purchaseOrderService: PurchaseOrderService,
    topic: String,
    deserializer: Deserializer<UpstreamPurchaseOrder>
) =
    shutdownNeeded {
        val consumerProcessorConfig = ConsumerProcessorConfig(
            kafkaConfiguration,
            StringDeserializer(),
            deserializer,
            listOf(topic),
        )
        CoroutinesProcessor<String, UpstreamPurchaseOrder>(
            pollConfig = PollConfig(
                processorConfig.pollTimeout,
                processorConfig.pollInterval,
                processorConfig.processTimeout,
            ),
            consumerProcessorConfig = consumerProcessorConfig,
            meterRegistry = meterRegistry,
            process = purchaseOrderService::processRecords,
            handleDeserializationException = DeserializationExceptionStrategy.create(
                LOG_ERROR_IGNORE,
                meterRegistry,
            ),
            recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                meterRegistry,
                "po_processor_write_failure",
            ),
        ).also {
            HealthChecks.add(it)
            StartUpChecks.add(it)
        }
    }

private suspend fun runASNService(
    processorConfig: ProcessorConfig,
    kafkaConfig: Map<String, String>,
    meterRegistry: HelloFreshMeterRegistry,
    advanceShippingNoticeService: AdvanceShippingNoticeService
) {
    createASNProcessor(
        processorConfig,
        kafkaConfig,
        meterRegistry,
        advanceShippingNoticeService,
    ).also {
        HealthChecks.add(it)
        StartUpChecks.add(it)
    }.run()
}

private fun createASNProcessor(
    processorConfig: ProcessorConfig,
    kafkaConfiguration: Map<String, String>,
    meterRegistry: MeterRegistry,
    advanceShippingNoticeService: AdvanceShippingNoticeService
) =
    shutdownNeeded {
        val consumerProcessorConfig = ConsumerProcessorConfig(
            kafkaConfiguration,
            StringDeserializer(),
            ProtoDeserializer<PurchaseOrderAsn>(),
            listOf(PURCHASE_ORDER_ADVANCE_SHIPPING_NOTICE_TOPIC),
        )
        CoroutinesProcessor<String, PurchaseOrderAsn>(
            pollConfig = PollConfig(
                processorConfig.pollTimeout,
                processorConfig.pollInterval,
                processorConfig.processTimeout,
            ),
            consumerProcessorConfig = consumerProcessorConfig,
            meterRegistry = meterRegistry,
            process = advanceShippingNoticeService::processRecords,
            handleDeserializationException = DeserializationExceptionStrategy.create(
                LOG_ERROR_IGNORE,
                meterRegistry,
            ),
            recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                meterRegistry,
                "asn_processor_write_failure",
            ),
        )
    }

data class ProcessorConfig(
    val pollTimeout: Duration,
    val pollInterval: Long,
    val processTimeout: Duration
)
