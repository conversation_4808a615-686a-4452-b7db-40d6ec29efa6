package com.hellofresh.cif.purchaseorder.db

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.purchaseorder.model.Po
import com.hellofresh.cif.purchaseorder.model.PoStatus
import com.hellofresh.cif.purchaseorder.model.PoStatus.Accepted
import com.hellofresh.cif.purchaseorder.model.PoStatus.Planned
import com.hellofresh.cif.purchaseorder.model.PoStatus.Sent
import com.hellofresh.cif.purchaseorder.schema.Tables.PURCHASE_ORDER
import com.hellofresh.cif.purchaseorder.schema.Tables.PURCHASE_ORDER_SKU
import com.hellofresh.cif.purchaseorder.schema.enums.PurchaseOrderStatus
import com.hellofresh.cif.purchaseorder.schema.enums.Uom
import com.hellofresh.cif.purchaseorder.schema.enums.Uom.UOM_UNIT
import java.math.BigDecimal
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.DSLContext

class PurchaseOrderRepositoryImpl(private val metricsDSLContext: MetricsDSLContext) : PurchaseOrderRepository {

    private val savePurchaseOrder = "save-purchase-order"
    private val deletePurchaseOrder = "delete-purchase-order"

    override suspend fun upsert(purchaseOrder: Po) {
        logger.info("Saving/Updating po: $purchaseOrder")

        metricsDSLContext.withTagName(savePurchaseOrder)
            .transactionAsync { tx ->
                val txDsl = tx.dsl()
                val purchaseOrderStatus = toPurchaseOrderStatus(purchaseOrder.status)
                txDsl.insertInto(PURCHASE_ORDER)
                    .columns(
                        PURCHASE_ORDER.PO_REF,
                        PURCHASE_ORDER.PO_ID,
                        PURCHASE_ORDER.PO_NUMBER,
                        PURCHASE_ORDER.DC_CODE,
                        PURCHASE_ORDER.EXPECTED_ARRIVAL_START_TIME,
                        PURCHASE_ORDER.EXPECTED_ARRIVAL_END_TIME,
                        PURCHASE_ORDER.SUPPLIER_ID,
                        PURCHASE_ORDER.SUPPLIER_NAME,
                        PURCHASE_ORDER.STATUS
                    ).values(
                        purchaseOrder.poRef,
                        purchaseOrder.poId,
                        purchaseOrder.poNumber,
                        purchaseOrder.dc,
                        purchaseOrder.expectedArrivalStartTime,
                        purchaseOrder.expectedArrivalEndTime,
                        purchaseOrder.supplierId,
                        purchaseOrder.supplierName,
                        purchaseOrderStatus,
                    ).onDuplicateKeyUpdate()
                    .set(PURCHASE_ORDER.PO_ID, purchaseOrder.poId)
                    .set(PURCHASE_ORDER.PO_REF, purchaseOrder.poRef)
                    .set(PURCHASE_ORDER.DC_CODE, purchaseOrder.dc)
                    .set(PURCHASE_ORDER.EXPECTED_ARRIVAL_START_TIME, purchaseOrder.expectedArrivalStartTime)
                    .set(PURCHASE_ORDER.EXPECTED_ARRIVAL_END_TIME, purchaseOrder.expectedArrivalEndTime)
                    .set(PURCHASE_ORDER.SUPPLIER_ID, purchaseOrder.supplierId)
                    .set(PURCHASE_ORDER.SUPPLIER_NAME, purchaseOrder.supplierName)
                    .set(PURCHASE_ORDER.STATUS, purchaseOrderStatus)
                    .execute()

                with(createInsertUpdateSkuBatch(txDsl)) {
                    purchaseOrder.skus.map { poSku ->
                        bind(
                            purchaseOrder.poNumber,
                            poSku.id,
                            poSku.quantity,
                            toUom(poSku.skuUOM),
                            poSku.quantity,
                            toUom(poSku.skuUOM)
                        )
                    }
                    execute()
                }
                txDsl.deleteFrom(PURCHASE_ORDER_SKU)
                    .where(PURCHASE_ORDER_SKU.PO_NUMBER.eq(purchaseOrder.poNumber))
                    .and(PURCHASE_ORDER_SKU.SKU_ID.notIn(purchaseOrder.skus.map { it.id }))
                    .execute()
            }.await()
    }

    private fun createInsertUpdateSkuBatch(dslContext: DSLContext) = dslContext.batch(
        dslContext.insertInto(PURCHASE_ORDER_SKU)
            .columns(
                PURCHASE_ORDER_SKU.PO_NUMBER,
                PURCHASE_ORDER_SKU.SKU_ID,
                PURCHASE_ORDER_SKU.QUANTITY,
                PURCHASE_ORDER_SKU.UOM,
            ).values(
                "",
                UUID(0, 0),
                BigDecimal.ZERO,
                UOM_UNIT,
            ).onDuplicateKeyUpdate()
            .set(PURCHASE_ORDER_SKU.QUANTITY, BigDecimal.ZERO)
            .set(PURCHASE_ORDER_SKU.UOM, UOM_UNIT)
    )

    override suspend fun delete(poNumber: String) {
        logger.info("Deleting po with number: $poNumber")

        metricsDSLContext.withTagName(deletePurchaseOrder)
            .transactionAsync { tx ->
                val txDsl = tx.dsl()

                txDsl.deleteFrom(PURCHASE_ORDER_SKU).where(PURCHASE_ORDER_SKU.PO_NUMBER.eq(poNumber)).execute()

                txDsl.deleteFrom(PURCHASE_ORDER).where(PURCHASE_ORDER.PO_NUMBER.eq(poNumber)).execute()
            }.await()
    }

    private fun toUom(skuUOM: SkuUOM): Uom =
        when (skuUOM) {
            SkuUOM.UOM_UNIT -> UOM_UNIT

            SkuUOM.UOM_KG -> Uom.UOM_KG
            SkuUOM.UOM_LBS -> Uom.UOM_LBS
            SkuUOM.UOM_OZ -> Uom.UOM_OZ
            SkuUOM.UOM_GAL -> Uom.UOM_GAL
            SkuUOM.UOM_LITRE -> Uom.UOM_LITRE
            SkuUOM.UOM_UNRECOGNIZED, SkuUOM.UOM_UNSPECIFIED -> Uom.UOM_UNSPECIFIED
        }

    private fun toPurchaseOrderStatus(poStatus: PoStatus?) =
        poStatus?.let {
            when (poStatus) {
                Planned -> PurchaseOrderStatus.Planned
                Sent -> PurchaseOrderStatus.Sent
                Accepted -> PurchaseOrderStatus.Accepted
            }
        }

    companion object : Logging
}
