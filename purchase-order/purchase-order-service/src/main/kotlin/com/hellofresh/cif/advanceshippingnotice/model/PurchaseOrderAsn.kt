package com.hellofresh.cif.advanceshippingnotice.model

import com.hellofresh.cif.models.SkuUOM
import java.time.OffsetDateTime
import java.util.UUID

data class PoAsn(
    val id: String,
    val poId: UUID,
    val poNumber: String,
    val dc: String,
    val plannedDeliveryTime: OffsetDateTime,
    val supplierId: UUID,
    val skus: List<PoAsnSku>
)

data class PoAsnSku(
    val asnId: String,
    val skuId: UUID,
    val shippedQuantity: Long,
    val skuUOM: SkuUOM
)
