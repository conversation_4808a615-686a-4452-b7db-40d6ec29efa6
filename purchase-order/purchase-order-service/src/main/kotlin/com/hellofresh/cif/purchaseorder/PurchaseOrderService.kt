@file:Suppress("TooManyFunctions")

package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.featureflags.Context.SUPPLIER_ID
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.BlockedSupplier
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.purchaseorder.db.PurchaseOrderRepository
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoState.Approved
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoState.Deleted
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoState.Initiated
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoState.Rejected
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoState.Unspecified
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PurchaseOrderItem
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PurchaseOrderType
import com.hellofresh.cif.purchaseorder.model.Po
import com.hellofresh.cif.purchaseorder.model.PoSku
import com.hellofresh.cif.purchaseorder.model.PoStatus
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.sku.models.SkuSpecification
import java.math.BigDecimal
import java.time.Duration
import java.util.UUID
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.logging.log4j.kotlin.Logging

class PurchaseOrderService(
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val skuSpecificationService: SkuSpecificationService,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
) {

    suspend fun processRecords(records: ConsumerRecords<String, UpstreamPurchaseOrder>) {
        records.asSequence()
            .map { it.value() }
            .filter(::isValidPurchaseOrder)
            .forEach { record ->
                runCatching {
                    logger.info(record.revision.formatted)
                    processSingleRecord(record)
                }.onFailure { logger.error("Failed to process record:  ${record.revision.formatted}", it) }
            }
    }

    private fun isValidPurchaseOrder(purchaseOrder: UpstreamPurchaseOrder): Boolean {
        val isBlockedSupplier = isBlockedSupplierFeatureFlag(purchaseOrder.supplierId)
        val hasValidStates = purchaseOrder.validateRevisionState()
        return !isBlockedSupplier && hasValidStates
    }

    private fun isBlockedSupplierFeatureFlag(supplierId: String) =
        statsigFeatureFlagClient.isEnabledFor(
            BlockedSupplier(setOf(ContextData(SUPPLIER_ID, supplierId))),
        )

    private suspend fun processSingleRecord(purchaseOrder: UpstreamPurchaseOrder) {
        val po = mapPurchaseOrder(purchaseOrder, skuSpecificationService.specifications)
        with(po) {
            logger.info("PurchaseOrders record : $poRef , $dc , $expectedArrivalStartTime , $state")
            if (hasToBeDeleted(this)) {
                purchaseOrderRepository.delete(poNumber)
            } else {
                purchaseOrderRepository.upsert(this)
            }
        }
    }

    private fun hasToBeDeleted(po: Po): Boolean =
        po.skus.isEmpty() ||
            when (po.state) {
                Rejected, Deleted, Unspecified -> true
                Initiated, Approved -> false
            }

    companion object : Logging {
        private val updateTimeThreshold = Duration.ofSeconds(5)
        internal fun mapPurchaseOrder(
            purchaseOrder: UpstreamPurchaseOrder,
            skuSpecifications: Map<UUID, SkuSpecification>,
        ): Po = Po(
            purchaseOrder.revision.formatted,
            purchaseOrder.id?.let { UUID.fromString(it) },
            purchaseOrder.revision.number.formatted,
            purchaseOrder.distributionCenterCode,
            purchaseOrder.state, // PurchaseOrder.State
            mapStatus(purchaseOrder),
            purchaseOrder.expectedArrivalStartTime,
            purchaseOrder.expectedArrivalEndTime,
            UUID.fromString(purchaseOrder.supplierId),
            purchaseOrder.supplierName,
            purchaseOrder.orderItems
                .asSequence()
                .map(::mapPoSku)
                // Same order could contain multiple entries of same sku
                .groupingBy { it.id }
                .reduce { _, acc, sku ->
                    acc.copy(quantity = acc.quantity + sku.quantity)
                }
                .filter { (_, poSku) ->
                    isValidPoSku(
                        purchaseOrder.revision.formatted,
                        poSku,
                        skuSpecifications[poSku.id]?.uom
                    )
                }
                .values.toList(),
        )

        private fun isValidPoSku(poRef: String, poSku: PoSku, skuUOM: SkuUOM?) =
            if (poSku.quantity < 0) {
                logger.error("Skipping record with negative quantity, poRef: $poRef")
                false
            } else if (skuUOM != poSku.skuUOM) {
                logger.error(
                    "Skipping PO record with invalid uom [po UOM: ${poSku.skuUOM}, uom: $skuUOM], poRef: $poRef"
                )
                false
            } else {
                true
            }

        private fun mapPoSku(it: PurchaseOrderItem): PoSku {
            val quantity = BigDecimal(it.quantity.value)
            return PoSku(UUID.fromString(it.skuId), quantity.toLong(), it.packagingUnit)
        }

        fun mapStatus(purchaseOrder: UpstreamPurchaseOrder): PoStatus? =
            when (purchaseOrder.state) {
                Initiated -> {
                    val sendTime = mapSendTime(purchaseOrder)
                    if (sendTime == null || sendTime.plus(updateTimeThreshold) < purchaseOrder.updateTime
                    ) {
                        PoStatus.Planned
                    } else {
                        PoStatus.Sent
                    }
                }

                Approved -> PoStatus.Accepted
                else -> null
            }

        private fun mapSendTime(purchaseOrder: UpstreamPurchaseOrder) =
            with(purchaseOrder) {
                sendTime.also {
                    if (it == null) {
                        logger.warn(
                            "PurchaseOrder [Number: ${revision.formatted}, Id: $id] has invalid [SentTime: $sendTime]",
                        )
                    }
                }
            }

        private fun UpstreamPurchaseOrder.validateRevisionState() = when (revision.type) {
            PurchaseOrderType.PROVISIONAL -> false
            PurchaseOrderType.UNRECOGNIZED, PurchaseOrderType.UNSPECIFIED -> {
                logger.warn("Unspecified PurchaseOrder Revision Type received [Number: ${revision.formatted}, Id: $id]")
                false
            }

            else -> true
        }
    }
}
