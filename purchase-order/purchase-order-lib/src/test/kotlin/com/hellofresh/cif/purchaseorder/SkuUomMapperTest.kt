package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom.UOM_GAL
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom.UOM_KG
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom.UOM_OZ
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom.UOM_UNRECOGNIZED
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom.UOM_UNSPECIFIED
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class SkuUomMapperTest {

    @Test
    fun `Uom enum is correctly mapped to SkuUOM`() {
        val mapUnspecified = SkuUomMapper.mapUomToSkuUOM(UOM_UNSPECIFIED)
        assertEquals(SkuUOM.UOM_UNSPECIFIED, mapUnspecified)
        val mapUnit = SkuUomMapper.mapUomToSkuUOM(UOM_UNIT)
        assertEquals(SkuUOM.UOM_UNIT, mapUnit)
        val mapKg = SkuUomMapper.mapUomToSkuUOM(UOM_KG)
        assertEquals(SkuUOM.UOM_KG, mapKg)

        val mapLbs = SkuUomMapper.mapUomToSkuUOM(UOM_LBS)
        assertEquals(SkuUOM.UOM_LBS, mapLbs)
        val mapGal = SkuUomMapper.mapUomToSkuUOM(UOM_GAL)
        assertEquals(SkuUOM.UOM_GAL, mapGal)
        val mapLitre = SkuUomMapper.mapUomToSkuUOM(UOM_LITRE)
        assertEquals(SkuUOM.UOM_LITRE, mapLitre)
        val mapOz = SkuUomMapper.mapUomToSkuUOM(UOM_OZ)
        assertEquals(SkuUOM.UOM_OZ, mapOz)
        val mapUnrecognized = SkuUomMapper.mapUomToSkuUOM(UOM_UNRECOGNIZED)
        assertEquals(SkuUOM.UOM_UNRECOGNIZED, mapUnrecognized)
    }
}
