package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import java.time.LocalDate
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

internal class PurchaseOrderRepositoryImplTest : TestPrepare() {

    private val dcCode = "VE"
    private val skuId = UUID.randomUUID()
    private val daysInPast = 7L
    private val expectedArrivalStartTime = OffsetDateTime.now(UTC)
    private val expectedArrivalEndTime = expectedArrivalStartTime.plusHours(2)
    private val expectedArrivalDate = expectedArrivalStartTime.toLocalDate()
    private val dateRangeOneDayInPast = DateRange(expectedArrivalDate.minusDays(1), expectedArrivalDate)
    private val dateRangeDaysInPast = DateRange(expectedArrivalDate.minusDays(daysInPast), expectedArrivalDate)
    private val poNumber = "2305DH046273"
    private val poRef = "2305DH046273_01"
    private val poId = UUID.randomUUID()
    private val poQuantity = 12345L
    private val mlorDays = 10
    private val localTime: LocalTime = LocalTime.NOON
    private val defaultPurchaseOrder = PurchaseOrderRecord(
        poRef = poRef,
        poId = poId,
        poNumber = poNumber,
        dcCode = dcCode,
        expectedDateStartTime = expectedArrivalStartTime,
        expectedDateEndTime = expectedArrivalEndTime,
        supplierId = UUID.randomUUID(),
        supplierName = UUID.randomUUID().toString(),
        skuId = skuId,
        quantity = poQuantity,
    )

    @Test
    fun `should return a null expectedDate for PHF category inside IE market `() {
        val ieSkuId = UUID.randomUUID()
        val ieDcCode = "IE"
        val supplierId = UUID.randomUUID()

        persistSupplierSkus(supplierId, ieSkuId, mlorDays, "Active")
        persistDcConfig(dcCode = ieDcCode, market = "IE", zoneId = "Europe/Dublin")
        persistSkuSpecification(skuId = ieSkuId, category = "PHF", market = "IE")

        val grnClosed = createGrn(
            dcCode = ieDcCode,
            poRef = poRef,
            skuId = ieSkuId,
            deliveryInfoStatus = CLOSED,
        )
        persistGoodsReceivedNotes(setOf(grnClosed))
        persistPurchaseOrderAndRefreshPoInfoView(
            defaultPurchaseOrder.copy(
                supplierId = supplierId,
                dcCode = ieDcCode,
                skus = listOf(PurchaseOrderSkuRecord(ieSkuId, 500)),
            ),
        )

        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(setOf(ieDcCode), dateRangeOneDayInPast)
        }
        assertEquals(1, purchaseOrders.size)
        assertNull(purchaseOrders.first().purchaseOrderSkus.first().expiryDate)
        assertNull(purchaseOrders.first().purchaseOrderSkus.first().deliveries.first().expiryDate)
    }

    @Test
    fun `should return a grn expiry date when value exists instead of using mlor `() {
        val supplierId = UUID.randomUUID()

        persistSupplierSkus(supplierId, skuId, mlorDays, "Active")

        val grnClosed = createGrn(
            dcCode = dcCode,
            poRef = poRef,
            skuId = skuId,
            deliveryInfoStatus = CLOSED,
            expiryDate = LocalDate.now().plusMonths(1),
        )
        persistGoodsReceivedNotes(setOf(grnClosed))
        persistPurchaseOrderAndRefreshPoInfoView(
            defaultPurchaseOrder.copy(
                supplierId = supplierId,
                dcCode = dcCode,
                skus = listOf(PurchaseOrderSkuRecord(skuId, 500)),
            ),
        )

        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(setOf(dcCode), dateRangeOneDayInPast)
        }
        assertEquals(1, purchaseOrders.first().purchaseOrderSkus.size)
        assertEquals(1, purchaseOrders.first().purchaseOrderSkus.first().deliveries.size)
        assertEquals(skuId, purchaseOrders.first().purchaseOrderSkus.first().skuId)
        assertEquals(
            grnClosed.expiryDate,
            purchaseOrders.first().purchaseOrderSkus.first().deliveries.first().expiryDate
        )
    }

    @Test
    fun `Purchase Order Info repo returns a single purchase order info record for the given dc code`() {
        persistSupplierSkus(UUID.randomUUID(), skuId, mlorDays, "Active")
        val grnClosed = createGrn(
            dcCode = dcCode,
            poRef = poRef,
            skuId = skuId,
            deliveryInfoStatus = CLOSED,
        )
        persistPurchaseOrderAndRefreshPoInfoView(defaultPurchaseOrder)
        persistGoodsReceivedNotes(setOf(grnClosed))

        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(setOf(defaultPurchaseOrder.dcCode), dateRangeOneDayInPast)
        }

        assertEquals(1, purchaseOrders.size)
        purchaseOrders.first().assertPurchaseOrder(
            grnClosed,
            defaultPurchaseOrder,
            mlorDays,
        )
    }

    @Test
    fun `Purchase Order Info repo returns a single purchase order info record where there is no grn records`() {
        persistSupplierSkus(UUID.randomUUID(), skuId, mlorDays, "Active")
        persistPurchaseOrderAndRefreshPoInfoView(defaultPurchaseOrder)

        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(setOf(defaultPurchaseOrder.dcCode), dateRangeOneDayInPast)
        }
        assertEquals(1, purchaseOrders.size)
        purchaseOrders.first().assertPurchaseOrder(
            null,
            defaultPurchaseOrder,
            null,
        )
    }

    @Test
    fun `Purchase Order Info repo returns a single purchase order info where there is no purchase order info records`() {
        val grnClosed = createGrn(
            dcCode = dcCode,
            poRef = "2305DH046273_01",
            skuId = skuId,
            deliveryInfoStatus = CLOSED,
        )
        persistGoodsReceivedNotes(setOf(grnClosed))
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(setOf(grnClosed.dcCode), dateRangeOneDayInPast)
        }

        assertEquals(1, purchaseOrders.size)

        purchaseOrders.first().assertPurchaseOrder(grnClosed)
    }

    @Test
    fun `Purchase Order Info repo returns a single purchase order info using dc local date time`() {
        val canadaDcCode = "OE"
        val deliveryDateTime = OffsetDateTime.now()
        persistDcConfig(dcCode = canadaDcCode, market = "CA", zoneId = "Canada/Central")
        persistSupplierSkus(UUID.randomUUID(), skuId, mlorDays, "Active")
        val grnClosed = createGrn(
            dcCode = canadaDcCode,
            poRef = poRef,
            skuId = skuId,
            deliveryInfoStatus = CLOSED,
            deliveryDate = deliveryDateTime,
        )
        persistPurchaseOrderAndRefreshPoInfoView(
            defaultPurchaseOrder.copy(
                expectedDateStartTime = deliveryDateTime,
                expectedDateEndTime = deliveryDateTime.plusHours(2),
                dcCode = canadaDcCode,
            ),
        )
        persistGoodsReceivedNotes(setOf(grnClosed))
        val dateRangeOneDayInPast = DateRange(
            deliveryDateTime.toLocalDate().minusDays(1),
            deliveryDateTime.toLocalDate(),
        )
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(setOf(canadaDcCode), dateRangeOneDayInPast)
        }

        assertEquals(1, purchaseOrders.size)
        purchaseOrders.first().assertPurchaseOrder(
            grnClosed,
            defaultPurchaseOrder.copy(
                expectedDateStartTime = deliveryDateTime,
                expectedDateEndTime = deliveryDateTime.plusHours(2),
                dcCode = canadaDcCode,
            ),
            mlorDays,
        )
    }

    @Test
    fun `Purchase Order Info repo returns a single purchase order info where actual inbound was delivered 30 days prior to expected inbound`() {
        persistSupplierSkus(UUID.randomUUID(), skuId, mlorDays, "Active")

        val actualInboundDateBefore30Days = OffsetDateTime.of(
            expectedArrivalDate.minusDays(30),
            localTime,
            ZoneOffset.UTC,
        )
        val grnClosed = createGrn(
            dcCode = defaultPurchaseOrder.dcCode,
            poRef = defaultPurchaseOrder.poRef,
            skuId = defaultPurchaseOrder.skus.first().skuId,
            deliveryDate = actualInboundDateBefore30Days,
            deliveryInfoStatus = CLOSED,
        )
        persistPurchaseOrderAndRefreshPoInfoView(defaultPurchaseOrder)
        persistGoodsReceivedNotes(setOf(grnClosed))

        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(setOf(defaultPurchaseOrder.dcCode), dateRangeOneDayInPast)
        }
        assertEquals(1, purchaseOrders.size)
        purchaseOrders.first().assertPurchaseOrder(
            grnClosed,
            defaultPurchaseOrder,
            mlorDays,
        )
    }

    @Test
    fun `returns list of purchase orders for the given inbounds`() {
        persistSupplierSkus(UUID.randomUUID(), skuId, mlorDays, "Active")

        persistPurchaseOrderAndRefreshPoInfoView(defaultPurchaseOrder)
        val deliveryTime = OffsetDateTime.of(
            expectedArrivalDate.plusDays(1),
            localTime,
            ZoneOffset.UTC,
        )

        val grn = createGrn(
            dcCode,
            poNumber,
            poRef,
            skuId,
            UUID.randomUUID().toString(),
            deliveryTime,
            5,
            CLOSED,
        )
        persistGoodsReceivedNotes(setOf(grn))

        // when
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(setOf(dcCode), dateRangeDaysInPast)
        }

        // then
        assertEquals(1, purchaseOrders.size)
        purchaseOrders[0].assertPurchaseOrder(
            grn,
            defaultPurchaseOrder,
            mlorDays,
        )
    }

    @Test
    fun `PoService returns list of purchase orders for expected inbounds and no grn`() {
        persistSupplierSkus(UUID.randomUUID(), skuId, mlorDays, "Active")
        val nextDay = expectedArrivalStartTime.plusDays(1)

        val expectedPurchaseOrder2 = PurchaseOrderRecord(
            "2306CO092621_E3",
            UUID.randomUUID(), UUID.randomUUID().toString(),
            dcCode,
            nextDay,
            nextDay,
            UUID.randomUUID(),
            UUID.randomUUID().toString(),
            skuId,
            20,
        )

        persistPurchaseOrderAndRefreshPoInfoView(listOf(defaultPurchaseOrder, expectedPurchaseOrder2))

        // when
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(
                setOf(dcCode),
                DateRange(expectedArrivalDate, nextDay.toLocalDate()),
            )
        }

        // then
        assertEquals(2, purchaseOrders.size)
        purchaseOrders.first { it.poReference == defaultPurchaseOrder.poRef }
            .assertPurchaseOrder(
                null,
                defaultPurchaseOrder,
                null,
            )

        purchaseOrders.first { it.poReference == expectedPurchaseOrder2.poRef }
            .assertPurchaseOrder(
                null,
                expectedPurchaseOrder2,
                null,
            )
    }

    @Test
    fun `PoService returns list of purchase orders with no expected inbounds and with grn`() {
        val deliveryTime = OffsetDateTime.of(
            expectedArrivalDate.plusDays(1),
            localTime,
            ZoneOffset.UTC,
        )
        val grn1 = createGrn(
            dcCode,
            poNumber,
            poRef,
            skuId,
            UUID.randomUUID().toString(),
            deliveryTime,
            100,
            CLOSED,
        )

        val poNumber2 = "2306CO092621"
        val poRef2 = "2306CO092621_E3"
        val grn2 = createGrn(
            dcCode,
            poNumber2,
            poRef2,
            skuId,
            UUID.randomUUID().toString(),
            deliveryTime,
            200,
            CLOSED,
        )
        persistGoodsReceivedNotes(setOf(grn1, grn2))

        // when
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(
                setOf(dcCode),
                DateRange(expectedArrivalDate, deliveryTime.toLocalDate()),
            )
        }

        // then
        assertEquals(2, purchaseOrders.size)
        purchaseOrders.first { it.poReference == poRef }
            .assertPurchaseOrder(grn1)
        purchaseOrders.first { it.poReference == poRef2 }
            .assertPurchaseOrder(grn2)
    }

    @Test
    fun `PoService returns different purchase orders for given dc codes`() {
        persistSupplierSkus(UUID.randomUUID(), skuId, mlorDays, "Active")
        val expectedPurchaseOrder1 = defaultPurchaseOrder
        val nextDay = expectedArrivalStartTime.plusDays(1)
        val expectedPurchaseOrder2 = PurchaseOrderRecord(
            "2306CO092621_E3",
            UUID.randomUUID(), UUID.randomUUID().toString(),
            "DC2",
            nextDay,
            expectedArrivalStartTime,
            UUID.randomUUID(),
            UUID.randomUUID().toString(),
            skuId,
            20,
        )

        persistDcConfig(dcCode = expectedPurchaseOrder2.dcCode)
        persistPurchaseOrderAndRefreshPoInfoView(listOf(expectedPurchaseOrder1, expectedPurchaseOrder2))

        // when
        val purchaseOrders1 = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(
                setOf(expectedPurchaseOrder1.dcCode),
                DateRange(expectedArrivalDate, nextDay.toLocalDate()),
            )
        }
        val purchaseOrders2 = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(
                setOf(expectedPurchaseOrder2.dcCode),
                DateRange(expectedArrivalDate, nextDay.toLocalDate()),
            )
        }

        // then
        assertEquals(1, purchaseOrders1.size)
        assertEquals(1, purchaseOrders2.size)
        purchaseOrders1.first { it.poReference == expectedPurchaseOrder1.poRef }
            .assertPurchaseOrder(
                null,
                expectedPurchaseOrder1,
                null,
            )

        purchaseOrders2.first { it.poReference == expectedPurchaseOrder2.poRef }
            .assertPurchaseOrder(
                null,
                expectedPurchaseOrder2,
                null,
            )
    }

    @Test
    fun `returns POs by poNumbers`() {
        // given
        val posInCriteria = (1..5).map {
            defaultPurchaseOrder.copy(
                poNumber = "PHF-000$it",
                poRef = "PHF-000${it}_O1",
                poId = UUID.randomUUID(),
                supplierId = UUID.randomUUID(),
            )
        }
        val posNotInCriteria = (1..5).map {
            defaultPurchaseOrder.copy(
                poNumber = "PRO-000$it",
                poRef = "PHF-000${it}_O1",
                poId = UUID.randomUUID(),
                supplierId = UUID.randomUUID(),
            )
        }
        persistPurchaseOrderAndRefreshPoInfoView(posInCriteria + posNotInCriteria)

        // when
        val poRecords = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(posInCriteria.map { it.poNumber }.toSet())
        }
        val poIdsInDb = poRecords.map { it.poId }

        // then
        assertEquals(posInCriteria.map { it.poId }.toSet(), poIdsInDb.toSet())
    }

    @Test
    fun `should use supplier name from po table if exists`() {
        val supplierId = UUID.randomUUID()

        persistSupplierSkus(supplierId, skuId, mlorDays, "Active")

        val grnClosed = createGrn(
            dcCode = dcCode,
            poRef = poRef,
            skuId = skuId,
            deliveryInfoStatus = CLOSED,
            expiryDate = LocalDate.now().plusMonths(1),
        )
        persistGoodsReceivedNotes(setOf(grnClosed))
        persistPurchaseOrderAndRefreshPoInfoView(
            defaultPurchaseOrder.copy(
                supplierId = supplierId,
                dcCode = dcCode,
                skus = listOf(PurchaseOrderSkuRecord(skuId, 500)),
                poSupplierName = "PO SUPPLIER NAME",
            ),
        )

        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(setOf(dcCode), dateRangeOneDayInPast)
        }
        assertEquals("PO SUPPLIER NAME", purchaseOrders.first().supplier?.name)
    }
}
