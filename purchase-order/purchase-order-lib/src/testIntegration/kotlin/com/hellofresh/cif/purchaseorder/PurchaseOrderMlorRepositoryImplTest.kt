package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

internal class PurchaseOrderMlorRepositoryImplTest : TestPrepare() {
    private val dcCode = "VE"
    private val skuId = UUID.randomUUID()
    private val expectedArrivalStartTime = OffsetDateTime.now(UTC)
    private val expectedArrivalEndTime = expectedArrivalStartTime.plusHours(2)
    private val expectedArrivalDate = expectedArrivalStartTime.toLocalDate()
    private val dateRangeOneDayInPast = DateRange(expectedArrivalDate.minusDays(1), expectedArrivalDate)
    private val poRef = "2305DH046273_01"
    private val poId = UUID.randomUUID()
    private val poNumber = UUID.randomUUID().toString()
    private val poQuantity = 12345L
    private val mlorDays = 10
    private val defaultPurchaseOrder = PurchaseOrderRecord(
        poRef, poId, poNumber, dcCode, expectedArrivalStartTime,
        expectedArrivalEndTime,
        UUID.randomUUID(),
        UUID.randomUUID().toString(),
        skuId,
        poQuantity,
    )
    private val mlorDaysAtSupplierLevel = 5
    private val supplierName = "HF Agri Foods Inc"

    @Test
    fun `Purchase Order Info repo returns a single purchase order info with mlordays at the supplier level`() {
        val supplierSkuIdOne = UUID.randomUUID()
        val supplierSkuIdTwo = UUID.randomUUID()
        val supplierParentIdOne = UUID.randomUUID()
        val supplierParentIdTwo = UUID.randomUUID()
        val supplierIdOne = UUID.randomUUID()
        val supplierIdTwo = UUID.randomUUID()

        persistSupplierSkus(supplierSkuIdOne, skuId, mlorDays, "Active")
        persistSupplierSkus(supplierSkuIdTwo, skuId, mlorDaysAtSupplierLevel, "Active")

        persistSupplierCulinarySku(supplierSkuIdOne, skuId, supplierParentIdOne, "dach", "Active")
        persistSupplierCulinarySku(supplierSkuIdTwo, skuId, supplierParentIdTwo, "dach", "Active")

        persistSupplier(supplierIdOne, "HF Tech Foods Inc", supplierParentIdOne)
        persistSupplier(supplierIdTwo, supplierName, supplierParentIdTwo)

        val grnClosed = createGrn(
            dcCode = dcCode,
            poNumber = poNumber,
            poRef = poRef,
            skuId = skuId,
            deliveryInfoStatus = CLOSED,
        )
        persistPurchaseOrderAndRefreshPoInfoViewWithoutSupplier(
            listOf(
                defaultPurchaseOrder.copy(
                    supplierId = supplierIdTwo,
                    supplierName = supplierName
                )
            )
        )
        persistGoodsReceivedNotes(setOf(grnClosed))

        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(setOf(defaultPurchaseOrder.dcCode), dateRangeOneDayInPast)
        }

        assertEquals(1, purchaseOrders.size)
        purchaseOrders.first().assertPurchaseOrder(
            grnClosed,
            defaultPurchaseOrder,
            mlorDaysAtSupplierLevel,
            "HF Agri Foods Inc"
        )
    }

    @Test
    fun `Purchase Order Info repo returns a single purchase order info with least mlordays when po mlor is not matching`() {
        val supplierSkuIdOne = UUID.randomUUID()
        val supplierSkuIdTwo = UUID.randomUUID()
        val supplierParentIdOne = UUID.randomUUID()
        val supplierParentIdTwo = UUID.randomUUID()
        val supplierIdOne = UUID.randomUUID()
        val supplierIdTwo = UUID.randomUUID()

        persistSupplierSkus(supplierSkuIdOne, skuId, mlorDays, "Active")
        persistSupplierSkus(supplierSkuIdTwo, skuId, mlorDaysAtSupplierLevel, "Active")
        persistSupplierCulinarySku(UUID.randomUUID(), skuId, supplierParentIdOne, "dach", "Active")
        persistSupplierCulinarySku(UUID.randomUUID(), skuId, supplierParentIdTwo, "dach", "Active")

        persistSupplier(supplierIdOne, "HF Tech Foods Inc", supplierParentIdOne)
        persistSupplier(supplierIdTwo, supplierName, supplierParentIdTwo)

        val grnClosed = createGrn(
            dcCode = dcCode,
            poNumber = poNumber,
            poRef = poRef,
            skuId = skuId,
            deliveryInfoStatus = CLOSED,
        )
        persistPurchaseOrderAndRefreshPoInfoViewWithoutSupplier(
            listOf(
                defaultPurchaseOrder.copy(
                    supplierId = supplierIdTwo,
                    supplierName = supplierName
                )
            )
        )
        persistGoodsReceivedNotes(setOf(grnClosed))
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrders(setOf(defaultPurchaseOrder.dcCode), dateRangeOneDayInPast)
        }
        assertEquals(1, purchaseOrders.size)
        purchaseOrders.first().assertPurchaseOrder(
            grnClosed,
            defaultPurchaseOrder,
            mlorDaysAtSupplierLevel,
            "HF Agri Foods Inc"
        )
    }
}
