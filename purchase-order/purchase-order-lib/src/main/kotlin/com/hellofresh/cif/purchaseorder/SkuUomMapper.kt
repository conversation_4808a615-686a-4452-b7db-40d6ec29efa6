package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom

object SkuUomMapper {

    fun mapUomToSkuUOM(uom: Uom): SkuUOM =
        when (uom) {
            Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
            Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
            Uom.UOM_KG -> SkuUOM.UOM_KG
            Uom.UOM_LBS -> SkuUOM.UOM_LBS
            Uom.UOM_GAL -> SkuUOM.UOM_GAL
            Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
            Uom.UOM_OZ -> SkuUOM.UOM_OZ
            Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
        }
}
