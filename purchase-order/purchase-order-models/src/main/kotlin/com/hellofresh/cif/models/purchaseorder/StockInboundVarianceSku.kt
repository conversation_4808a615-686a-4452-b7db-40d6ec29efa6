package com.hellofresh.cif.models.purchaseorder

import com.hellofresh.cif.models.SkuQuantity
import java.util.UUID

data class StockInboundVarianceSku(
    val poRef: String,
    val asnId: String? = null,
    val supplierId: UUID? = null,
    val supplierName: String? = null,
    val skuId: UUID,
    val skuName: String,
    val skuCode: String,
    val skuCategory: String,
    val poVsInbound: SkuQuantity?,
    val asnVsInbound: SkuQuantity?,
)
