package com.hellofresh.cif.models.purchaseorder

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.OPEN
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PoStatus.PLANNED
import com.hellofresh.cif.models.purchaseorder.PoStatus.SENT
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

data class PurchaseOrder(
    val number: String,
    val poReference: String,
    val poId: UUID?,
    val dcCode: String,
    val expectedDeliveryTimeslot: TimeRange?,
    val supplier: Supplier?,
    val purchaseOrderSkus: List<PurchaseOrderSku>,
    val asns: List<Asn> = emptyList(),
    val poStatus: PoStatus
) {
    init {
        if (expectedDeliveryTimeslot == null && purchaseOrderSkus.isEmpty()) {
            throw IllegalArgumentException(
                "purchase order skus could not be empty, required at " +
                    "least 1 purchase order sku, poRef = $poReference, dcCode = $dcCode",
            )
        }
        if (asns.isNotEmpty()) {
            require(supplier != null) {
                "Supplier must be non-null when asns is not empty"
            }
        }
    }

    fun status(skuId: UUID) = run {
        val deliveryStatuses =
            purchaseOrderSkus.filter { it.skuId == skuId }.flatMap { it.deliveries.map { v -> v.state } }
        val isDeliveryStatusOpen =
            deliveryStatuses.isNotEmpty() && deliveryStatuses.any { it == OPEN }
        val isDeliveryStateClosed = deliveryStatuses.isNotEmpty() && deliveryStatuses.all { it == CLOSED }
        val isExpectedDeliveryTimeElapsed =
            expectedDeliveryTimeslot?.expectedDeliveryTime?.let {
                it.toLocalDate().isBefore(LocalDate.now(it.zone))
            } ?: false

        if (PLANNED == poStatus) {
            PurchaseOrderStatus.PLANNED
        } else if (isDeliveryStateClosed) {
            PurchaseOrderStatus.DELIVERED
        } else if (isExpectedDeliveryTimeElapsed) {
            PurchaseOrderStatus.OVERDUE
        } else if (isDeliveryStatusOpen) {
            PurchaseOrderStatus.DELIVERY_OPEN
        } else if (asns.isNotEmpty()) {
            PurchaseOrderStatus.ASN_RECEIVED
        } else {
            when (poStatus) {
                PLANNED -> PurchaseOrderStatus.PLANNED
                SENT -> PurchaseOrderStatus.SENT
                APPROVED -> PurchaseOrderStatus.APPROVED
            }
        }
    }
}

data class PurchaseOrderSku(
    val skuId: UUID,
    val expectedQuantity: SkuQuantity?,
    val deliveries: List<DeliveryInfo>,
    val expiryDate: LocalDate? = null
) {
    init {
        if (expectedQuantity == null && deliveries.isEmpty()) {
            throw IllegalArgumentException(
                "Delivery info cannot be null if the expected PO quantity is null, " +
                    "delivery info should exist, skuId = $skuId",
            )
        }
    }
}

data class PoVariance(
    val poReference: String,
    val poId: UUID?,
    val supplier: Supplier?,
    val poSkuVariances: List<PoSkuVariance>,
    val asns: List<Asn> = emptyList()
) {
    init {
        if (asns.isNotEmpty()) {
            require(supplier != null) {
                "Supplier must be non-null when asns is not empty"
            }
        }
    }
}

data class PoSkuVariance(
    val skuId: UUID,
    val skuName: String,
    val skuCode: String,
    val skuCategory: String,
    val expectedQuantity: SkuQuantity?,
    val deliveredQuantity: SkuQuantity?,
)

data class DeliveryInfo(
    val id: String,
    val deliveryTime: ZonedDateTime,
    val state: DeliveryInfoStatus,
    val quantity: SkuQuantity,
    val expiryDate: LocalDate? = null
) {
    companion object
}

enum class DeliveryInfoStatus {
    OPEN, CLOSED
}

data class Asn(
    val id: String,
    val skuId: UUID,
    val plannedDeliveryTime: ZonedDateTime,
    val shippedQuantity: SkuQuantity,
)

data class Supplier(
    val id: UUID,
    val name: String,
)

/**
 * Sent:
 * Approved:
 * ASN Received: The PO has an ASN.
 * Delivery Open: PO has OPEN state GRN.
 * Delivered: The PO has been inbounded with a CLOSED state GRN.
 * Overdue: No closed GRN and, it is past the delivery window.
 */
enum class PurchaseOrderStatus {
    PLANNED,
    SENT,
    APPROVED,
    ASN_RECEIVED,
    DELIVERY_OPEN,
    DELIVERED,
    OVERDUE
}

enum class PoStatus {
    PLANNED,
    SENT,
    APPROVED;

    fun isUsable() = this != PLANNED
}

data class TimeRange(
    val startTime: ZonedDateTime,
    val endTime: ZonedDateTime
) {
    val expectedDeliveryTime: ZonedDateTime
        get() = startTime
    val expectedDeliveryDate = startTime.toLocalDate()
}
