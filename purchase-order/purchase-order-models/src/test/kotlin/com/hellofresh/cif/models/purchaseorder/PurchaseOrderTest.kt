package com.hellofresh.cif.models.purchaseorder

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PoStatus.SENT
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.Test
import org.junit.jupiter.api.assertThrows

class PurchaseOrderTest {

    @Test
    fun `if PO has Asns then the supplier must not be null`() {
        assertThrows<IllegalArgumentException> {
            PurchaseOrder(
                "poN",
                "poR",
                null,
                "VE",
                null,
                supplier = null,
                emptyList(),
                listOf(Asn("ASN00", UUID.randomUUID(), ZonedDateTime.now(UTC), SkuQuantity.fromLong(0))),
                SENT
            )
        }
    }
}
