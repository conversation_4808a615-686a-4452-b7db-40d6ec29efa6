package com.hellofresh.cif.models.purchaseorder

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.OPEN
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PoStatus.SENT
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds.Companion.EXPECTED_LOCAL_TIME_LIMIT
import io.mockk.every
import io.mockk.mockkStatic
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

class PurchaseOrderInboundsTest {

    private val poNumber = "poNumber"
    private val poRef = "poRef"
    private val supplier = Supplier(UUID.randomUUID(), "test supplier")

    @Test
    fun `no inbound quantities are returned for po with different requested date`() {
        val expectedDate = ZonedDateTime.now()
        val purchaseOrder = getPurchaseOrder(
            expectedDate = expectedDate,
            deliveries = null,
        )
        val purchaseOrderSku = getPurchaseOrder(
            deliveries = null,
        ).purchaseOrderSkus.first()

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder))

        val purchaseOrderInfosEmpty = purchaseOrderInbounds.inbounds(
            purchaseOrderSku.skuId,
            purchaseOrder.dcCode,
            listOf(expectedDate.minusDays(1).toLocalDate()),
        )

        assertEquals(SkuQuantity.fromLong(0), purchaseOrderInfosEmpty.actualQuantity)
        assertEquals(0, purchaseOrderInfosEmpty.actualPoNumbers.size)
        assertEquals(SkuQuantity.fromLong(0), purchaseOrderInfosEmpty.remainingExpectedQuantity)
        assertEquals(SkuQuantity.fromLong(0), purchaseOrderInfosEmpty.poQuantity)
        assertEquals(0, purchaseOrderInfosEmpty.expectedPoNumbers.size)
    }

    @Test
    fun `expected inbound quantities are returned for po without deliveries`() {
        val expectedDate = ZonedDateTime.now()
        val purchaseOrder = getPurchaseOrder(
            expectedDate = expectedDate,
            deliveries = null,
        )
        val purchaseOrderSku = purchaseOrder.purchaseOrderSkus.first()

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder))
        val inbounds =
            purchaseOrderInbounds.inbounds(
                purchaseOrderSku.skuId,
                purchaseOrder.dcCode,
                listOf(expectedDate.toLocalDate()),
            )

        assertEquals(SkuQuantity.fromLong(0), inbounds.actualQuantity)
        assertEquals(0, inbounds.actualPoNumbers.size)
        assertEquals(purchaseOrderSku.expectedQuantity, inbounds.remainingExpectedQuantity)
        assertEquals(purchaseOrderSku.expectedQuantity, inbounds.poQuantity)
        assertEquals(setOf(purchaseOrder.number), inbounds.expectedPoNumbers)
    }

    @Test
    fun `remaining expected and actual inbound quantities are returned for po with open deliveries`() {
        val expectedDate = ZonedDateTime.now()
        val expectedQuantity = SkuQuantity.fromLong(100L)
        val deliveredQuantity1 = SkuQuantity.fromLong(60L)
        val purchaseOrder = getPurchaseOrder(
            expectedDate = expectedDate,
            expectedQuantity = expectedQuantity,
            deliveries = listOf(
                DeliveryInfo(UUID.randomUUID().toString(), expectedDate, OPEN, deliveredQuantity1),
            ),
        )

        val purchaseOrderSku = purchaseOrder.purchaseOrderSkus.first()

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder))
        val inbounds =
            purchaseOrderInbounds.inbounds(
                purchaseOrderSku.skuId,
                purchaseOrder.dcCode,
                listOf(expectedDate.toLocalDate()),
            )

        assertEquals(deliveredQuantity1, inbounds.actualQuantity)
        assertEquals(setOf(purchaseOrder.number), inbounds.actualPoNumbers)
        assertEquals(expectedQuantity.minus(deliveredQuantity1), inbounds.remainingExpectedQuantity) // TODO Test check
        assertEquals(setOf(purchaseOrder.number), inbounds.expectedPoNumbers)
        assertEquals(purchaseOrderSku.expectedQuantity, inbounds.poQuantity)
    }

    @Test
    fun `remaining expected quantity are returned for po with open deliveries before expected date`() {
        val expectedDate = ZonedDateTime.now()
        val expectedQuantity = SkuQuantity.fromLong(100L)
        val deliveredQuantity = SkuQuantity.fromLong(60L)
        val deliveryDate = expectedDate.minusDays(2)
        val purchaseOrder = getPurchaseOrder(
            expectedDate = expectedDate,
            expectedQuantity = expectedQuantity,
            deliveries = listOf(
                DeliveryInfo(UUID.randomUUID().toString(), deliveryDate, OPEN, deliveredQuantity),
            ),
        )

        val purchaseOrderSku = purchaseOrder.purchaseOrderSkus.first()

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder))
        val actualDeliveredInbounds =
            purchaseOrderInbounds.inbounds(
                purchaseOrderSku.skuId,
                purchaseOrder.dcCode,
                listOf(deliveryDate.toLocalDate()),
            )

        assertEquals(deliveredQuantity, actualDeliveredInbounds.actualQuantity)
        assertEquals(setOf(purchaseOrder.number), actualDeliveredInbounds.actualPoNumbers)
        assertEquals(SkuQuantity.fromLong(0), actualDeliveredInbounds.remainingExpectedQuantity)
        assertEquals(emptySet(), actualDeliveredInbounds.expectedPoNumbers)
        assertEquals(SkuQuantity.fromLong(0), actualDeliveredInbounds.poQuantity)

        val expectedDeliveredInbounds =
            purchaseOrderInbounds.inbounds(
                purchaseOrderSku.skuId,
                purchaseOrder.dcCode,
                listOf(expectedDate.toLocalDate()),
            )

        assertEquals(SkuQuantity.fromLong(0), expectedDeliveredInbounds.actualQuantity)
        assertEquals(emptySet(), expectedDeliveredInbounds.actualPoNumbers)
        assertEquals(expectedQuantity.minus(deliveredQuantity), expectedDeliveredInbounds.remainingExpectedQuantity)
        assertEquals(setOf(purchaseOrder.number), expectedDeliveredInbounds.expectedPoNumbers)
        assertEquals(purchaseOrderSku.expectedQuantity, expectedDeliveredInbounds.poQuantity)
    }

    @Test
    fun `no remaining expected and actual inbound quantities are returned for po with closed delivery`() {
        val expectedDate = ZonedDateTime.now()
        val expectedQuantity = SkuQuantity.fromLong(100L)
        val deliveredQuantity = SkuQuantity.fromLong(60L)
        val purchaseOrder = getPurchaseOrder(
            expectedDate = expectedDate,
            expectedQuantity = expectedQuantity,
            deliveries = listOf(
                DeliveryInfo(UUID.randomUUID().toString(), expectedDate, CLOSED, deliveredQuantity),
            ),
        )
        val purchaseOrderSku = purchaseOrder.purchaseOrderSkus.first()

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder))
        val inbounds =
            purchaseOrderInbounds.inbounds(
                purchaseOrderSku.skuId,
                purchaseOrder.dcCode,
                listOf(expectedDate.toLocalDate()),
            )

        assertEquals(deliveredQuantity, inbounds.actualQuantity)
        assertEquals(setOf(purchaseOrder.number), inbounds.actualPoNumbers)
        assertEquals(SkuQuantity.fromLong(0), inbounds.remainingExpectedQuantity)
        assertEquals(setOf(purchaseOrder.number), inbounds.expectedPoNumbers)
        assertEquals(purchaseOrderSku.expectedQuantity, inbounds.poQuantity)
    }

    @Test
    fun `no remaining expected and actual inbound quantities are returned for po with closed delivery and quantity bigger than expected`() {
        val expectedDate = ZonedDateTime.now()
        val expectedQuantity = SkuQuantity.fromLong(100L)
        val deliveredQuantity = SkuQuantity.fromLong(150L)
        val purchaseOrder = getPurchaseOrder(
            expectedDate = expectedDate,
            expectedQuantity = expectedQuantity,
            deliveries = listOf(DeliveryInfo(UUID.randomUUID().toString(), expectedDate, CLOSED, deliveredQuantity)),
        )
        val purchaseOrderSku = purchaseOrder.purchaseOrderSkus.first()

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder))
        val inbounds =
            purchaseOrderInbounds.inbounds(
                purchaseOrderSku.skuId,
                purchaseOrder.dcCode,
                listOf(expectedDate.toLocalDate()),
            )

        assertEquals(purchaseOrderSku.deliveries.first().quantity, inbounds.actualQuantity)
        assertEquals(setOf(purchaseOrder.number), inbounds.actualPoNumbers)
        assertEquals(SkuQuantity.fromLong(0), inbounds.remainingExpectedQuantity)
        assertEquals(setOf(purchaseOrder.number), inbounds.expectedPoNumbers)
        assertEquals(purchaseOrderSku.expectedQuantity, inbounds.poQuantity)
    }

    @Test
    fun `actual inbound quantities are returned for po with closed delivery and without expected inbounds`() {
        val expectedDate = ZonedDateTime.now()
        val quantity = SkuQuantity.fromLong(123L)
        val purchaseOrder = getPurchaseOrder(
            expectedDate = null,
            expectedQuantity = null,
            deliveries = listOf(DeliveryInfo(UUID.randomUUID().toString(), expectedDate, CLOSED, quantity)),
        )
        val purchaseOrderSku = purchaseOrder.purchaseOrderSkus.first()

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder))
        val inbounds =
            purchaseOrderInbounds.inbounds(
                purchaseOrderSku.skuId,
                purchaseOrder.dcCode,
                listOf(expectedDate.toLocalDate()),
            )

        assertEquals(quantity, inbounds.actualQuantity)
        assertEquals(setOf(poNumber), inbounds.actualPoNumbers)
        assertEquals(SkuQuantity.fromLong(0), inbounds.remainingExpectedQuantity)
        assertEquals(SkuQuantity.fromLong(0), inbounds.poQuantity)
        assertEquals(0, inbounds.expectedPoNumbers.size)
    }

    @Test
    fun `no actual inbound quantities are returned for po without expected and with open actual inbounds`() {
        val expectedDate = ZonedDateTime.now()
        val purchaseOrder = getPurchaseOrder(
            expectedDate = null,
            expectedQuantity = null,
            deliveries = listOf(
                DeliveryInfo(UUID.randomUUID().toString(), expectedDate, OPEN, SkuQuantity.fromLong(1000)),
            ),
        )
        val purchaseOrderSku = purchaseOrder.purchaseOrderSkus.first()

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder))
        val inbounds =
            purchaseOrderInbounds.inbounds(
                purchaseOrderSku.skuId,
                purchaseOrder.dcCode,
                listOf(expectedDate.toLocalDate()),
            )

        assertEquals(purchaseOrderSku.deliveries.first().quantity, inbounds.actualQuantity)
        assertEquals(setOf(poNumber), inbounds.actualPoNumbers)
        assertEquals(SkuQuantity.fromLong(0), inbounds.remainingExpectedQuantity)
        assertEquals(SkuQuantity.fromLong(0), inbounds.poQuantity)
        assertEquals(0, inbounds.expectedPoNumbers.size)
    }

    @Test
    fun `delivery inbound quantities are returned for po delivered the day before`() {
        val expectedDate = ZonedDateTime.now()
        val purchaseOrder = getPurchaseOrder(
            expectedDate = expectedDate,
            deliveries = listOf(
                DeliveryInfo(
                    UUID.randomUUID().toString(),
                    expectedDate.minusDays(1),
                    CLOSED,
                    SkuQuantity.fromLong(1000),
                ),
            ),
        )
        val purchaseOrderSku = purchaseOrder.purchaseOrderSkus.first()

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder))

        val inbounds = purchaseOrderInbounds.inbounds(
            purchaseOrderSku.skuId,
            purchaseOrder.dcCode,
            listOf(purchaseOrderSku.deliveries.first().deliveryTime.toLocalDate()),
        )

        assertEquals(purchaseOrderSku.deliveries.first().quantity, inbounds.actualQuantity)
        assertEquals(setOf(poNumber), inbounds.actualPoNumbers)
        assertEquals(SkuQuantity.fromLong(0), inbounds.remainingExpectedQuantity)
        assertEquals(SkuQuantity.fromLong(0), inbounds.poQuantity)
        assertEquals(0, inbounds.expectedPoNumbers.size)
    }

    @Test
    fun `expected inbound quantities are not returned for po with closed delivery the day before`() {
        val expectedDate = ZonedDateTime.now()
        val purchaseOrder = getPurchaseOrder(
            expectedDate = expectedDate,
            deliveries = listOf(
                DeliveryInfo(
                    UUID.randomUUID().toString(),
                    expectedDate.minusDays(1),
                    CLOSED,
                    SkuQuantity.fromLong(1000),
                ),
            ),
        )
        val purchaseOrderSku = purchaseOrder.purchaseOrderSkus.first()

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder))

        val inbounds =
            purchaseOrderInbounds.inbounds(
                purchaseOrderSku.skuId,
                purchaseOrder.dcCode,
                listOf(expectedDate.toLocalDate()),
            )

        assertEquals(SkuQuantity.fromLong(0), inbounds.actualQuantity)
        assertEquals(0, inbounds.actualPoNumbers.size)
        assertEquals(SkuQuantity.fromLong(0), inbounds.remainingExpectedQuantity)
        assertEquals(purchaseOrderSku.expectedQuantity, inbounds.poQuantity)
        assertEquals(setOf(poNumber), inbounds.expectedPoNumbers)
    }

    @Test
    fun `all pos expected inbound quantities are aggregated`() {
        val dcCode = "dcCode"
        val skuId = UUID.randomUUID()
        val expectedDeliveryTimeslot = TimeRange(ZonedDateTime.now(), ZonedDateTime.now().plusMinutes(1))
        val quantity1 = SkuQuantity.fromLong(123L)
        val quantity2 = SkuQuantity.fromLong(1232323L)
        val poNumber2 = "poNumber2"
        val poRef2 = "poRef2"
        val purchaseOrder1 = PurchaseOrder(
            poNumber,
            poRef,
            UUID.randomUUID(),
            dcCode,
            expectedDeliveryTimeslot,
            supplier,
            listOf(
                PurchaseOrderSku(
                    skuId,
                    quantity1,
                    emptyList(),
                ),
            ),
            poStatus = SENT,
        )
        val purchaseOrder2 = PurchaseOrder(
            poNumber2,
            poRef2,
            UUID.randomUUID(),
            dcCode,
            expectedDeliveryTimeslot,
            supplier,
            listOf(
                PurchaseOrderSku(
                    skuId,
                    quantity2,
                    emptyList(),
                ),
            ),
            poStatus = SENT,
        )

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder1, purchaseOrder2))

        val inbounds = purchaseOrderInbounds.inbounds(
            skuId,
            dcCode,
            listOf(expectedDeliveryTimeslot.expectedDeliveryDate),
        )
        assertEquals(SkuQuantity.fromLong(0L), inbounds.actualQuantity)
        assertEquals(0, inbounds.actualPoNumbers.size)
        assertEquals(quantity1.plus(quantity2), inbounds.remainingExpectedQuantity)
        assertEquals(quantity1.plus(quantity2), inbounds.poQuantity)
        assertEquals(setOf(poNumber, poNumber2), inbounds.expectedPoNumbers)
    }

    @Test
    fun `all pos actual inbound quantities are aggregated`() {
        val deliveryDate = ZonedDateTime.now()

        val poNumber2 = "po2"
        val poRef2 = "poRef2"
        val purchaseOrder1 = getPurchaseOrder(
            expectedDate = null,
            expectedQuantity = null,
            deliveries = listOf(
                DeliveryInfo(
                    UUID.randomUUID().toString(),
                    deliveryDate,
                    CLOSED,
                    SkuQuantity.fromLong(1000),
                ),
            ),
        )

        val purchaseOrder2 = getPurchaseOrder(
            poNumber = poNumber2,
            poRef = poRef2,
            expectedDate = null,
            expectedQuantity = null,
            skuId = purchaseOrder1.purchaseOrderSkus.first().skuId,
            deliveries = listOf(
                DeliveryInfo(
                    UUID.randomUUID().toString(),
                    purchaseOrder1.purchaseOrderSkus.first().deliveries.first().deliveryTime,
                    CLOSED,
                    SkuQuantity.fromLong(5000),
                ),
            ),
        )

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder1, purchaseOrder2))

        val inbounds = purchaseOrderInbounds.inbounds(
            purchaseOrder1.purchaseOrderSkus.first().skuId,
            purchaseOrder1.dcCode,
            listOf(deliveryDate.toLocalDate()),
        )

        val totalActualQuantity = purchaseOrder1.purchaseOrderSkus.first().deliveries.first().quantity +
            purchaseOrder2.purchaseOrderSkus.first().deliveries.first().quantity
        assertEquals(totalActualQuantity, inbounds.actualQuantity)
        assertEquals(setOf(poNumber, poNumber2), inbounds.actualPoNumbers)
        assertEquals(SkuQuantity.fromLong(0L), inbounds.remainingExpectedQuantity)
        assertEquals(0, inbounds.expectedPoNumbers.size)
        assertEquals(SkuQuantity.fromLong(0), inbounds.poQuantity)
    }

    @Test
    fun `all pos actual inbound and expected inbound quantities are aggregated`() {
        val dcCode = "dcCode"
        val skuId = UUID.randomUUID()
        val expectedDeliveryTimeslot = TimeRange(ZonedDateTime.now(), ZonedDateTime.now().plusMinutes(1))
        val quantity = SkuQuantity.fromLong(123L)
        val deliveredQuantity = SkuQuantity.fromLong(1232323L)
        val poNumber2 = "poNumber2"
        val poRef2 = "poRef2"
        val purchaseOrder1 = PurchaseOrder(
            poNumber,
            poRef,
            UUID.randomUUID(),
            dcCode,
            expectedDeliveryTimeslot,
            supplier,
            listOf(
                PurchaseOrderSku(
                    skuId,
                    quantity,
                    emptyList(),
                ),
            ),
            poStatus = APPROVED,
        )
        val purchaseOrder2 = PurchaseOrder(
            poNumber2,
            poRef2,
            UUID.randomUUID(),
            dcCode,
            expectedDeliveryTimeslot,
            supplier,
            listOf(
                PurchaseOrderSku(
                    skuId,
                    quantity,
                    listOf(
                        DeliveryInfo(
                            UUID.randomUUID().toString(),
                            expectedDeliveryTimeslot.startTime,
                            CLOSED,
                            deliveredQuantity,
                        ),
                    ),
                ),
            ),
            poStatus = APPROVED,
        )

        val purchaseOrderInbounds = PurchaseOrderInbounds(listOf(purchaseOrder1, purchaseOrder2))

        val inbounds = purchaseOrderInbounds.inbounds(
            skuId,
            dcCode,
            listOf(expectedDeliveryTimeslot.expectedDeliveryDate),
        )
        assertEquals(deliveredQuantity, inbounds.actualQuantity)
        assertEquals(setOf(poNumber2), inbounds.actualPoNumbers)
        assertEquals(quantity, inbounds.remainingExpectedQuantity)
        assertEquals(quantity.times(SkuQuantity.fromLong(2)).getValue(), inbounds.poQuantity.getValue())
        assertEquals(setOf(poNumber, poNumber2), inbounds.expectedPoNumbers)
    }

    @ParameterizedTest
    @MethodSource("poExpectedTimes")
    fun `verify is Purchase order delivery time is before 23,30 for different timezones`(
        startTime: ZonedDateTime?,
        nowUTC: LocalDateTime,
        expectedResult: Boolean
    ) {
        val expectedQuantity = SkuQuantity.fromLong(100L)
        val purchaseOrder = getPurchaseOrder(
            expectedDate = startTime,
            expectedQuantity = expectedQuantity,
            deliveries = emptyList(),
        )

        startTime?.also {
            mockkStatic(LocalDateTime::class)
            every { LocalDateTime.now(startTime.zone) } returns nowUTC
        }

        val inbounds = PurchaseOrderInbounds(listOf(purchaseOrder))
            .inbounds(
                purchaseOrder.purchaseOrderSkus.first().skuId,
                purchaseOrder.dcCode,
                listOf(startTime?.toLocalDate() ?: nowUTC.toLocalDate()),
            )

        if (expectedResult) {
            assertEquals(
                purchaseOrder.purchaseOrderSkus.first().expectedQuantity,
                inbounds.remainingExpectedQuantity,
            )
        } else {
            assertEquals(
                BigDecimal.ZERO,
                inbounds.remainingExpectedQuantity.getValue(),
            )
        }
    }

    @ParameterizedTest
    @EnumSource(PoStatus::class)
    fun `purchase order inbounds excludes unusable purchase orders`(poStatus: PoStatus) {
        val expectedQuantity = SkuQuantity.fromLong(100L)
        val now = ZonedDateTime.now(ZoneOffset.UTC)
        val purchaseOrder = getPurchaseOrder(
            expectedDate = now,
            expectedQuantity = expectedQuantity,
            deliveries = emptyList(),
            poStatus = poStatus
        )

        val inbounds = PurchaseOrderInbounds(listOf(purchaseOrder))
            .inbounds(
                purchaseOrder.purchaseOrderSkus.first().skuId,
                purchaseOrder.dcCode,
                now.toLocalDate(),
            )

        if (poStatus.isUsable()) {
            assertEquals(1, inbounds.pos.size)
            assertEquals(expectedQuantity, inbounds.poQuantity)
        } else {
            assertTrue(inbounds.pos.isEmpty())
            assertTrue(inbounds.poQuantity.isZero())
        }
    }

    @SuppressWarnings("LongParameterList")
    private fun getPurchaseOrder(
        poNumber: String = "poNumber",
        poRef: String = "poRef",
        skuId: UUID = UUID.randomUUID(),
        expectedDate: ZonedDateTime? = ZonedDateTime.now(),
        expectedQuantity: SkuQuantity? = SkuQuantity.fromLong(1000),
        deliveries: List<DeliveryInfo>?,
        purchaseOrderSkus: List<PurchaseOrderSku>? = null,
        poStatus: PoStatus = SENT
    ) =
        PurchaseOrder(
            poNumber,
            poRef,
            UUID.randomUUID(),
            "VE",
            expectedDate?.let { TimeRange(expectedDate, expectedDate.plusMinutes(1)) },
            supplier,
            purchaseOrderSkus ?: listOf(
                PurchaseOrderSku(
                    skuId,
                    expectedQuantity,
                    deliveries ?: emptyList(),
                ),
            ),
            poStatus = poStatus,
        )

    companion object {
        @JvmStatic
        fun poExpectedTimes() = listOf(
            // Case 1: Time within the limit (before 11:30 PM)
            arrayOf(
                ZonedDateTime.of(
                    LocalDateTime.of(2024, 9, 21, 8, 0),
                    ZoneId.of("Australia/Melbourne"),
                ),
                LocalDate.of(2024, 9, 20).atTime(EXPECTED_LOCAL_TIME_LIMIT.minusMinutes(1)),
                true,
            ),
            // Case 2: Time beyond the limit (after 11:30 PM)
            arrayOf(
                ZonedDateTime.of(
                    LocalDateTime.of(2024, 9, 20, 8, 0),
                    ZoneId.of("Australia/Melbourne"),
                ),
                LocalDate.of(2024, 9, 20).atTime(EXPECTED_LOCAL_TIME_LIMIT.plusMinutes(1)),
                false,
            ),
            // Case 3: no expected po Time
            arrayOf(
                null,
                LocalDateTime.of(2024, 9, 20, 12, 0, 0),
                false,
            ),
        )
    }
}
