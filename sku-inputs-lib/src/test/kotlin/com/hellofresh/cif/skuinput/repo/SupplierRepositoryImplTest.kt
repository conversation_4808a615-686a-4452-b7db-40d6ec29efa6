package com.hellofresh.cif.skuinput.repo

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.sku_inputs_lib.schema.Tables
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierCulinarySkuRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierSkuPricingRecord
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierSkuRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class SupplierRepositoryImplTest {
    private val skuId = UUID.randomUUID()
    private val supplierSkuId = UUID.randomUUID()
    private val expectedSupplierId = UUID.randomUUID()
    private val supplierParentId = UUID.randomUUID()
    private val culinarySkuId = UUID.randomUUID()
    private val supplierRecord = SupplierRecord(
        expectedSupplierId,
        supplierParentId,
        "Name 1"
    )
    private val supplierCulinarySkuRecord = SupplierCulinarySkuRecord(
        supplierSkuId,
        supplierParentId,
        culinarySkuId,
        "dach",
        "active",
        OffsetDateTime.now(),
        OffsetDateTime.now()
    )

    private val supplierSkuRecord = SupplierSkuRecord(
        supplierSkuId,
        skuId,
        10,
        OffsetDateTime.now(),
        OffsetDateTime.now(),
        "active"
    )

    private val supplierSkuPricingRecord = SupplierSkuPricingRecord(
        UUID.randomUUID(), supplierSkuId, 10, true, "dach", OffsetDateTime.now(), OffsetDateTime.now(), LocalDate.now(),
        LocalDate.now().plusMonths(10)
    )

    @BeforeEach
    fun initData() {
        dsl.deleteFrom(Tables.SUPPLIER).execute()
        dsl.deleteFrom(Tables.SUPPLIER_SKU).execute()
        dsl.deleteFrom(Tables.SUPPLIER_SKU_PRICING).execute()
        dsl.deleteFrom(Tables.SUPPLIER_CULINARY_SKU).execute()
    }

    @Test
    fun `returns requested supplier with lead time for the given sku id`() {
        val supplierSkuPricingRecordOne = SupplierSkuPricingRecord(
            UUID.randomUUID(), supplierSkuId, 10, true, "dach", OffsetDateTime.now(),
            OffsetDateTime.now(), LocalDate.now(),
            LocalDate.now().plusMonths(10),
        )
        dsl.batchInsert(
            supplierRecord,
            supplierCulinarySkuRecord,
            supplierSkuRecord,
            supplierSkuPricingRecord,
            supplierSkuPricingRecordOne,
        ).execute()
        assertSupplierDetails()
    }

    @Test
    fun `returns supplier with lead time for the given sku id without duplicate supplier details`() {
        val supplierSkuPricingRecordOne = SupplierSkuPricingRecord(
            UUID.randomUUID(), supplierSkuId, 10, true, "dach", OffsetDateTime.now(),
            OffsetDateTime.now(), LocalDate.now(),
            LocalDate.now().plusMonths(10),
        )
        val supplierSkuPricingRecordTwo = SupplierSkuPricingRecord(
            UUID.randomUUID(), supplierSkuId, 10, true, "dach", OffsetDateTime.now(),
            OffsetDateTime.now(), LocalDate.now(),
            LocalDate.now().plusMonths(10),
        )
        dsl.batchInsert(
            supplierRecord,
            supplierCulinarySkuRecord,
            supplierSkuRecord,
            supplierSkuPricingRecord,
            supplierSkuPricingRecordOne,
            supplierSkuPricingRecordTwo
        ).execute()
        assertSupplierDetails()
    }

    @Test
    fun `returns supplier details with empty lead times`() {
        dsl.batchInsert(
            supplierRecord,
            supplierCulinarySkuRecord,
            supplierSkuRecord
        ).execute()
        runBlocking {
            val supplierWithLeadTimes = supplierRepository.fetchSuppliersBySkuId(
                skuId
            )
            assertEquals(1, supplierWithLeadTimes.size)
            assertEquals(0, supplierWithLeadTimes[skuId]?.first()?.leadTimes?.size)
        }
    }
    private fun assertSupplierDetails() {
        runBlocking {
            val supplierWithLeadTimes = supplierRepository.fetchSuppliersBySkuId(
                skuId
            )[skuId] ?: emptyList()
            assertEquals(1, supplierWithLeadTimes.size)
            val supplierWithLeadTime = supplierWithLeadTimes.first()
            supplierWithLeadTime.apply {
                assertEquals(expectedSupplierId, supplierId)
                assertEquals("Name 1", supplierName)
                assertEquals(10, leadTimes.first().leadTime)
            }
        }
    }
    companion object {
        lateinit var dsl: MetricsDSLContext
        lateinit var supplierRepository: SupplierRepositoryImpl
        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            supplierRepository = SupplierRepositoryImpl(dsl)
        }
    }
}
