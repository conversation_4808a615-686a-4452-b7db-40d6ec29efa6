---
name: ci
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

on: pull_request

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: [ self-hosted, default ]
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
        with:
            fetch-depth: 0

      - name: PR Title Check
        id: pr-title-check
        uses: ./.github/actions/pr-title-check

      - name: Import Vault Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;

      - name: Checkout jetstream-ci-scripts
        uses: actions/checkout@v4
        with:
          repository: hellofresh/jetstream-ci-scripts
          path: jetstream-ci-scripts
          token: ${{ env.GITHUB_TOKEN }}

      - name: Run linting
        uses: ./jetstream-ci-scripts/actions/super-linter
        with:
          base-branch: master
          token: ${{ env.GITHUB_TOKEN }}
          filter-regex-exclude: '.*/custom-templates/.*\.ya?ml|./gradle*'

  check:
    name: Check
    runs-on: [ self-hosted, heavy ]
    timeout-minutes: 20
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Import Vault Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;
            common/data/defaults SONAR_TOKEN | SONAR_TOKEN ;

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: eu-west-1
          role-to-assume: "arn:aws:iam::489198589229:role/github-actions-runner"

      - name: Set short commit ID
        run: echo "SHORT_COMMIT_ID=$(git rev-parse --short=12 "$GITHUB_SHA")" >> "$GITHUB_ENV"

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v3
        with:
          build-scan-publish: true
          cache-read-only: false

      - name: Check Gradle
        shell: bash
        run: |
          gradle check jacocoAggregatedReport sonar \
            "-Dsonar.login=$SONAR_TOKEN" \
            "-Dsonar.pullrequest.branch=$PR_HEADREF" \
            "-Dsonar.pullrequest.key=$PR_NUMBER" \
            "-Dsonar.pullrequest.base=$PR_BASEREF"
        env:
          ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
          ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}
          SONAR_TOKEN: ${{ env.SONAR_TOKEN }}
          PR_HEADREF: ${{ github.event.pull_request.head.ref }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
          PR_BASEREF: ${{ github.event.pull_request.base.ref }}

  e2e:
    name: End-to-end
    runs-on: [ self-hosted, heavy ]
    timeout-minutes: 15
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Import Vault Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          secrets: |
            live/key-value/data/kafka  KAFKA_PASSWORD |  HF_AIVEN_PASSWORD ;
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
            aws-region: eu-west-1
            role-to-assume: "arn:aws:iam::489198589229:role/github-actions-runner"

      - name: Set short commit ID
        run: echo "SHORT_COMMIT_ID=$(git rev-parse --short=12 "$GITHUB_SHA")" >> "$GITHUB_ENV"
      - run: setup-java

      - name: end-to-end-up
        run: env=local make set-docker-env e2e-apps-up
        env:
            HF_KAFKA_PASSWORD_LIVE: ${{ env.HF_AIVEN_PASSWORD }}
            HF_AIVEN_PASSWORD: ${{ env.HF_AIVEN_PASSWORD }}
            HF_PROFILES: 'e2e'
            HF_TIER: 'local'
            DOCKER_TAG: ${{env.SHORT_COMMIT_ID}}
            DOCKER_REGISTRY: '489198589229.dkr.ecr.eu-west-1.amazonaws.com/'

      - name: docker-ps
        run: env=local docker ps
        env:
            HF_KAFKA_PASSWORD_LIVE: ${{ env.HF_AIVEN_PASSWORD }}
            HF_AIVEN_PASSWORD: ${{ env.HF_AIVEN_PASSWORD }}
            HF_PROFILES: 'e2e'
            HF_TIER: 'local'
            DOCKER_TAG: ${{env.SHORT_COMMIT_ID}}
            DOCKER_REGISTRY: '489198589229.dkr.ecr.eu-west-1.amazonaws.com/'

      - name: end-to-end-run
        run: env=local make run-e2e-test
        env:
            HF_KAFKA_PASSWORD_LIVE: ${{ env.HF_AIVEN_PASSWORD }}
            HF_AIVEN_PASSWORD: ${{ env.HF_AIVEN_PASSWORD }}
            HF_PROFILES: 'e2e'
            HF_TIER: 'local'
            DOCKER_TAG: ${{env.SHORT_COMMIT_ID}}
            DOCKER_REGISTRY: '489198589229.dkr.ecr.eu-west-1.amazonaws.com/'

      - name: local-down
        if: always()
        run: env=local make local-down
        env:
          DOCKER_TAG: ${{env.SHORT_COMMIT_ID}}
          DOCKER_REGISTRY: '489198589229.dkr.ecr.eu-west-1.amazonaws.com/'
