package com.hellofresh.cif.actualconsumption

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.actualConsumption.schema.Tables
import com.hellofresh.cif.actualConsumption.schema.tables.records.Pick_2LightRecord
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.concurrent.Executors
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class PickToLightRepositoryImplTest {
    private lateinit var dsl: MetricsDSLContext

    @BeforeEach
    fun init() {
        dsl = DSL.using(
            DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                },
        ).withMetrics(SimpleMeterRegistry())
    }

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.PICK_2_LIGHT).execute()
    }

    @Test
    fun `should be able to insert pick to light records with VALID csku values`() {
        val pickToLightRepository = PickToLightRepositoryImpl(dsl)

        val expectedPick2LightRecord = createPick2LightRecord("")

        runBlocking {
            pickToLightRepository.batchInsert(listOf(expectedPick2LightRecord))
        }

        val pick2LightRecords = dsl.selectFrom(Tables.PICK_2_LIGHT).toList()
        assertEquals(1, pick2LightRecords.size)
        val actualPick2LightRecord = pick2LightRecords[0]
        assertEquals(expectedPick2LightRecord.kafkaMessageKey, actualPick2LightRecord.kafkaMessageKey)
        assertEquals(expectedPick2LightRecord.market, actualPick2LightRecord.market)
        assertEquals(expectedPick2LightRecord.dcCode, actualPick2LightRecord.dcCode)
        assertEquals(expectedPick2LightRecord.cskuCode, actualPick2LightRecord.cskuCode)
    }

    private fun createPick2LightRecord(cskuCode: String? = "PTN-11-11313-2") = Pick_2LightRecord(
        "kafkamessage",
        OffsetDateTime.now(UTC),
        10,
        "IT",
        "IT",
        cskuCode,
    )

    companion object {
        private val dataSource = getMigratedDataSource()
    }
}
