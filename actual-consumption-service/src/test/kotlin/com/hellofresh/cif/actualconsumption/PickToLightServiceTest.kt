package com.hellofresh.cif.actualconsumption

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.cif.actualConsumption.schema.tables.records.Pick_2LightRecord
import com.hellofresh.cif.actualconsumption.model.Pick2Light
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition
import org.junit.jupiter.api.Test

private const val PICK2LIGHT_FILE = "pick-2-light-data.json"
private const val PICK2LIGHT_FILE_WITH_EMPTY_CSKU = "pick-2-light-data-with-empty-csku-code.json"
private const val PICK2LIGHT_FILE_WITH_NOT_SUPPORTED_CITY = "pick-2-light-data-with-not-supported-city.json"
private const val IT = "IT"
private const val DACH = "DACH"
private const val GB = "GB"

class PickToLightServiceTest {
    private val mockPickToLightRepository: PickToLightRepository = mockk(relaxed = true)
    private var mockDcConfig = DistributionCenterConfiguration.default()
    private val dcConfigService = DcConfigService(
        SimpleMeterRegistry(),
        repo = {
            listOf(
                mockDcConfig.copy(dcCode = "IT", market = "IT"),
                mockDcConfig.copy(dcCode = "VE", market = "DACH"),
                mockDcConfig.copy(dcCode = "BV", market = "GB"),
            )
        },
    )
    private val pickToLightService = PickToLightService(
        mockPickToLightRepository,
        dcConfigService
    )
    private val objectMapper = ObjectMapper().findAndRegisterModules()

    @Test
    fun `should not process the pick 2 light records containing empty csku codes`() {
        val pick2Light = getPick2Light(PICK2LIGHT_FILE_WITH_EMPTY_CSKU)

        runBlocking {
            pickToLightService.processRecords(
                ConsumerRecords(
                    mapOf(
                        TopicPartition("test", 0) to
                            listOf(
                                ConsumerRecord(
                                    "test", 0, 0,
                                    UUID.randomUUID().toString(), pick2Light
                                )
                            )
                    )
                )
            )
            coVerify(exactly = 0) {
                mockPickToLightRepository.batchInsert(any())
            }
        }
    }

    @Test
    fun `should not process the pick 2 light records containing empty market`() {
        val pick2Light = getPick2Light(PICK2LIGHT_FILE_WITH_NOT_SUPPORTED_CITY)

        runBlocking {
            pickToLightService.processRecords(
                ConsumerRecords(
                    mapOf(
                        TopicPartition("test", 0) to
                            listOf(
                                ConsumerRecord(
                                    "test", 0, 0,
                                    UUID.randomUUID().toString(), pick2Light
                                )
                            )
                    )
                )
            )
            coVerify(exactly = 0) {
                mockPickToLightRepository.batchInsert(any())
            }
        }
    }

    @Test
    fun `should process the pick to light records for all markets`() {
        val pick2Light = getPick2Light()
        val pick2LightUK = pick2Light.copy(city = "Nuneaton")
        val pick2LightGermany = pick2Light.copy(city = "Verden")

        val expectedId = UUID.randomUUID().toString()
        val pickToLightRecords = ConsumerRecords(
            mapOf(
                TopicPartition("test", 0) to
                    listOf(
                        ConsumerRecord("test", 0, 0, expectedId, pick2Light),
                        ConsumerRecord("test", 0, 0, UUID.randomUUID().toString(), pick2LightUK),
                        ConsumerRecord("test", 0, 0, UUID.randomUUID().toString(), pick2LightGermany)
                    )
            )
        )

        val expectedPickToLightRecordForItaly = createPick2lightrecord(expectedId, pick2Light)
        val expectedPickToLightRecordForGermany = createPick2lightrecord(expectedId, pick2Light, DACH, "VE",)
        val expectedPickToLightRecordForGB = createPick2lightrecord(expectedId, pick2Light, GB, "BV",)

        val slot = mutableListOf<List<Pick_2LightRecord>>()
        coEvery { mockPickToLightRepository.batchInsert(capture(slot)) } returns Unit

        runBlocking {
            pickToLightService.processRecords(pickToLightRecords)
        }
        assertEquals(3, slot[0].size)
        assertEquals(expectedPickToLightRecordForItaly.dcCode, slot[0].filter { it.market == IT }[0].dcCode)
        assertEquals(expectedPickToLightRecordForGermany.dcCode, slot[0].filter { it.market == DACH }[0].dcCode)
        assertEquals(expectedPickToLightRecordForGB.dcCode, slot[0].filter { it.market == GB }[0].dcCode)
    }

    private fun createPick2lightrecord(
        expectedId: String,
        pick2Light: Pick2Light,
        market: String = IT,
        dcCode: String = IT,
    ) = Pick_2LightRecord(
        expectedId,
        pick2Light.createdAt.atOffset(UTC),
        pick2Light.data.pickRow.quantity.toShort(),
        market,
        dcCode,
        pick2Light.data.pickRow.cskuCode,
    )

    private fun getPick2Light(fileName: String = PICK2LIGHT_FILE) = objectMapper.readValue(
        getPick2LightFileData(fileName),
        Pick2Light::class.java,
    )

    private fun getPick2LightFileData(fileName: String = PICK2LIGHT_FILE) =
        this::class.java.classLoader.getResourceAsStream(fileName)?.readAllBytes()?.let {
            String(it, Charsets.UTF_8)
        }
}
