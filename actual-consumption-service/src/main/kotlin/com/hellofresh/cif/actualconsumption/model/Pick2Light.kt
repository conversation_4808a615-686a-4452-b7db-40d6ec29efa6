package com.hellofresh.cif.actualconsumption.model

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonFormat.Shape.STRING
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset

@JsonIgnoreProperties(ignoreUnknown = true)
data class Pick2Light(
    val data: Data,
    val city: String,
    @JsonProperty("ts") @JsonFormat(shape = STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    val createdAt: LocalDateTime
) {
    @JsonIgnore val offsetDateTime = OffsetDateTime.of(createdAt, ZoneOffset.UTC)
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class Data(
    @JsonProperty("PickRow") val pickRow: PickRow
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class PickRow(
    @JsonProperty("QtyPicked") val quantity: Int,
    @JsonProperty("IngredientSKU") val cskuCode: String
)
