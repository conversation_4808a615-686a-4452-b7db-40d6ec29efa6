package com.hellofresh.cif.outputdemandforecast

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.calculator.models.inventoryCalculationsTopic
import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategyType.LOG_ERROR_IGNORE
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.kafka.serde.deserializer
import com.hellofresh.cif.lib.kafka.serde.serializer
import com.hellofresh.cif.outputdemandforecast.service.OutputDemandForecastService
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastVal
import io.micrometer.core.instrument.MeterRegistry
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.Partitioner
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.clients.producer.internals.BuiltInPartitioner
import org.apache.kafka.common.Cluster

@Suppress("LongParameterList")
class OutputDemandForecastApp(
    private var meterRegistry: MeterRegistry,
    private var pollConfig: PollConfig,
    private var kafkaConsumerConfiguration: Map<String, String>,
    private var kafkaProducerConfiguration: Map<String, String>,
    private var statsigFeatureFlagClient: StatsigFeatureFlagClient,
    private var dcConfigService: DcConfigService = DcConfigService(meterRegistry),
    private var outputDemandForecastService: OutputDemandForecastService = OutputDemandForecastService(
        meterRegistry,
        createKafkaProducer(kafkaProducerConfiguration),
        statsigFeatureFlagClient = statsigFeatureFlagClient,
        dcConfigService = dcConfigService,
    )
) {
    suspend fun runApp() {
        shutdownNeeded {
            val consumerProcessorConfig = ConsumerProcessorConfig(
                kafkaConsumerConfiguration,
                deserializer<CskuInventoryForecastKey>(),
                deserializer<CskuInventoryForecastVal>(),
                listOf(inventoryCalculationsTopic.name),
            )
            CoroutinesProcessor(
                pollConfig = pollConfig,
                consumerProcessorConfig = consumerProcessorConfig,
                meterRegistry = meterRegistry,
                process = outputDemandForecastService::processRecords,
                handleDeserializationException = DeserializationExceptionStrategy.create(
                    LOG_ERROR_IGNORE,
                    meterRegistry,
                ),
                recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                    meterRegistry,
                    "calculation_processor_write_failure"
                ),
            ).also {
                HealthChecks.add(it)
                StartUpChecks.add(it)
            }
        }.run()
    }
    companion object {
        fun createKafkaProducer(kafkaProducerConfiguration: Map<String, String>) =
            KafkaProducer(
                kafkaProducerConfiguration.toMutableMap()
                    .apply {
                        put(
                            ProducerConfig.PARTITIONER_CLASS_CONFIG,
                            "com.hellofresh.cif.outputdemandforecast.SkuInventoryDemandForecastKeyPartitioner"
                        )
                    }.toMap(),
                serializer<SkuInventoryDemandForecastKey>(),
                serializer<SkuInventoryDemandForecastVal>(),
            )
    }
}

class SkuInventoryDemandForecastKeyPartitioner : Partitioner {
    override fun configure(configs: MutableMap<String, *>?) {
        // Nothing to do
    }

    override fun partition(
        topic: String,
        key: Any,
        keyBytes: ByteArray,
        value: Any?,
        valueBytes: ByteArray?,
        cluster: Cluster
    ) =
        BuiltInPartitioner.partitionForKey(
            (key as SkuInventoryDemandForecastKey).skuId.toByteArray(Charsets.UTF_8),
            cluster.partitionCountForTopic(topic),
        )

    override fun close() {
        // Nothing to do
    }
}
