package com.hellofresh.cif.outputdemandforecast

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.shutdown.shutdownHook
import kotlin.time.Duration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.kafka.clients.consumer.ConsumerConfig

private const val STATUS_SERVER_HTTP_PORT = 8081

suspend fun main() {
    val meterRegistry = createMeterRegistry()
    StatusServer.run(
        meterRegistry,
        STATUS_SERVER_HTTP_PORT,
    )

    val parallelism = ConfigurationLoader.getIntegerOrFail("parallelism")
    val kafkaConsumerConfiguration = ConfigurationLoader.loadKafkaConsumerConfigurations() + mapOf(
        ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false",
    )
    val kafkaProducerConfiguration = ConfigurationLoader.loadKafkaProducerConfigurations()

    withContext(Dispatchers.IO) {
        repeat(parallelism) {
            launch {
                OutputDemandForecastApp(
                    meterRegistry,
                    readPollConfig(),
                    kafkaConsumerConfiguration,
                    kafkaProducerConfiguration,
                    statsigFeatureFlagClient(),
                ).runApp()
            }
        }
    }
}

private fun statsigFeatureFlagClient() = StatsigFactory.build(
    ::shutdownHook,
    sdkKey = ConfigurationLoader.getStringOrFail("HF_STATSIG_SDK_KEY"),
    userId = ConfigurationLoader.getStringOrFail("application.name"),
    isOffline = ConfigurationLoader.getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
    hfTier = ConfigurationLoader.getStringOrFail("HF_TIER"),
)

private fun readPollConfig() = PollConfig(
    Duration.parse(ConfigurationLoader.getStringOrFail("poll.timeout")),
    ConfigurationLoader.getIntegerOrFail("poll.interval_ms").toLong(),
    Duration.parse(ConfigurationLoader.getStringOrFail("process.timeout")),
)
