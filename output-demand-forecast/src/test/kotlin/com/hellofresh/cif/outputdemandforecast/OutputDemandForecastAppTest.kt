package com.hellofresh.cif.outputdemandforecast

import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastKey
import io.mockk.every
import io.mockk.mockk
import java.util.UUID
import org.apache.kafka.common.Cluster
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class OutputDemandForecastAppTest {

    private val testTopicName = "topicTest"

    @Test
    fun `same sku id uses same partition`() {
        val cluster = mockk<Cluster>()

        every { cluster.partitionCountForTopic(testTopicName) } returns 3

        val partitioner = SkuInventoryDemandForecastKeyPartitioner()

        repeat(10) {
            val skuInventoryDemandForecastKey = SkuInventoryDemandForecastKey.newBuilder().setSkuId(
                UUID.randomUUID().toString()
            ).build()

            val partition1 = partitioner.partition(
                testTopicName,
                skuInventoryDemandForecastKey,
                skuInventoryDemandForecastKey.toByteArray(),
                null,
                null,
                cluster,
            )
            val partition2 = partitioner.partition(
                testTopicName,
                skuInventoryDemandForecastKey,
                skuInventoryDemandForecastKey.toByteArray(),
                null,
                null,
                cluster,
            )

            assertEquals(partition1, partition2)
        }
    }

    @Test
    fun `partitioner distributes sku ids in different partitions`() {
        val cluster = mockk<Cluster>()

        every { cluster.partitionCountForTopic(testTopicName) } returns 3

        val partitioner = SkuInventoryDemandForecastKeyPartitioner()

        val partitionMap = mutableMapOf<Int, MutableSet<UUID>>()

        repeat(1000) {
            val skuId = UUID.randomUUID()
            val skuInventoryDemandForecastKey = SkuInventoryDemandForecastKey.newBuilder().setSkuId(
                skuId.toString()
            ).build()
            val partition = partitioner.partition(
                testTopicName,
                skuInventoryDemandForecastKey,
                skuInventoryDemandForecastKey.toByteArray(),
                null,
                null,
                cluster,
            )
            partitionMap.computeIfAbsent(partition) { mutableSetOf() }.add(skuId)
        }

        assertEquals(3, partitionMap.keys.size)
        assertEquals((0..2).toSet(), partitionMap.keys)
    }
}
