package com.hellofresh.cif.outputdemandforecast.service

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.outputdemandforecast.OutputDemandForecastAppIntegrationTest.Companion.assertKey
import com.hellofresh.cif.outputdemandforecast.OutputDemandForecastAppIntegrationTest.Companion.assertValue
import com.hellofresh.cif.outputdemandforecast.TestPrepare
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastVal
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.random.Random
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.common.TopicPartition
import org.junit.jupiter.api.Test
import random

class OutputDemandForecastServiceTest : TestPrepare() {

    private val meterRegistry = SimpleMeterRegistry()
    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())

    @Test
    fun `new records are produced in output topic`() {
        val producerMock = mockk<Producer<SkuInventoryDemandForecastKey, SkuInventoryDemandForecastVal>>(relaxed = true)

        val key = CskuInventoryForecastKey(UUID.randomUUID(), UUID.randomUUID().toString(), LocalDate.now(UTC))
        val value = CskuInventoryForecastVal.Companion.random()

        OutputDemandForecastService(
            meterRegistry,
            producerMock,
            statsigFeatureFlagClient = statsigFeatureFlagClient,
            dcConfigService = DcConfigService(meterRegistry, DcRepositoryImpl(metricsDSLContext)),
        )
            .processRecords(
                ConsumerRecords(
                    mapOf(
                        mockk<TopicPartition>() to listOf(ConsumerRecord("", 0, 0, key, value)),
                    ),
                ),
            )

        verify(exactly = 1) {
            producerMock.send(
                withArg {
                    assertKey(key, it.key())
                    assertValue(value, it.value())
                },
            )
        }
    }

    @Test
    fun `duplicated output records are produced in output topic once`() {
        val producerMock = mockk<Producer<SkuInventoryDemandForecastKey, SkuInventoryDemandForecastVal>>(relaxed = true)

        val key = CskuInventoryForecastKey(UUID.randomUUID(), UUID.randomUUID().toString(), LocalDate.now(UTC))
        val value1 = CskuInventoryForecastVal.Companion.random()
        val value1SameOutput = value1.copy(
            actualInbound = Random.nextDouble(1000.0).toBigDecimal(),
        )
        val value1NewOutput = value1.copy(
            demanded = Random.nextDouble(1000.0).toBigDecimal(),
        )
        val key2 = CskuInventoryForecastKey(UUID.randomUUID(), UUID.randomUUID().toString(), LocalDate.now(UTC))
        val value2 = CskuInventoryForecastVal.Companion.random()

        val records = listOf(
            ConsumerRecord("", 0, 0, key, value1),
            ConsumerRecord("", 0, 0, key, value1),
            ConsumerRecord("", 0, 0, key, value1SameOutput),
            ConsumerRecord("", 0, 0, key, value1SameOutput),
            ConsumerRecord("", 0, 0, key, value1NewOutput),
            ConsumerRecord("", 0, 0, key, value1NewOutput),
            ConsumerRecord("", 0, 0, key2, value2),
            ConsumerRecord("", 0, 0, key2, value2),
        )
        OutputDemandForecastService(
            meterRegistry,
            producerMock,
            statsigFeatureFlagClient = statsigFeatureFlagClient,
            dcConfigService = DcConfigService(meterRegistry, DcRepositoryImpl(metricsDSLContext)),
        )
            .processRecords(ConsumerRecords(records.associate { mockk<TopicPartition>() to listOf(it) }))

        val expectedRecords = listOf(
            key to value1,
            key to value1NewOutput,
            key2 to value2,
        )

        verify(exactly = expectedRecords.size) { producerMock.send(any()) }
        expectedRecords.forEach { (expectedKey, expectedValue) ->
            verify(exactly = 1) {
                producerMock.send(
                    withArg {
                        assertKey(expectedKey, it.key())
                        assertValue(expectedValue, it.value())
                    },
                )
            }
        }
    }
}
