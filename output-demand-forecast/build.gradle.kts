plugins {
    id("com.hellofresh.cif.application-conventions")
}

description = "Process Calculation Topic to produce messages in public topics"
group = "$group.outputDemandForecast"

dependencies {
    api(projects.calculatorModels)
    api(projects.dateUtilModels)
    api(projects.distributionCenterLib)
    api(projects.distributionCenterModels)
    api(libs.jackson.jsr310)
    implementation(projects.lib)
    implementation(projects.lib.db)
    implementation(projects.dateUtilModels)
    implementation(libs.hellofresh.schemaregistry) {
        exclude(group = "com.google.api.grpc", module = "proto-google-common-protos")
    }
    implementation(libs.protobuf.grpc)

    implementation(libs.protobuf.java)
    implementation(libs.caffeine.core)
    implementation(libs.jackson.cbor)

    testImplementation(projects.libTests)
    testImplementation(libs.mockk)
    testImplementation(libs.kafka.clients)
    testImplementation(libs.testcontainers.core)
    testImplementation(libs.testcontainers.kafka)
    testImplementation(libs.testcontainers.junit)
    testImplementation(testFixtures(projects.lib.featureflags))
    testImplementation(testFixtures(projects.calculatorModels))
}
